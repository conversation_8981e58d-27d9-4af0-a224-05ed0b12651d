'use client';

import React, { useState, useEffect } from 'react';
import { Phone, MessageCircle } from 'lucide-react';
import { Input } from '@/components/ui/Input';
import { cn } from '@/utils';

interface ContactInputProps {
  value: {
    phone?: string;
    wechat?: string;
  };
  onChange: (contactInfo: { phone?: string; wechat?: string }) => void;
  petType?: 'breeding' | 'selling' | 'lost' | 'wanted' | '';
  required?: boolean;
  error?: string;
  className?: string;
}

type ContactType = 'phone' | 'wechat';

const ContactInput: React.FC<ContactInputProps> = ({
  value,
  onChange,
  petType = '',
  required = false,
  error,
  className
}) => {
  // 智能默认选择逻辑：微信号优先，如果都有值则选择微信号，如果都没有值也选择微信号
  const getDefaultSelectedType = (): ContactType => {
    // 微信号优先策略
    if (value.wechat) return 'wechat';
    if (value.phone && !value.wechat) return 'phone';
    return 'wechat'; // 默认选择微信号
  };

  const [selectedType, setSelectedType] = useState<ContactType>(getDefaultSelectedType);

  // 监听 value 变化，智能更新选中类型
  useEffect(() => {
    // 只有在当前选中类型没有值时，才考虑自动切换
    if (!value[selectedType]) {
      // 微信号优先策略
      if (value.wechat) {
        setSelectedType('wechat');
      } else if (value.phone) {
        setSelectedType('phone');
      }
    }
    // 如果当前选中类型有值，则保持不变，让用户自由切换
  }, [value, selectedType]);

  // 判断是否需要联系方式（出售、配种、寻回、求购需要）
  const needsContact = ['breeding', 'selling', 'lost', 'wanted'].includes(petType);
  const isRequired = required || needsContact;

  // 联系方式选项
  const contactOptions = [
    {
      type: 'wechat' as ContactType,
      label: '微信号',
      icon: MessageCircle,
      placeholder: '请输入微信号',
      pattern: /^[a-zA-Z][\w-]{5,19}$/,
      errorMessage: '微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号'
    },
    {
      type: 'phone' as ContactType,
      label: '手机号',
      icon: Phone,
      placeholder: '请输入手机号',
      pattern: /^1[3-9]\d{9}$/,
      errorMessage: '请输入正确的手机号格式'
    }
  ];

  // 获取当前选中的联系方式配置
  const currentOption = contactOptions.find(option => option.type === selectedType)!;

  // 获取当前输入值
  const currentValue = value[selectedType] || '';

  // 处理联系方式类型切换 - 确保用户可以自由切换
  const handleTypeChange = (type: ContactType) => {
    // 直接切换类型，不受当前输入内容影响
    setSelectedType(type);
    // 不调用 onChange，因为我们只是切换显示，不修改数据
    // 所有已输入的数据都会保持不变
  };

  // 处理输入值变化 - 确保数据分离存储
  const handleValueChange = (inputValue: string) => {
    // 创建新的联系方式对象，保留所有现有数据
    const newValue = { ...value };

    // 只更新当前选中类型的值，其他类型的值完全不受影响
    if (inputValue.trim()) {
      newValue[selectedType] = inputValue.trim();
    } else {
      // 如果输入为空，删除当前类型的值，但保留其他类型
      delete newValue[selectedType];
    }

    // 调用父组件的 onChange，传递完整的联系方式对象
    onChange(newValue);
  };

  // 验证当前输入值
  const validateCurrentValue = (inputValue: string): string | null => {
    if (!inputValue.trim()) {
      return isRequired ? '请填写联系方式' : null;
    }
    
    if (!currentOption.pattern.test(inputValue.trim())) {
      return currentOption.errorMessage;
    }
    
    return null;
  };

  const validationError = validateCurrentValue(currentValue);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          联系方式 {isRequired && <span className="text-red-500">*</span>}
        </label>
        {needsContact && (
          <span className="text-xs text-gray-500">
            {petType === 'breeding' && '配种需要联系方式'}
            {petType === 'selling' && '出售需要联系方式'}
            {petType === 'lost' && '寻回需要联系方式'}
            {petType === 'wanted' && '求购需要联系方式'}
          </span>
        )}
      </div>

      {/* 联系方式类型选择 */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {contactOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = selectedType === option.type;
          const hasValue = value[option.type] && value[option.type]!.trim().length > 0;

          return (
            <button
              key={option.type}
              type="button"
              onClick={() => handleTypeChange(option.type)}
              className={cn(
                'flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-200',
                'hover:bg-white hover:shadow-sm', // 确保始终有悬停效果
                isSelected
                  ? 'bg-white text-primary-600 shadow-sm ring-1 ring-primary-200'
                  : 'text-gray-600 hover:text-gray-900',
                hasValue && !isSelected && 'text-green-600' // 有数据但未选中时显示绿色
              )}
              // 确保按钮始终可点击
              disabled={false}
            >
              <Icon className="h-4 w-4" />
              <span className="flex items-center space-x-1">
                <span>{option.label}</span>
                {hasValue && (
                  <span className="w-2 h-2 bg-green-500 rounded-full" title="已填写" />
                )}
              </span>
            </button>
          );
        })}
      </div>

      {/* 联系方式输入框 */}
      <Input
        placeholder={currentOption.placeholder}
        value={currentValue}
        onChange={(e) => handleValueChange(e.target.value)}
        leftIcon={<currentOption.icon className="h-4 w-4" />}
        error={validationError || error}
        type={selectedType === 'phone' ? 'tel' : 'text'}
      />

      {/* 数据状态显示（帮助用户了解数据持久化状态） */}
      {(value.wechat || value.phone) && (
        <div className="text-xs bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-blue-800 font-medium mb-1">已保存的联系方式：</p>
          <div className="space-y-1">
            {value.wechat && (
              <p className="text-blue-700">
                • 微信号: {value.wechat}
                {selectedType === 'wechat' && <span className="ml-2 text-blue-500">(当前显示)</span>}
              </p>
            )}
            {value.phone && (
              <p className="text-blue-700">
                • 手机号: {value.phone}
                {selectedType === 'phone' && <span className="ml-2 text-blue-500">(当前显示)</span>}
              </p>
            )}
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="text-xs text-gray-500">
        {!needsContact && (
          <p>• 如需要其他用户联系您，请填写联系方式</p>
        )}
        {needsContact && (
          <p>• 请确保联系方式准确，以便买家/配种方/失主/卖家联系您</p>
        )}
        <p>• 平台不会公开您的联系方式，仅在用户点击联系时显示</p>
        <p>• 您可以同时填写微信号和手机号，用户可选择联系方式</p>
      </div>
    </div>
  );
};

export default ContactInput;
