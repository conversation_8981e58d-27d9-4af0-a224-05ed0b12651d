(()=>{var e={};e.id=731,e.ids=[731],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},10265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),r(34150),r(9457),r(16953),r(35866);var s=r(23191),a=r(88716),l=r(37922),i=r.n(l),n=r(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o=["",{children:["admin",{children:["activities",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34150)),"D:\\web-cloudbase-project\\src\\app\\admin\\activities\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\web-cloudbase-project\\src\\app\\admin\\activities\\page.tsx"],m="/admin/activities/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/activities/page",pathname:"/admin/activities",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79193:(e,t,r)=>{Promise.resolve().then(r.bind(r,87445))},64271:(e,t,r)=>{Promise.resolve().then(r.bind(r,5264))},87445:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(10326),a=r(17577),l=r(41828),i=r(45003),n=r(7710),d=r(8566);let o=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});var c=r(84625),m=r(96229),x=r(41217),u=r(43404);function p(){let[e,t]=(0,a.useState)([]),[r,p]=(0,a.useState)(!0),[b,y]=(0,a.useState)(null),[v,j]=(0,a.useState)(!1),[f,w]=(0,a.useState)(!1),[N,k]=(0,a.useState)("all"),[_,C]=(0,a.useState)("all"),E=async()=>{p(!0);try{let e=await l.petAPI.getActivities({status:"all"===N?void 0:N,type:"all"===_?void 0:_,limit:100,includeArchived:!0});e.success&&t(e.data||[])}catch(e){console.error("加载活动失败:",e)}finally{p(!1)}},S=async e=>{try{(await l.petAPI.updateSystemConfig({config:e})).success&&(y(e),alert("系统配置更新成功！"),j(!1))}catch(e){console.error("更新系统配置失败:",e),alert("更新失败："+(e?.message||"未知错误"))}},D=async e=>{if(confirm("确定要删除这个活动吗？此操作不可恢复。"))try{(await l.petAPI.deleteActivity({activity_id:e})).success&&(alert("活动删除成功！"),await E())}catch(e){console.error("删除活动失败:",e),alert("删除失败："+(e?.message||"未知错误"))}},T=e=>{switch(e){case"CONTEST":default:return i.Z;case"VOTING":return n.Z;case"DISCUSSION":return d.Z}},L=e=>{switch(e){case"CONTEST":return"评选竞赛";case"VOTING":return"投票话题";case"DISCUSSION":return"讨论活动";default:return"未知类型"}},I=e=>{let t={DRAFT:{label:"草稿",color:"bg-yellow-100 text-yellow-800"},ACTIVE:{label:"进行中",color:"bg-green-100 text-green-800"},ENDED:{label:"已结束",color:"bg-blue-100 text-blue-800"},ARCHIVED:{label:"已归档",color:"bg-gray-100 text-gray-800"}},r=t[e]||t.DRAFT;return s.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.color}`,children:r.label})},M=e=>new Date(e).toLocaleDateString("zh-CN");return r?s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"活动管理"}),s.jsx("p",{className:"text-gray-600",children:"管理社区活动和系统配置"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>j(!0),className:"flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[s.jsx(o,{className:"h-4 w-4 mr-2"}),"系统设置"]}),(0,s.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700",children:[s.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"创建活动"]})]})]}),s.jsx("div",{className:`p-4 rounded-lg ${b?.enabled?"bg-green-50 border border-green-200":"bg-yellow-50 border border-yellow-200"}`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:`h-3 w-3 rounded-full mr-3 ${b?.enabled?"bg-green-500":"bg-yellow-500"}`}),(0,s.jsxs)("span",{className:`font-medium ${b?.enabled?"text-green-800":"text-yellow-800"}`,children:["活动系统状态：",b?.enabled?"已启用":"已禁用"]}),!b?.enabled&&s.jsx("span",{className:"ml-2 text-yellow-700",children:"（用户端不显示活动入口）"})]})}),(0,s.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow flex space-x-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态筛选"}),(0,s.jsxs)("select",{value:N,onChange:e=>k(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[s.jsx("option",{value:"all",children:"全部状态"}),s.jsx("option",{value:"DRAFT",children:"草稿"}),s.jsx("option",{value:"ACTIVE",children:"进行中"}),s.jsx("option",{value:"ENDED",children:"已结束"}),s.jsx("option",{value:"ARCHIVED",children:"已归档"})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型筛选"}),(0,s.jsxs)("select",{value:_,onChange:e=>C(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[s.jsx("option",{value:"all",children:"全部类型"}),s.jsx("option",{value:"CONTEST",children:"评选竞赛"}),s.jsx("option",{value:"VOTING",children:"投票话题"}),s.jsx("option",{value:"DISCUSSION",children:"讨论活动"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[s.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:s.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"活动列表"})}),s.jsx("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"活动信息"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"参与数据"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===e.length?s.jsx("tr",{children:s.jsx("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无活动数据"})}):e.map(e=>{let t=T(e.type);return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),s.jsx("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(t,{className:"h-5 w-5 text-gray-400 mr-2"}),s.jsx("span",{className:"text-sm text-gray-900",children:L(e.type)})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:I(e.status)}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,s.jsxs)("div",{children:["开始：",M(e.start_time)]}),(0,s.jsxs)("div",{children:["结束：",M(e.end_time)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["持续 ",e.duration_days," 天"]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,s.jsxs)("div",{children:["投票：",e.statistics_summary?.total_votes||0]}),(0,s.jsxs)("div",{children:["评论：",e.statistics_summary?.total_comments||0]})]}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>window.open(`/activities/${e._id}`,"_blank"),className:"text-blue-600 hover:text-blue-900",title:"查看活动",children:s.jsx(m.Z,{className:"h-4 w-4"})}),s.jsx("button",{onClick:()=>alert("编辑功能开发中"),className:"text-green-600 hover:text-green-900",title:"编辑活动",children:s.jsx(x.Z,{className:"h-4 w-4"})}),s.jsx("button",{onClick:()=>D(e._id),className:"text-red-600 hover:text-red-900",title:"删除活动",children:s.jsx(u.Z,{className:"h-4 w-4"})})]})})]},e._id)})})]})})]}),v&&s.jsx(h,{config:b,onSave:S,onClose:()=>j(!1)}),f&&s.jsx(g,{onSuccess:()=>{w(!1),E()},onClose:()=>w(!1)})]})}let h=({config:e,onSave:t,onClose:r})=>{let[l,i]=(0,a.useState)({enabled:e?.enabled||!1,comments_enabled:e?.comments_enabled||!0,rate_limit_interval:e?.rate_limit_interval||10,max_comment_length:e?.max_comment_length||100,default_result_display_days:e?.default_result_display_days||3});return s.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"系统配置"}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(l)},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:l.enabled,onChange:e=>i({...l,enabled:e.target.checked}),className:"mr-2"}),s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"启用活动系统"})]}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"关闭后用户端将不显示活动入口"})]}),s.jsx("div",{children:(0,s.jsxs)("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:l.comments_enabled,onChange:e=>i({...l,comments_enabled:e.target.checked}),className:"mr-2"}),s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"启用评论功能"})]})}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"评论间隔限制（秒）"}),(0,s.jsxs)("select",{value:l.rate_limit_interval,onChange:e=>i({...l,rate_limit_interval:parseInt(e.target.value)}),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[s.jsx("option",{value:5,children:"5秒"}),s.jsx("option",{value:10,children:"10秒（推荐）"}),s.jsx("option",{value:15,children:"15秒"}),s.jsx("option",{value:20,children:"20秒"})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"评论最大长度"}),s.jsx("input",{type:"number",value:l.max_comment_length,onChange:e=>i({...l,max_comment_length:parseInt(e.target.value)}),min:10,max:500,className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"默认结果展示天数"}),s.jsx("input",{type:"number",value:l.default_result_display_days,onChange:e=>i({...l,default_result_display_days:parseInt(e.target.value)}),min:1,max:30,className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[s.jsx("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"取消"}),s.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700",children:"保存"})]})]})]})})},g=({onSuccess:e,onClose:t})=>{let[r,i]=(0,a.useState)(1),[n,d]=(0,a.useState)(!1),[o,c]=(0,a.useState)({title:"",description:"",type:"VOTING",startTime:"",durationDays:7,resultDisplayDays:3,options:[{id:"option1",title:"",description:""},{id:"option2",title:"",description:""}]}),m=e=>{c({...o,title:e.title,description:e.description,type:e.type,options:e.options||o.options}),i(2)},x=async()=>{d(!0);try{let t=new Date(o.startTime),r=new Date(t.getTime()+864e5*o.durationDays);r.getTime(),o.resultDisplayDays;let s={title:o.title,description:o.description,type:o.type,startTime:o.startTime,endTime:r.toISOString(),durationDays:o.durationDays,resultDisplayDays:o.resultDisplayDays,config:{comments_enabled:!0,comments_after_vote:!0,rate_limit:{interval:10},..."VOTING"===o.type&&{options:o.options.filter(e=>e.title.trim())}}};(await l.petAPI.createActivity(s)).success&&(alert("活动创建成功！"),e())}catch(e){console.error("创建活动失败:",e),alert("创建失败："+(e?.message||"未知错误"))}finally{d(!1)}};return s.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:1===r?"选择活动模板":"完善活动信息"}),s.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsx("div",{className:"p-6",children:1===r?(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("p",{className:"text-gray-600 mb-6",children:"选择一个活动模板快速开始，或者创建自定义活动"}),[{id:"pet_vs_pet",title:"宠物对决",description:"猫咪 VS 狗狗，你更喜欢哪一种？",type:"VOTING",options:[{id:"cats",title:"猫咪更好",description:"独立、优雅、适合忙碌生活"},{id:"dogs",title:"狗狗更好",description:"忠诚、活泼、是最好的伙伴"}]},{id:"cute_contest",title:"最萌宠物评选",description:"展示你的宠物，参与最萌宠物评选活动",type:"CONTEST"},{id:"pet_care_tips",title:"宠物护理讨论",description:"分享宠物护理经验和技巧",type:"DISCUSSION"}].map(e=>s.jsx("div",{onClick:()=>m(e),className:"border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900",children:e.title}),s.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),s.jsx("span",{className:"inline-block mt-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:"VOTING"===e.type?"投票话题":"CONTEST"===e.type?"评选竞赛":"讨论活动"})]}),s.jsx("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},e.id)),(0,s.jsxs)("div",{onClick:()=>i(2),className:"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 cursor-pointer transition-colors text-center",children:[s.jsx("svg",{className:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),s.jsx("h3",{className:"font-medium text-gray-900",children:"自定义活动"}),s.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"从头开始创建活动"})]})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"活动标题 *"}),s.jsx("input",{type:"text",value:o.title,onChange:e=>c({...o,title:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"请输入活动标题",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"活动描述"}),s.jsx("textarea",{value:o.description,onChange:e=>c({...o,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"请输入活动描述"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"开始时间 *"}),s.jsx("input",{type:"datetime-local",value:o.startTime,onChange:e=>c({...o,startTime:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"持续天数"}),s.jsx("input",{type:"number",value:o.durationDays,onChange:e=>c({...o,durationDays:parseInt(e.target.value)}),min:1,max:365,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]})]}),"VOTING"===o.type&&(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"投票选项"}),(0,s.jsxs)("div",{className:"space-y-3",children:[o.options.map((e,t)=>s.jsx("div",{className:"border border-gray-200 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[s.jsx("input",{type:"text",value:e.title,onChange:e=>{let r=[...o.options];r[t].title=e.target.value,c({...o,options:r})},placeholder:`选项 ${t+1} 标题`,className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"}),s.jsx("input",{type:"text",value:e.description,onChange:e=>{let r=[...o.options];r[t].description=e.target.value,c({...o,options:r})},placeholder:`选项 ${t+1} 描述`,className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]})},e.id)),o.options.length<5&&s.jsx("button",{type:"button",onClick:()=>{let e=[...o.options,{id:`option${o.options.length+1}`,title:"",description:""}];c({...o,options:e})},className:"w-full border-2 border-dashed border-gray-300 rounded-lg p-3 text-gray-600 hover:border-gray-400",children:"+ 添加选项"})]})]}),(0,s.jsxs)("div",{className:"flex justify-between pt-4",children:[s.jsx("button",{type:"button",onClick:()=>i(1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"上一步"}),(0,s.jsxs)("div",{className:"space-x-3",children:[s.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"取消"}),s.jsx("button",{onClick:x,disabled:n||!o.title||!o.startTime,className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50",children:n?"创建中...":"创建活动"})]})]})]})})]})})}},5264:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(10326);r(17577);var a=r(20603);function l({children:e}){return(0,s.jsxs)("div",{className:"admin-layout",children:[s.jsx(a.ToastProvider,{}),e]})}r(23824)},34150:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\activities\page.tsx#default`)},9457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},23824:()=>{},7710:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},96229:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},41217:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},84625:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},43404:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},45003:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"}))})},8566:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,946,240],()=>r(10265));module.exports=s})();