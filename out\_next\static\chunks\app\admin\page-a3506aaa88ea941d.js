(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3],{35967:function(e,s,r){Promise.resolve().then(r.bind(r,36683))},66337:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},88906:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},92369:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},99376:function(e,s,r){"use strict";var t=r(35475);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},36683:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return m}});var t=r(57437),n=r(2265),a=r(99376),i=r(56334),o=r(9356),l=r(88906),c=r(92369),d=r(66337),u=r(98011);function m(){let e=(0,a.useRouter)(),[s,r]=(0,n.useState)({username:"",password:""}),[m,x]=(0,n.useState)(!1),[g,h]=(0,n.useState)(!0);if((0,n.useEffect)(()=>{(()=>{try{let s=localStorage.getItem("adminToken"),r=localStorage.getItem("adminUser");if(s&&r){e.push("/admin/dashboard");return}}catch(e){console.error("检查管理员登录状态失败:",e)}h(!1)})()},[e]),g)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(l.Z,{className:"w-16 h-16 text-blue-600 mx-auto mb-4 animate-pulse"}),(0,t.jsx)("p",{className:"text-gray-600",children:"检查登录状态..."})]})});let f=async r=>{r.preventDefault(),x(!0);try{let r=await u.petAPI.adminLogin({username:s.username,password:s.password});r.success?(localStorage.setItem("adminToken",r.data.token),localStorage.setItem("adminUser",JSON.stringify(r.data)),o.C.success("登录成功"),e.push("/admin/dashboard")):o.C.error(r.message||"登录失败")}catch(e){console.error("登录失败:",e),o.C.error(e.message||"登录失败，请重试")}finally{x(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4",children:(0,t.jsx)(l.Z,{className:"w-8 h-8 text-blue-600"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"管理员登录"}),(0,t.jsx)("p",{className:"text-gray-600",children:"请输入管理员账号和密码"})]}),(0,t.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户名"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.Z,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{type:"text",value:s.username,onChange:e=>r(s=>({...s,username:e.target.value})),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入用户名",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"密码"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(d.Z,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{type:"password",value:s.password,onChange:e=>r(s=>({...s,password:e.target.value})),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入密码",required:!0})]})]}),(0,t.jsx)(i.Z,{type:"submit",loading:m,disabled:m,className:"w-full",size:"lg",children:m?"登录中...":"登录"})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"超级管理员"}),(0,t.jsxs)("div",{className:"text-sm text-blue-700",children:[(0,t.jsx)("p",{children:"只有超级管理员可以登录此系统"}),(0,t.jsx)("p",{children:"普通管理员账号由超级管理员创建"})]})]})]}),(0,t.jsx)("div",{className:"text-center mt-6",children:(0,t.jsx)("button",{onClick:()=>e.push("/"),className:"text-gray-600 hover:text-gray-900 text-sm",children:"← 返回首页"})})]})})}},56334:function(e,s,r){"use strict";var t=r(57437),n=r(2265),a=r(68661);let i=n.forwardRef((e,s)=>{let{className:r,variant:n="primary",size:i="md",loading:o=!1,icon:l,children:c,disabled:d,...u}=e;return(0,t.jsxs)("button",{className:(0,a.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[n],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],r),ref:s,disabled:d||o,...u,children:[o&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!o&&l&&(0,t.jsx)("span",{className:"mr-2",children:l}),c]})});i.displayName="Button",s.Z=i},9356:function(e,s,r){"use strict";r.d(s,{C:function(){return m},ToastProvider:function(){return x}});var t=r(57437);r(2265);var n=r(69064),a=r(65302),i=r(45131),o=r(22252),l=r(33245),c=r(68661);let d={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},u=e=>{let{message:s,type:r,onDismiss:n}=e,d={success:(0,t.jsx)(a.Z,{className:"h-5 w-5 text-green-500"}),error:(0,t.jsx)(i.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,t.jsx)(o.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,t.jsx)(l.Z,{className:"h-5 w-5 text-blue-500"})};return(0,t.jsxs)("div",{className:(0,c.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[r]),children:[d[r],(0,t.jsx)("span",{className:"flex-1 text-sm font-medium",children:s}),(0,t.jsx)("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})},m={success:e=>{n.Am.custom(s=>(0,t.jsx)(u,{message:e,type:"success",onDismiss:()=>n.Am.dismiss(s.id)}),d)},error:e=>{n.Am.custom(s=>(0,t.jsx)(u,{message:e,type:"error",onDismiss:()=>n.Am.dismiss(s.id)}),{...d,duration:6e3})},warning:e=>{n.Am.custom(s=>(0,t.jsx)(u,{message:e,type:"warning",onDismiss:()=>n.Am.dismiss(s.id)}),d)},info:e=>{n.Am.custom(s=>(0,t.jsx)(u,{message:e,type:"info",onDismiss:()=>n.Am.dismiss(s.id)}),d)},loading:e=>n.Am.loading(e,{style:d.style,position:d.position}),dismiss:e=>{n.Am.dismiss(e)},promise:(e,s)=>n.Am.promise(e,s,{style:d.style,position:d.position})},x=()=>(0,t.jsx)(n.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},68661:function(e,s,r){"use strict";r.d(s,{cn:function(){return a},uf:function(){return i},vV:function(){return o}});var t=r(61994),n=r(53335);function a(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,n.m6)((0,t.W)(s))}function i(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function o(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}}},function(e){e.O(0,[649,19,347,554,222,11,971,117,744],function(){return e(e.s=35967)}),_N_E=e.O()}]);