'use client';

import { useState, useEffect, useCallback } from 'react';
import { authAPI } from '@/lib/cloudbase';
import { User, LoginState } from '@/types';
import { showToast } from '@/components/ui/Toast';
import { syncDraftsFromCloud } from '@/utils/drafts';

interface UseAuthReturn {
  user: User | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<boolean>;
  updateProfile: (data: Partial<User>) => Promise<boolean>;
  refreshUser: () => Promise<void>;
  refreshLoginState: () => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // 保存用户状态到 localStorage
  const saveUserToStorage = useCallback((userData: User | null) => {
    try {
      if (userData) {
        localStorage.setItem('pet_platform_user', JSON.stringify(userData));
        localStorage.setItem('pet_platform_logged_in', 'true');
      } else {
        localStorage.removeItem('pet_platform_user');
        localStorage.removeItem('pet_platform_logged_in');
      }
    } catch (error) {
      console.error('保存用户状态失败:', error);
    }
  }, []);

  // 从 localStorage 恢复用户状态
  const loadUserFromStorage = useCallback(() => {
    try {
      const savedUser = localStorage.getItem('pet_platform_user');
      const savedLoggedIn = localStorage.getItem('pet_platform_logged_in');

      if (savedUser && savedLoggedIn === 'true') {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setIsLoggedIn(true);
        return userData;
      }
    } catch (error) {
      console.error('恢复用户状态失败:', error);
    }
    return null;
  }, []);

  // 检查登录状态
  const checkLoginState = useCallback(async () => {
    try {
      setIsLoading(true);

      // 先从localStorage恢复状态
      const savedUser = loadUserFromStorage();
      if (savedUser) {
        // 验证用户是否仍然有效
        try {
          // 如果用户有email，则验证用户状态；否则直接使用保存的用户信息
          if (savedUser.email) {
            const result = await authAPI.getCurrentUser(savedUser.email);
            if (result.success && result.data) {
              setUser(result.data);
              setIsLoggedIn(true);
              saveUserToStorage(result.data);
            } else {
              // 用户信息无效，清除本地状态
              setUser(null);
              setIsLoggedIn(false);
              saveUserToStorage(null);
            }
          } else {
            // 匿名用户或没有email的用户，直接使用保存的信息
            console.log('使用本地保存的用户信息（匿名用户）');
            setUser(savedUser);
            setIsLoggedIn(true);
          }
        } catch (error) {
          console.error('验证用户状态失败:', error);
          // 如果验证失败，但用户信息存在，可能是匿名用户，继续使用本地信息
          if (savedUser) {
            console.log('验证失败，使用本地用户信息');
            setUser(savedUser);
            setIsLoggedIn(true);
          } else {
            setUser(null);
            setIsLoggedIn(false);
            saveUserToStorage(null);
          }
        }
      } else {
        // 没有保存的用户信息，尝试匿名登录
        console.log('没有保存的用户信息，尝试匿名登录');
        try {
          const result = await authAPI.getCurrentUser();
          if (result.success && result.data) {
            setUser(result.data);
            setIsLoggedIn(true);
            saveUserToStorage(result.data);
          } else {
            setUser(null);
            setIsLoggedIn(false);
          }
        } catch (error) {
          console.error('匿名登录失败:', error);
          setUser(null);
          setIsLoggedIn(false);
        }
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      setUser(null);
      setIsLoggedIn(false);
    } finally {
      setIsLoading(false);
    }
  }, [loadUserFromStorage, saveUserToStorage]);

  // 登录
  const login = useCallback(async (email?: string, password?: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      if (!email || !password) {
        showToast.error('请输入邮箱和密码');
        return false;
      }

      // 调用邮箱登录API
      const result = await authAPI.loginWithEmail(email, password);

      if (result.success) {
        setUser(result.data);
        setIsLoggedIn(true);
        saveUserToStorage(result.data);

        // 同步云端草稿（自动下架的帖子）
        try {
          await syncDraftsFromCloud(result.data._id);
        } catch (error) {
          console.error('同步草稿失败:', error);
          // 不影响登录流程
        }

        if (result.data.isNewUser) {
          showToast.success('欢迎加入宠物交易平台！');
        } else {
          showToast.success('登录成功！');
        }

        return true;
      } else {
        showToast.error(result.message || '登录失败');
        return false;
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      showToast.error(error.message || '登录失败，请重试');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [saveUserToStorage]);

  // 退出登录
  const logout = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      await authAPI.logout();
      setUser(null);
      setIsLoggedIn(false);
      saveUserToStorage(null);
      showToast.success('已退出登录');
      return true;
    } catch (error: any) {
      console.error('退出登录失败:', error);
      showToast.error('退出登录失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [saveUserToStorage]);

  // 更新用户资料
  const updateProfile = useCallback(async (data: Partial<User>): Promise<boolean> => {
    if (!user) return false;

    try {
      setIsLoading(true);
      // TODO: 实现用户资料更新API
      console.log('更新用户资料:', data);

      // 暂时只更新本地状态
      const updatedUser = { ...user, ...data };
      setUser(updatedUser);
      saveUserToStorage(updatedUser);
      showToast.success('资料更新成功');
      return true;
    } catch (error: any) {
      console.error('更新资料失败:', error);
      showToast.error('更新失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, saveUserToStorage]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    if (!isLoggedIn) return;

    try {
      const result = await authAPI.getCurrentUser();
      if (result.success && result.data) {
        setUser(result.data);
        saveUserToStorage(result.data);
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  }, [isLoggedIn, saveUserToStorage]);

  // 轻量级状态刷新（不触发loading）
  const refreshLoginState = useCallback(async () => {
    try {
      // 从localStorage获取用户邮箱
      const savedUser = loadUserFromStorage();
      const result = await authAPI.getCurrentUser(savedUser?.email);
      if (result.success && result.data) {
        setUser(result.data);
        setIsLoggedIn(true);
        saveUserToStorage(result.data);
      } else {
        setUser(null);
        setIsLoggedIn(false);
        saveUserToStorage(null);
      }
    } catch (error) {
      console.error('刷新登录状态失败:', error);
      setUser(null);
      setIsLoggedIn(false);
      saveUserToStorage(null);
    }
  }, [saveUserToStorage, loadUserFromStorage]);

  // 初始化时检查登录状态
  useEffect(() => {
    const initAuth = async () => {
      // 先从 localStorage 恢复状态
      const savedUser = loadUserFromStorage();
      if (savedUser) {
        // 立即设置用户状态，避免闪烁
        setUser(savedUser);
        setIsLoggedIn(true);
        setIsLoading(false);

        // 在后台验证云端状态，但不立即清除本地状态
        try {
          console.log('后台验证用户状态...');
          const result = await authAPI.getCurrentUser(savedUser.email);
          if (result.success && result.data) {
            // 云端验证成功，更新用户信息
            setUser(result.data);
            saveUserToStorage(result.data);
            console.log('用户状态验证成功');
          } else {
            // 云端验证失败，但给用户一些时间，不立即清除
            console.warn('云端验证失败，但保持本地状态');
            // 可以选择在这里显示一个提示，但不清除登录状态
          }
        } catch (error) {
          console.error('后台验证失败:', error);
          // 网络错误等情况，保持本地状态
        }
      } else {
        // 没有保存的状态，直接检查云端
        await checkLoginState();
      }
    };

    initAuth();
  }, [checkLoginState, loadUserFromStorage, saveUserToStorage]);

  // 监听认证状态变化
  useEffect(() => {
    // 暂时移除监听器，避免类型问题
    // TODO: 后续优化认证状态监听
  }, []);

  return {
    user,
    isLoading,
    isLoggedIn,
    login,
    logout,
    updateProfile,
    refreshUser,
    refreshLoginState,
  };
};
