'use client';

import React from 'react';
import Link from 'next/link';
import { Heart, MapPin, User, X } from 'lucide-react';
import { cn } from '@/utils';
import LazyImage from '@/components/ui/LazyImage';

interface PetCardProps {
  post: any;
  className?: string;
  isDraft?: boolean;
  showRemoveFromFavorites?: boolean;
  onRemoveFromFavorites?: () => void;
}

const PetCard: React.FC<PetCardProps> = ({
  post,
  className,
  isDraft = false,
  showRemoveFromFavorites = false,
  onRemoveFromFavorites
}) => {
  // 获取封面图片
  const coverImage = post.images?.[0] || post.image || 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center';

  // 格式化地址显示
  const formatAddressForDisplay = (address: string) => {
    if (!address) return '未知地区';
    const parts = address.split(/[省市区县]/);
    return parts[parts.length - 2] || address;
  };

  // 获取类型配置
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'selling':
      case 'sell':
        return { label: '出售', color: 'bg-green-500' };
      case 'adoption':
      case 'adopt':
        return { label: '领养', color: 'bg-blue-500' };
      case 'breeding':
      case 'mate':
        return { label: '配种', color: 'bg-orange-500' };
      case 'lost':
        return { label: '寻宠', color: 'bg-red-500' };
      case 'found':
        return { label: '招领', color: 'bg-yellow-500' };
      case 'wanted':
        return { label: '求购', color: 'bg-purple-500' };
      case 'sharing':
        return { label: '分享', color: 'bg-indigo-500' };
      default:
        return null;
    }
  };

  const typeConfig = getTypeConfig(post.type);

  // 卡片内容
  const cardContent = (
    <div className={cn(
      'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 h-full flex flex-col',
      !isDraft && 'cursor-pointer',
      className
    )}>
      {/* 图片区域 */}
      <div className="relative w-full overflow-hidden" style={{ height: '200px' }}>
        <LazyImage
          src={coverImage}
          alt={post.breed || '宠物图片'}
          className="w-full h-full object-cover rounded-t-lg"
          fallback="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"
          quality={80}
          sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"
        />

        {/* 草稿标签 */}
        {isDraft && (
          <div className="absolute top-2 left-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">
            待发布
          </div>
        )}

        {/* 类型标签 */}
        {!isDraft && typeConfig && (
          <div className={cn(
            "absolute top-2 left-2 text-white text-xs px-2 py-1 rounded-full font-medium",
            typeConfig.color
          )}>
            {typeConfig.label}
          </div>
        )}

        {/* 图片数量指示器 */}
        {post.images && post.images.length > 1 && (
          <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full">
            1/{post.images.length}
          </div>
        )}

        {/* 移除收藏按钮 */}
        {showRemoveFromFavorites && (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onRemoveFromFavorites?.();
            }}
            className="absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1.5 rounded-full transition-colors"
            title="从收藏中移除"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>

      {/* 信息区域 */}
      <div className="p-3 flex-1 flex flex-col justify-center" style={{ minHeight: '72px' }}>
        <div className="flex items-center justify-between h-full">
          {/* 左列：品种名称 + 作者昵称 */}
          <div className="flex flex-col justify-center flex-1 min-w-0 pr-2">
            <h3 className="font-medium text-gray-900 text-sm leading-tight truncate mb-1">
              {post.breed || '未知品种'}
            </h3>
            
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              <User className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">
                {post.user?.nickname || post.author?.nickname || '匿名用户'}
              </span>
            </div>
          </div>

          {/* 右列：位置 + 点赞数 */}
          <div className="flex flex-col justify-center items-end flex-shrink-0">
            {/* 发布位置 */}
            {(post.location || post.city) && (
              <div className="flex items-center space-x-1 text-xs text-gray-500 mb-1">
                <MapPin className="h-3 w-3" />
                <span className="truncate max-w-20" title={post.location || `#${post.province}${post.city}`}>
                  {post.city || formatAddressForDisplay(post.location)}
                </span>
              </div>
            )}
            
            {/* 点赞数 */}
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <Heart className="h-3 w-3" />
              <span>{post.likes || 0}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 如果是草稿，直接返回卡片内容，不包装Link
  if (isDraft) {
    return cardContent;
  }

  // 如果不是草稿，用Link包装卡片内容
  return (
    <Link href={`/post/detail?id=${post._id}`}>
      {cardContent}
    </Link>
  );
};

export default PetCard;
