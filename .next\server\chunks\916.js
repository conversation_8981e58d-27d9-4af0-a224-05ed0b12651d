"use strict";exports.id=916,exports.ids=[916],exports.modules={43810:(e,s,t)=>{t.d(s,{Z:()=>l});let l=(0,t(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},924:(e,s,t)=>{t.d(s,{Z:()=>l});let l=(0,t(76557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},58038:(e,s,t)=>{t.d(s,{Z:()=>l});let l=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},67187:(e,s,t)=>{t.d(s,{Z:()=>l});let l=(0,t(76557).Z)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},68197:(e,s,t)=>{t.d(s,{Z:()=>u});var l=t(10326),r=t(17577),a=t(35047),i=t(94019),c=t(67427),n=t(58038),d=t(79635),o=t(67187),x=t(99837),h=t(20603),m=t(41828);function u({isOpen:e,onClose:s,userId:t}){let u=(0,a.useRouter)(),[y,g]=(0,r.useState)("following"),[f,v]=(0,r.useState)([]),[j,b]=(0,r.useState)([]),[p,N]=(0,r.useState)(!1),k=e=>{try{u.push(`/profile/${e}`),s()}catch(e){console.error("导航到用户资料页面失败:",e),h.C.error("无法打开用户资料页面")}},w=async e=>{try{let s=await m.petAPI.toggleFollow({targetUserId:e});s.success?(v(s=>s.map(s=>s._id===e?{...s,isFollowing:!1}:s)),h.C.success("已取消关注")):h.C.error(s.message||"取消关注失败")}catch(e){h.C.error("取消关注失败")}},C=async e=>{try{let s=await m.petAPI.toggleFollow({targetUserId:e});s.success?(v(s=>s.map(s=>s._id===e?{...s,isFollowing:!0}:s)),h.C.success("关注成功")):h.C.error(s.message||"关注失败")}catch(e){h.C.error("关注失败")}},Z=async e=>{try{let s=await m.petAPI.unblockUser({targetUserId:e});s.success?(b(s=>s.filter(s=>s._id!==e)),h.C.success("已取消拉黑")):h.C.error(s.message||"取消拉黑失败")}catch(e){h.C.error("取消拉黑失败")}};return e?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&s()},children:(0,l.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[l.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"following"===y?"我的关注":"黑名单"}),l.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:l.jsx(i.Z,{className:"w-6 h-6"})})]}),l.jsx("div",{className:"border-b border-gray-200 flex-shrink-0",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsxs)("button",{onClick:()=>g("following"),className:`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${"following"===y?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[l.jsx(c.Z,{className:"w-4 h-4"}),l.jsx("span",{children:"我的关注"})]}),(0,l.jsxs)("button",{onClick:()=>g("blocked"),className:`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${"blocked"===y?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[l.jsx(n.Z,{className:"w-4 h-4"}),l.jsx("span",{children:"黑名单"})]})]})}),l.jsx("div",{className:"flex-1 overflow-y-auto",children:p?l.jsx("div",{className:"flex items-center justify-center py-12",children:l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):"following"===y?0===f.length?(0,l.jsxs)("div",{className:"text-center py-12",children:[l.jsx("div",{className:"text-gray-400 mb-4",children:l.jsx(c.Z,{className:"w-16 h-16 mx-auto"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有关注任何人"}),l.jsx("p",{className:"text-gray-600",children:"去发现更多有趣的用户吧！"})]}):l.jsx("div",{className:"divide-y divide-gray-200",children:f.map(e=>l.jsx("div",{className:"p-4 hover:bg-gray-50",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.avatar_url?l.jsx("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):l.jsx("div",{className:"w-full h-full flex items-center justify-center",children:l.jsx(d.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[l.jsx("h3",{className:"text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate transition-colors",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.nickname}),e.bio&&l.jsx("p",{className:"text-sm text-gray-500 truncate",children:e.bio})]}),l.jsx("div",{className:"flex-shrink-0",children:e.isFollowing?l.jsx(x.Z,{variant:"outline",size:"sm",onClick:()=>w(e._id),children:"取消关注"}):l.jsx(x.Z,{size:"sm",onClick:()=>C(e._id),children:"关注"})})]})},e._id))}):0===j.length?(0,l.jsxs)("div",{className:"text-center py-12",children:[l.jsx("div",{className:"text-gray-400 mb-4",children:l.jsx(n.Z,{className:"w-16 h-16 mx-auto"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"黑名单为空"}),l.jsx("p",{className:"text-gray-600",children:"您还没有拉黑任何用户"})]}):l.jsx("div",{className:"divide-y divide-gray-200",children:j.map(e=>l.jsx("div",{className:"p-4 hover:bg-gray-50",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.avatar_url?l.jsx("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):l.jsx("div",{className:"w-full h-full flex items-center justify-center",children:l.jsx(o.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[l.jsx("h3",{className:"text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate transition-colors",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.nickname}),(0,l.jsxs)("p",{className:"text-sm text-gray-500 truncate",children:["拉黑时间：",new Date(e.blockedAt).toLocaleDateString()]})]}),l.jsx("div",{className:"flex-shrink-0",children:l.jsx(x.Z,{variant:"outline",size:"sm",onClick:()=>Z(e._id),className:"text-red-600 border-red-200 hover:bg-red-50",children:"取消拉黑"})})]})},e._id))})})]})}):null}}};