(()=>{var e={};e.id=178,e.ids=[178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},10743:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o}),s(85282),s(16953),s(35866);var a=s(23191),r=s(88716),i=s(37922),l=s.n(i),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85282)),"D:\\web-cloudbase-project\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\profile\\page.tsx"],m="/profile/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},21437:(e,t,s)=>{Promise.resolve().then(s.bind(s,82538))},51896:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},70003:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},82538:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U});var a=s(10326),r=s(35047),i=s(17577),l=s(74131),n=s(94019),c=s(70003),o=s(86333),d=s(79635),m=s(51896),x=s(43810),u=s(76557);let h=(0,u.Z)("PenTool",[["path",{d:"m12 19 7-7 3 3-7 7-3-3z",key:"rklqx2"}],["path",{d:"m18 13-1.5-7.5L2 2l3.5 14.5L13 18l5-5z",key:"1et58u"}],["path",{d:"m2 2 7.586 7.586",key:"etlp93"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var g=s(88378),p=s(39730),f=s(67427);let y=(0,u.Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var j=s(924),b=s(48998),v=s(33734),w=s(12714),N=s(99837),C=s(20603),k=s(41828);let _=e=>e.startsWith("cloud://")||!e.startsWith("http")&&!e.startsWith("/"),Z=({fileId:e,alt:t="",className:s="",width:r,height:l,style:n,onClick:c})=>{let[o,d]=(0,i.useState)(""),[m,x]=(0,i.useState)(!0),[u,h]=(0,i.useState)(!1),[g,p]=(0,i.useState)(0);return((0,i.useEffect)(()=>{(async()=>{if(!e){h(!0),x(!1);return}if(e.startsWith("http")){d(e),x(!1);return}try{x(!0),h(!1),p(0);let t=await (0,k.getImageUrl)(e);d(t)}catch(e){console.error("加载图片失败:",e),h(!0)}finally{x(!1)}})()},[e]),m)?a.jsx("div",{className:`bg-gray-200 animate-pulse flex items-center justify-center ${s}`,style:{width:r,height:l,...n},children:a.jsx("div",{className:"text-gray-400 text-sm",children:"加载中..."})}):u||!o?a.jsx("div",{className:`bg-gray-200 flex items-center justify-center ${s}`,style:{width:r,height:l,...n},children:a.jsx("div",{className:"text-gray-400 text-sm",children:"图片加载失败"})}):a.jsx("img",{src:o,alt:t,className:s,width:r,height:l,style:n,onClick:c,onError:async()=>{if(console.log("图片加载失败，尝试刷新URL，重试次数:",g),g<2){if(p(e=>e+1),_(e))try{let t=await k.petAPI.getImage({fileId:e});t.success&&t.data?.url?(console.log("获取新的图片URL:",t.data.url),d(t.data.url)):(console.error("刷新图片URL失败:",t.message),h(!0))}catch(e){console.error("刷新图片URL异常:",e),h(!0)}else console.log("普通URL加载失败，无法刷新"),h(!0)}else h(!0)}})};var S=s(90434),P=s(8040);function O({isOpen:e,onClose:t,currentBio:s,onSave:r}){let[l,c]=(0,i.useState)(""),[o,d]=(0,i.useState)(!1),m=async()=>{if(l.length>200){C.C.error("个人简介不能超过200字");return}d(!0);try{await r(l),C.C.success("个人简介更新成功"),t()}catch(e){C.C.error("更新失败，请重试")}finally{d(!1)}};return e?a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"编辑个人简介"}),a.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(n.Z,{className:"w-6 h-6"})})]}),a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),a.jsx("textarea",{value:l,onChange:e=>c(e.target.value),placeholder:"介绍一下自己吧...",rows:4,maxLength:200,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}),a.jsx("div",{className:"flex justify-between items-center mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[l.length,"/200"]})})]})}),(0,a.jsxs)("div",{className:"flex space-x-3 p-6 border-t",children:[a.jsx(N.Z,{variant:"outline",onClick:t,className:"flex-1",children:"取消"}),a.jsx(N.Z,{onClick:m,loading:o,className:"flex-1",children:"保存"})]})]})}):null}var I=s(68197),D=s(88673),E=s(13570),L=s(22502),$=s(98091),R=s(37202);let A=({isOpen:e,onClose:t,onConfirm:s,title:r,message:l,confirmText:c="确定",cancelText:o="取消",type:d="danger",loading:m=!1})=>((0,i.useEffect)(()=>{let a=a=>{e&&("Escape"===a.key?t():"Enter"!==a.key||m||s())};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[e,t,s,m]),(0,i.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),e)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",role:"dialog","aria-modal":"true","aria-labelledby":"confirm-modal-title","aria-describedby":"confirm-modal-description",children:[a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t,"aria-hidden":"true"}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:[a.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors","aria-label":"关闭对话框",children:a.jsx(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(()=>{switch(d){case"danger":return a.jsx($.Z,{className:"w-6 h-6 text-red-600"});case"warning":return a.jsx(R.Z,{className:"w-6 h-6 text-yellow-600"});default:return a.jsx(R.Z,{className:"w-6 h-6 text-blue-600"})}})(),a.jsx("h3",{id:"confirm-modal-title",className:"text-lg font-medium text-gray-900",children:r||"确认操作"})]}),a.jsx("p",{id:"confirm-modal-description",className:"text-gray-600 mb-6 leading-relaxed",children:l}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-end",children:[a.jsx(N.Z,{variant:"outline",onClick:t,disabled:m,className:"px-4 py-2",children:o}),a.jsx(N.Z,{variant:(()=>{switch(d){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:s,loading:m,disabled:m,className:"px-4 py-2",children:c})]})]})]})]}):null,M=({isOpen:e,onClose:t,items:s,position:r,className:l=""})=>{let n=(0,i.useRef)(null);if((0,i.useEffect)(()=>{let s=e=>{n.current&&!n.current.contains(e.target)&&t()};return e&&(document.addEventListener("mousedown",s),document.addEventListener("touchstart",s)),()=>{document.removeEventListener("mousedown",s),document.removeEventListener("touchstart",s)}},[e,t]),(0,i.useEffect)(()=>{let s=s=>{e&&"Escape"===s.key&&t()};return document.addEventListener("keydown",s),()=>document.removeEventListener("keydown",s)},[e,t]),(0,i.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),!e)return null;let c=e=>{e.onClick(),t()};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-30",onClick:t,"aria-hidden":"true"}),a.jsx("div",{ref:n,className:`
          fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200
          min-w-48 py-3 mx-4 transform transition-all duration-200 ease-out
          ${r?"":"bottom-6 left-0 right-0 md:bottom-auto md:left-auto md:right-auto md:mx-0"}
          ${e?"scale-100 opacity-100":"scale-95 opacity-0"}
          ${l}
        `,style:r?{left:r.x,top:r.y,transform:"translate(-50%, -100%)"}:void 0,role:"menu","aria-orientation":"vertical",children:s.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>c(e),className:`
              w-full px-5 py-4 text-left flex items-center space-x-3
              transition-all duration-150 font-medium text-base
              ${"danger"===e.variant?"text-red-600 hover:bg-red-50 active:bg-red-100 hover:scale-[1.02]":"text-gray-700 hover:bg-gray-50 active:bg-gray-100 hover:scale-[1.02]"}
              ${0===t?"rounded-t-xl":""}
              ${t===s.length-1?"rounded-b-xl":""}
              ${t>0?"border-t border-gray-100":""}
            `,role:"menuitem",tabIndex:0,children:[e.icon&&a.jsx("span",{className:"flex-shrink-0 w-5 h-5 flex items-center justify-center",children:e.icon}),a.jsx("span",{className:"flex-1",children:e.label})]},e.id))})]})},q=({onLongPress:e,onPress:t,delay:s=500,shouldPreventDefault:a=!0})=>{let[r,l]=(0,i.useState)(!1),n=(0,i.useRef)(),c=(0,i.useRef)(),o=(0,i.useCallback)(t=>{a&&t.target&&(t.target.addEventListener("touchend",m,{passive:!1}),c.current=t.target),l(!1),n.current=setTimeout(()=>{l(!0),navigator.vibrate&&navigator.vibrate([50,30,50]),e()},s)},[e,s,a]),d=(0,i.useCallback)((e,s=!0)=>{n.current&&clearTimeout(n.current),s&&!r&&t&&t(),l(!1),a&&c.current&&c.current.removeEventListener("touchend",m)},[a,r,t]),m=e=>{e.touches&&e.touches.length<2&&e.preventDefault&&e.preventDefault()};return{onMouseDown:e=>o(e),onMouseUp:e=>d(e),onMouseLeave:e=>d(e,!1),onTouchStart:e=>o(e),onTouchEnd:e=>d(e),onTouchMove:e=>{e.touches[0]&&d(e,!1)},isLongPressing:r}},z=({post:e,onLongPress:t,onDelete:s,onEdit:r,type:i,isDraft:l=!1,onClick:c})=>{let o=q({onLongPress:t,onPress:c,delay:500});return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{...o,className:`relative transition-all duration-200 ${o.isLongPressing?"scale-95 opacity-80 ring-2 ring-blue-500 ring-opacity-50":""} ${c?"cursor-pointer":""}`,children:[a.jsx(L.Z,{post:e,isDraft:l}),o.isLongPressing&&a.jsx("div",{className:"absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center md:hidden",children:a.jsx("div",{className:"bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm font-medium text-blue-600",children:"松开显示选项"})})]}),a.jsx("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),s()},className:"absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors shadow-lg z-10 md:flex hidden",title:(()=>{switch(i){case"published":return"下架宝贝并移至待发布";case"draft":return"删除草稿";case"history":return"从浏览历史中移除";default:return"删除"}})(),children:a.jsx(n.Z,{className:"w-3 h-3"})})]})};function U(){let e=(0,r.useRouter)();(0,r.useSearchParams)().get("id");let{user:t,isLoggedIn:u,isLoading:_}=(0,l.a)(),[$,R]=(0,i.useState)(null),[q,U]=(0,i.useState)(!0),[T,F]=(0,i.useState)(!1),[W,G]=(0,i.useState)("posts"),[H,J]=(0,i.useState)(!1),[B,V]=(0,i.useState)(!1),[X,K]=(0,i.useState)(!1),[Q,Y]=(0,i.useState)([]),[ee,et]=(0,i.useState)([]),[es,ea]=(0,i.useState)([]),[er,ei]=(0,i.useState)(!1),[el,en]=(0,i.useState)(!1),[ec,eo]=(0,i.useState)(!1),[ed,em]=(0,i.useState)(""),[ex,eu]=(0,i.useState)([]),[eh,eg]=(0,i.useState)({isOpen:!1,message:"",onConfirm:()=>{}}),[ep,ef]=(0,i.useState)({isOpen:!1,postId:"",postType:"published"}),ey=e=>{try{console.warn("不在客户端环境，无法跳转");return}catch(e){console.error("跳转到发布页面失败:",e),C.C.error("跳转失败，请重试")}},ej=async e=>{eg({isOpen:!0,title:"确认下架",message:"下架这个宝贝并移动到待发布吗？",onConfirm:()=>eb(e)})},eb=async e=>{eg(e=>({...e,loading:!0}));try{C.C.loading("正在下架宝贝...");let t=await k.petAPI.getPostDetail({postId:e});if(!t.success){C.C.dismiss(),C.C.error("获取帖子信息失败");return}let s=t.data,a=await k.petAPI.deletePost({postId:e});if(!a||!a.success){C.C.dismiss(),C.C.error("下架失败："+(a?.message||"服务器删除失败"));return}console.log("服务器删除成功，开始保存草稿");let r=[];if(s.images&&Array.isArray(s.images))for(let e of s.images)try{if("string"==typeof e&&e.startsWith("http")){let t=await fetch(e),s=await t.blob(),a=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});r.push(a),console.log("成功转换图片URL为base64:",e.substring(0,50)+"...")}else r.push(e)}catch(t){console.error("转换图片失败:",e,t),r.push(e)}let i={id:`archived_${e}_${Date.now()}`,title:s.breed||"",breed:s.breed||"",description:s.description||"",images:r,category:s.category||"",location:s.location||"",contact_info:s.contact_info||{},type:s.type||"selling",price:s.price||"",age:s.age||"",gender:s.gender||"",vaccination:s.vaccination||!1,health_certificate:s.health_certificate||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),isArchived:!0,originalPostId:e},l=(0,D.Oe)(),n=[i,...l];localStorage.setItem("petDrafts",JSON.stringify(n)),console.log("草稿保存成功，包含",r.length,"张图片"),ea(t=>t.filter(t=>t._id!==e)),"drafts"===W&&Y(n),C.C.dismiss(),C.C.success("宝贝已下架并移至待发布")}catch(e){console.error("下架宝贝失败:",e),C.C.dismiss(),C.C.error(e.message||"下架失败，请重试")}finally{eg(e=>({...e,isOpen:!1,loading:!1}))}},ev=async e=>{try{let t=(0,D.Oe)().find(t=>t.id===e);if(!t){C.C.error("草稿不存在");return}let s=t.isArchived?"确定要删除这个归档的宝贝吗？删除后无法恢复。":"确定要删除这个草稿吗？";eg({isOpen:!0,title:"确认删除",message:s,onConfirm:()=>ew(e)})}catch(e){console.error("删除草稿失败:",e),C.C.error("删除失败，请重试")}},ew=async e=>{try{let t=(0,D.Oe)(),s=t.find(t=>t.id===e),a=t.filter(t=>t.id!==e);localStorage.setItem("petDrafts",JSON.stringify(a)),Y(a);let r=s?.isArchived?"归档宝贝已删除":"草稿已删除";C.C.success(r),eg(e=>({...e,isOpen:!1}))}catch(e){console.error("删除草稿失败:",e),C.C.error("删除失败，请重试"),eg(e=>({...e,isOpen:!1}))}},eN=e=>{try{E.KX.removeRecord(e),eu(t=>t.filter(t=>t.postId!==e)),C.C.success("已从浏览历史中移除")}catch(e){console.error("删除历史记录失败:",e),C.C.error("操作失败，请重试")}},eC=(e,t)=>{ef({isOpen:!0,postId:e,postType:t})},ek=async()=>{if($)try{await navigator.clipboard.writeText($._id),C.C.success("用户ID已复制到剪贴板")}catch(t){let e=document.createElement("textarea");e.value=$._id,document.body.appendChild(e),e.select();try{document.execCommand("copy"),C.C.success("用户ID已复制到剪贴板")}catch(e){C.C.error("复制失败，请手动复制")}document.body.removeChild(e)}},e_=async e=>{try{let s=await k.petAPI.updateProfile(e);if(s.success)C.C.success("资料更新成功"),$&&R({...$,nickname:e.nickname||$.nickname,bio:e.bio||$.bio,contact_info:e.contactInfo||$.contact_info,address:e.address||$.address}),e.contactInfo&&t&&localStorage.setItem(`contact_${t._id}`,JSON.stringify(e.contactInfo)),void 0!==e.address&&t&&localStorage.setItem(`address_${t._id}`,e.address);else throw Error(s.message||"更新失败")}catch(e){throw C.C.error(e.message||"更新失败，请重试"),e}},eZ=async e=>{let a=e.target.files?.[0];if(a){if(!a.type.startsWith("image/")){em("请选择图片文件");return}if(a.size>5242880){em("图片大小不能超过5MB");return}eo(!0),em("");try{let{uploadFile:e}=await Promise.resolve().then(s.bind(s,41828)),r=document.createElement("canvas"),i=r.getContext("2d"),l=new Image,n=await new Promise((e,t)=>{l.onload=()=>{let{width:s,height:n}=l;s>n?s>300&&(n=300*n/s,s=300):n>300&&(s=300*s/n,n=300),r.width=s,r.height=n,i?.drawImage(l,0,0,s,n),r.toBlob(s=>{if(s){let t=new File([s],a.name,{type:"image/jpeg",lastModified:Date.now()});e(t)}else t(Error("压缩失败"))},"image/jpeg",.9)},l.onerror=()=>t(Error("图片加载失败")),l.src=URL.createObjectURL(a)});console.log(`头像压缩: ${a.size} -> ${n.size} (${Math.round((1-n.size/a.size)*100)}% 减少)`);let{petAPI:c}=await Promise.resolve().then(s.bind(s,41828)),o=await new Promise((e,t)=>{let s=new FileReader;s.onload=()=>{let t=s.result.split(",")[1];e(t)},s.onerror=t,s.readAsDataURL(n)}),d=Date.now(),m=Math.random().toString(36).substring(2,8),x=`avatar_${d}_${m}.jpg`,u=await c.uploadToStatic({fileName:x,fileData:o,contentType:"image/jpeg"});if(!u.success)throw Error(u.message||"头像上传失败");let h=u.data.url,g=await c.updateAvatar({avatarUrl:h});if(g.success){if(R(e=>e?{...e,avatar_url:h}:null),t)try{let e={...t,avatar_url:h};localStorage.setItem("pet_platform_user",JSON.stringify(e))}catch(e){console.error("保存头像到本地存储失败:",e)}C.C.success("您的头像已经更改完成，三十天内只能修改一次头像")}else console.error("头像更新失败:",g),em(g.message||"头像更新失败")}catch(e){console.error("头像上传失败:",e),em("头像上传失败，请重试: "+(e instanceof Error?e.message:String(e)))}finally{eo(!1)}}},eS=async e=>{try{$&&R({...$,bio:e||"这个人很懒，什么都没有留下..."})}catch(e){throw e}},eP=t=>{e.push(`/upload?draftId=${t}`)};return q?a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"加载中..."})]})}):$?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[a.jsx("button",{onClick:()=>e.push("/"),className:"mb-6 p-2 hover:bg-gray-100 rounded-full transition-colors",children:a.jsx(o.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-24 h-24 md:w-32 md:h-32 rounded-full bg-gray-200 overflow-hidden",children:[$.avatar_url?a.jsx(Z,{fileId:$.avatar_url,alt:$.nickname,className:"w-full h-full object-cover"}):a.jsx("div",{className:"w-full h-full flex items-center justify-center",children:a.jsx(d.Z,{className:"w-12 h-12 md:w-16 md:h-16 text-gray-400"})}),ec&&a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-full",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})})]}),T&&(0,a.jsxs)("div",{className:"absolute bottom-0 right-0",children:[a.jsx("input",{type:"file",accept:"image/*",onChange:eZ,className:"hidden",id:"avatar-upload",disabled:ec}),a.jsx("label",{htmlFor:"avatar-upload",className:`bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors cursor-pointer block ${ec?"opacity-50 cursor-not-allowed":""}`,children:a.jsx(m.Z,{className:"w-4 h-4"})})]}),ed&&a.jsx("div",{className:"absolute top-full left-0 mt-2 text-red-500 text-sm",children:ed})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:$.nickname}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2 text-gray-600",children:[a.jsx(d.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{className:"text-sm",children:["ID: ",$._id]}),a.jsx("button",{onClick:ek,className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"复制用户ID",children:a.jsx(x.Z,{className:"w-4 h-4 text-gray-500 hover:text-blue-600"})})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center space-x-2",children:[a.jsx("p",{className:"text-gray-700",children:$.bio||"这个人很懒，什么都没有留下..."}),T&&a.jsx("button",{onClick:()=>V(!0),className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(h,{className:"w-4 h-4"})})]})]}),a.jsx("div",{className:"flex space-x-3 mt-4 md:mt-0",children:T?a.jsx(N.Z,{variant:"outline",icon:a.jsx(g.Z,{className:"w-4 h-4"}),onClick:()=>J(!0),children:"个人设置"}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(N.Z,{icon:a.jsx(p.Z,{className:"w-4 h-4"}),children:"私信"}),a.jsx(N.Z,{variant:"outline",icon:a.jsx(f.Z,{className:"w-4 h-4"}),children:"关注"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-xl font-bold text-gray-900",children:$.likes_count}),a.jsx("div",{className:"text-sm text-gray-600",children:"获赞"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-xl font-bold text-gray-900",children:$.followers_count}),a.jsx("div",{className:"text-sm text-gray-600",children:"粉丝"})]}),(0,a.jsxs)("div",{className:"text-center cursor-pointer",onClick:()=>T&&K(!0),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx("span",{className:"text-xl font-bold text-gray-900",children:$.following_count}),T&&a.jsx(y,{className:"w-4 h-4 text-gray-400"})]}),a.jsx("div",{className:"text-sm text-gray-600",children:"关注"})]})]})]})]})]})}),a.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[T?a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>G("posts"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"posts"===W?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(j.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"已发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>G("drafts"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"drafts"===W?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(b.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"待发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>G("wants"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"wants"===W?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(v.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"评分"})]}),(0,a.jsxs)("button",{onClick:()=>G("history"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"history"===W?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(w.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"浏览历史"})]})]})}):a.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:`${$.nickname}的发布`}),T&&a.jsx("div",{className:"mb-4 md:hidden",children:a.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:a.jsx("p",{className:"text-sm text-blue-700",children:"\uD83D\uDCA1 提示：长按卡片可显示操作选项"})})}),"posts"===W&&(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[a.jsx(S.default,{href:"/upload",children:a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer",children:a.jsx("div",{className:"aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(m.Z,{className:"w-12 h-12 text-blue-500 mx-auto mb-3"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"发布宝贝"}),a.jsx("p",{className:"text-sm text-gray-600",children:"分享您的宠物"})]})})})}),el?a.jsx("div",{className:"col-span-full text-center py-8 text-gray-500",children:a.jsx("p",{children:"加载中..."})}):0===es.length?a.jsx("div",{className:"col-span-full text-center py-8 text-gray-500",children:a.jsx("p",{children:"还没有发布任何内容"})}):es.map(e=>a.jsx(z,{post:e,onLongPress:()=>eC(e._id,"published"),onDelete:()=>ej(e._id),type:"published"},e._id))]}),"drafts"===W&&(0,a.jsxs)(a.Fragment,{children:[Q.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"待发布宝贝"}),a.jsx("button",{onClick:()=>{eg({isOpen:!0,title:"确认清空",message:"确定要清空所有待发布的宝贝吗？此操作不可恢复。",onConfirm:()=>{localStorage.removeItem("petDrafts"),Y([]),C.C.success("所有待发布宝贝已清空"),eg(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"全部清空"})]}),0===Q.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(b.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有待发布的内容"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"在发布页面保存草稿后会显示在这里"}),a.jsx(S.default,{href:"/upload",children:a.jsx(N.Z,{icon:a.jsx(m.Z,{className:"w-4 h-4"}),children:"创建内容"})})]}):a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Q.map(e=>{let t=e.images?.map(e=>"string"==typeof e?e:(console.warn("草稿中发现非字符串图片数据，使用默认图片:",e),"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"))||[],s={_id:e.id,breed:e.breed||"未命名草稿",description:e.description||"",images:t,author:{_id:"draft",nickname:"草稿"},location:e.location||"",likes_count:0,created_at:e.updated_at||e.created_at,type:e.type||"selling",gender:e.gender};return a.jsx("div",{className:"relative",children:a.jsx(z,{post:s,onLongPress:()=>eC(e.id,"draft"),onDelete:()=>ev(e.id),onClick:()=>ey(e.id),type:"draft",isDraft:!0})},e.id)})})]}),"wants"===W&&a.jsx(a.Fragment,{children:er?a.jsx("div",{className:"text-center py-12",children:a.jsx("div",{className:"text-gray-400",children:"加载中..."})}):0===ee.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(v.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有评分任何内容"}),a.jsx("p",{className:"text-gray-600",children:"给喜欢的宠物评分后会显示在这里"})]}):a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:ee.map(e=>a.jsx(L.Z,{post:e},e._id))})}),"history"===W&&(0,a.jsxs)("div",{children:[ex.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["浏览记录保留3天，共",ex.length,"条记录"]}),a.jsx("button",{onClick:()=>{eg({isOpen:!0,title:"确认清空",message:"确定要清空所有浏览历史吗？",onConfirm:()=>{E.KX.clearHistory(),eu([]),C.C.success("浏览历史已清空"),eg(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"清空历史"})]}),0===ex.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(w.Z,{className:"w-16 h-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有浏览历史"}),a.jsx("p",{className:"text-gray-600",children:"浏览过的内容会显示在这里，记录保留3天"})]}):a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:ex.map(e=>{let t={_id:e.postId,breed:e.title,description:"",images:e.image?[e.image]:[],author:{_id:"history",nickname:e.author||"匿名用户"},location:"",likes_count:0,created_at:new Date(e.timestamp).toISOString()};return a.jsx(z,{post:t,onLongPress:()=>eC(e.postId,"history"),onDelete:()=>eN(e.postId),type:"history"},e.postId)})})]})]})}),a.jsx(P.Z,{isOpen:H,onClose:()=>J(!1),currentUser:t,onUpdate:e_}),a.jsx(O,{isOpen:B,onClose:()=>V(!1),currentBio:$?.bio||"",onSave:eS}),a.jsx(I.Z,{isOpen:X,onClose:()=>K(!1),userId:t?._id||""}),a.jsx(A,{isOpen:eh.isOpen,onClose:()=>eg(e=>({...e,isOpen:!1})),onConfirm:eh.onConfirm,title:eh.title,message:eh.message,loading:eh.loading}),a.jsx(M,{isOpen:ep.isOpen,onClose:()=>ef(e=>({...e,isOpen:!1})),items:(()=>{let{postId:e,postType:t}=ep;switch(t){case"published":return[{id:"delete",label:"下架宝贝",icon:a.jsx(n.Z,{className:"w-4 h-4"}),onClick:()=>{ef(e=>({...e,isOpen:!1})),ej(e)},variant:"danger"}];case"draft":return[{id:"edit",label:"编辑草稿",icon:a.jsx(c.Z,{className:"w-4 h-4"}),onClick:()=>{ef(e=>({...e,isOpen:!1})),eP(e)}},{id:"delete",label:"删除草稿",icon:a.jsx(n.Z,{className:"w-4 h-4"}),onClick:()=>{ef(e=>({...e,isOpen:!1})),ev(e)},variant:"danger"}];case"history":return[{id:"delete",label:"从历史中移除",icon:a.jsx(n.Z,{className:"w-4 h-4"}),onClick:()=>{ef(e=>({...e,isOpen:!1})),eN(e)},variant:"danger"}];default:return[]}})(),position:ep.position})]}):a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"抱歉，找不到该用户的信息"}),a.jsx(S.default,{href:"/",children:a.jsx(N.Z,{children:"返回首页"})})]})})}},85282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\profile\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,946,240,535,162,916],()=>s(10743));module.exports=a})();