'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { petAPI, initCloudBase } from '@/lib/cloudbase';
import { showToast } from '@/components/ui/Toast';
import Button from '@/components/ui/Button';
import {
  Shield,
  Flag,
  MessageSquare,
  Users,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Eye,
  Plus,
  Edit,
  AlertTriangle,
  Trash2,
  Settings,
  BarChart3,
  UserPlus,
  Key,
  Activity,
  Database,
  Globe,
  Image,
  Search,
  User,
  Save,
  RefreshCw,
  X,
  Crown
} from 'lucide-react';

interface Appeal {
  _id: string;
  report_id: string;
  appellant_id: string;
  reason: string;
  type: 'post' | 'user';
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  admin_id?: string;
  admin_reason?: string;
  handled_at?: string;
}

interface Admin {
  _id: string;
  username: string;
  role: string;
  level: number;
  permissions: string[];
  status: 'active' | 'suspended' | 'disabled';
  created_at: string;
  created_by: string;
  last_login?: string;
  is_system_account: boolean;
  can_create_admin: boolean;
  can_delete_admin: boolean;
  max_admin_level: number;
}

interface DashboardStats {
  totalUsers: number;
  totalPosts: number;
  totalReports: number;
  totalAppeals: number;
  yesterdayNewUsers: number;
  yesterdayNewPosts: number;
  activeUsers: number;
  onlineUsers: number;
}

export default function AdminDashboard() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'admins' | 'appeals' | 'vip' | 'reports' | 'users' | 'ads' | 'posts' | 'activities' | 'settings'>('dashboard');
  const [currentAdmin, setCurrentAdmin] = useState<Admin | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [selectedAppeal, setSelectedAppeal] = useState<Appeal | null>(null);
  const [adminReason, setAdminReason] = useState('');
  const [showProcessModal, setShowProcessModal] = useState(false);
  const [processAction, setProcessAction] = useState<'approved' | 'rejected'>('approved');
  const [showCreateAdminModal, setShowCreateAdminModal] = useState(false);
  const [newAdminData, setNewAdminData] = useState({
    username: '',
    password: '',
    role: 'admin',
    level: 1,
    permissions: [] as string[]
  });

  // 帖子管理相关状态
  const [posts, setPosts] = useState<any[]>([]);
  const [postsLoading, setPostsLoading] = useState(false);
  const [showDeletePostModal, setShowDeletePostModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<any>(null);
  const [deleteReason, setDeleteReason] = useState('');
  const [postSearchUserId, setPostSearchUserId] = useState('');
  const [postFilterBreed, setPostFilterBreed] = useState('');
  const [categories, setCategories] = useState<any[]>([]);

  // 广告管理相关状态
  const [ads, setAds] = useState<any[]>([]);
  const [adPositions, setAdPositions] = useState<any[]>([]);
  const [adsLoading, setAdsLoading] = useState(false);
  const [activeAdTab, setActiveAdTab] = useState<'ads' | 'positions' | 'statistics'>('ads');
  const [showCreateAdModal, setShowCreateAdModal] = useState(false);

  // 活动管理相关状态
  const [activities, setActivities] = useState<any[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  const [systemConfig, setSystemConfig] = useState<any>(null);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [showCreateActivityModal, setShowCreateActivityModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  // 系统设置相关状态
  const [settings, setSettings] = useState<any>({
    maxImageSize: 5,
    maxImagesPerPost: 9,
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    autoReportThreshold: 10,
    // 帖子数量限制
    normalUserPostLimit: 100,
    superUserPostLimit: 500,
    autoArchiveEnabled: true,
    // 每日发帖限制
    normalUserDailyPostLimit: 5,
    vipUserDailyPostLimit: 20
  });
  const [settingsLoading, setSettingsLoading] = useState(false);

  // 举报阈值设置相关状态
  const [reportThresholds, setReportThresholds] = useState<any>({
    post_report_threshold: 5,
    user_report_threshold: 10,
    auto_hide_posts: true,
    auto_warn_users: true,
    notification_enabled: true
  });
  const [reportThresholdsLoading, setReportThresholdsLoading] = useState(false);

  // 用户管理相关状态
  const [searchUserId, setSearchUserId] = useState('');
  const [searchedUser, setSearchedUser] = useState<any>(null);
  const [userPermissions, setUserPermissions] = useState<any>(null);
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [permissionsLoading, setPermissionsLoading] = useState(false);
  const [userCreditScore, setUserCreditScore] = useState<any>(null);
  const [creditScoreLoading, setCreditScoreLoading] = useState(false);
  const [showCreditAdjustModal, setShowCreditAdjustModal] = useState(false);
  const [creditAdjustment, setCreditAdjustment] = useState({ score: 0, reason: '' });

  // VIP用户管理状态
  const [vipUserInfo, setVipUserInfo] = useState<any>(null);
  const [vipLoading, setVipLoading] = useState(false);
  const [showVipModal, setShowVipModal] = useState(false);
  const [vipDuration, setVipDuration] = useState(30);
  const [vipBenefits, setVipBenefits] = useState({
    daily_post_limit: 50,
    credit_score_limit: 200,
    special_badge: true,
    priority_support: true
  });

  // VIP用户列表管理状态
  const [vipUsers, setVipUsers] = useState<any[]>([]);
  const [vipUsersLoading, setVipUsersLoading] = useState(false);
  const [vipFilter, setVipFilter] = useState<'all' | 'active' | 'expired'>('all');
  const [selectedVipUser, setSelectedVipUser] = useState<any>(null);
  const [showVipDetailModal, setShowVipDetailModal] = useState(false);
  const [permissionsSaving, setPermissionsSaving] = useState(false);
  const [userSearchError, setUserSearchError] = useState<string | null>(null);



  // 每日发帖限制调整状态
  const [showDailyLimitModal, setShowDailyLimitModal] = useState(false);
  const [dailyPostLimit, setDailyPostLimit] = useState<number>(5);

  // 确认模态框状态
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmConfig, setConfirmConfig] = useState<{
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    type?: 'danger' | 'warning' | 'info';
  } | null>(null);
  const [settingsSaving, setSettingsSaving] = useState(false);

  // 通用确认函数
  const showConfirm = (config: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    type?: 'danger' | 'warning' | 'info';
  }) => {
    setConfirmConfig(config);
    setShowConfirmModal(true);
  };

  const handleConfirm = () => {
    if (confirmConfig?.onConfirm) {
      confirmConfig.onConfirm();
    }
    setShowConfirmModal(false);
    setConfirmConfig(null);
  };

  const handleCancel = () => {
    if (confirmConfig?.onCancel) {
      confirmConfig.onCancel();
    }
    setShowConfirmModal(false);
    setConfirmConfig(null);
  };

  // 检查管理员权限并加载数据
  useEffect(() => {
    const adminToken = localStorage.getItem('adminToken');
    const adminUser = localStorage.getItem('adminUser');

    if (!adminToken || !adminUser) {
      router.push('/admin');
      return;
    }

    try {
      const admin = JSON.parse(adminUser);
      setCurrentAdmin(admin);
      loadDashboardData();
    } catch (error) {
      console.error('解析管理员信息失败:', error);
      router.push('/admin');
    }
  }, [router]);

  // 加载仪表板数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 获取真实统计数据
      try {
        // 直接调用云函数
        const cloudbaseApp = await initCloudBase();
        if (!cloudbaseApp) {
          throw new Error('CloudBase未初始化');
        }

        const result = await cloudbaseApp.callFunction({
          name: 'pet-api',
          data: {
            action: 'getDashboardStats',
            data: {}
          }
        });

        if (result.result?.success) {
          setStats(result.result.data);
        } else {
          console.error('获取统计数据失败:', result.result?.message);
          // 使用默认数据
          setStats({
            totalUsers: 0,
            totalPosts: 0,
            totalReports: 0,
            totalAppeals: 0,
            yesterdayNewUsers: 0,
            yesterdayNewPosts: 0,
            activeUsers: 0,
            onlineUsers: 0
          });
        }
      } catch (error) {
        console.error('获取统计数据异常:', error);
        // 使用默认数据
        setStats({
          totalUsers: 0,
          totalPosts: 0,
          totalReports: 0,
          totalAppeals: 0,
          yesterdayNewUsers: 0,
          yesterdayNewPosts: 0,
          activeUsers: 0,
          onlineUsers: 0
        });
      }

      // 尝试加载管理员列表（如果有权限的话）
      try {
        await loadAdmins();
      } catch (error) {
        console.log('无权限加载管理员列表或不是超级管理员');
      }

      // 加载申诉列表
      await loadAppeals();

      // 加载分类列表
      await loadCategories();

      // 加载广告数据
      await loadAds();

      // 加载活动数据
      await loadActivities();

      // 加载系统设置
      await loadSettings();
    } catch (error: any) {
      console.error('加载仪表板数据失败:', error);
      showToast.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载管理员列表
  const loadAdmins = async () => {
    try {
      const result = await petAPI.getAdmins({
        limit: 50,
        offset: 0
      });

      if (result.success) {
        setAdmins(result.data || []);
      }
    } catch (error: any) {
      console.error('加载管理员列表失败:', error);
    }
  };

  // 加载帖子列表
  const loadPosts = async (searchUserId?: string, filterBreed?: string) => {
    setPostsLoading(true);
    try {
      const params: any = { limit: 50 };
      if (searchUserId) {
        params.userId = searchUserId;
      }
      if (filterBreed) {
        params.breed = filterBreed;
      }
      const result = await petAPI.getPostsForAdmin(params);
      if (result.success) {
        setPosts(result.data);
      }
    } catch (error) {
      console.error('加载帖子列表失败:', error);
      showToast.error('加载帖子列表失败');
    } finally {
      setPostsLoading(false);
    }
  };

  // 加载分类列表
  const loadCategories = async () => {
    try {
      const result = await petAPI.getCategories();
      if (result.success) {
        setCategories(result.data);
      }
    } catch (error) {
      console.error('加载分类列表失败:', error);
    }
  };

  // 加载申诉列表
  const loadAppeals = async () => {
    try {
      const result = await petAPI.getAppeals({
        status: 'all',
        limit: 50,
        offset: 0
      });

      if (result.success) {
        setAppeals(result.data || []);
      }
    } catch (error: any) {
      console.error('加载申诉列表失败:', error);
    }
  };

  // 创建管理员
  const createAdmin = async () => {
    if (!newAdminData.username || !newAdminData.password) {
      showToast.error('请填写用户名和密码');
      return;
    }

    try {
      const result = await petAPI.createAdmin({
        ...newAdminData,
        permissions: ['*'] // 普通管理员也拥有所有权限
      });

      if (result.success) {
        showToast.success('管理员创建成功');
        setShowCreateAdminModal(false);
        setNewAdminData({
          username: '',
          password: '',
          role: 'admin',
          level: 1,
          permissions: []
        });
        loadAdmins();
      } else {
        showToast.error(result.message || '创建失败');
      }
    } catch (error: any) {
      console.error('创建管理员失败:', error);
      showToast.error(error.message || '创建管理员失败');
    }
  };

  // 删除管理员
  const deleteAdmin = async (adminId: string) => {
    showConfirm({
      title: '删除管理员',
      message: '确定要删除这个管理员吗？此操作不可恢复。',
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          const result = await petAPI.deleteAdmin({ adminId });
          if (result.success) {
            showToast.success('管理员删除成功');
            loadAdmins();
          } else {
            showToast.error(result.message || '删除失败');
          }
        } catch (error: any) {
          console.error('删除管理员失败:', error);
          showToast.error(error.message || '删除管理员失败');
        }
      }
    });
  };

  // 处理申诉
  const handleAppeal = async () => {
    if (!selectedAppeal) return;

    try {
      setProcessingId(selectedAppeal._id);
      const result = await petAPI.handleAppeal({
        appealId: selectedAppeal._id,
        action: processAction,
        adminReason: adminReason
      });

      if (result.success) {
        showToast.success('申诉处理成功');
        setShowProcessModal(false);
        setSelectedAppeal(null);
        setAdminReason('');
        loadAppeals();
      } else {
        showToast.error(result.message || '处理失败');
      }
    } catch (error: any) {
      console.error('处理申诉失败:', error);
      showToast.error(error.message || '处理申诉失败');
    } finally {
      setProcessingId(null);
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'approved':
        return '已通过';
      case 'rejected':
        return '已驳回';
      default:
        return '未知';
    }
  };

  // 删除帖子
  const deletePost = async () => {
    if (!selectedPost || !deleteReason.trim()) {
      showToast.error('请填写删除原因');
      return;
    }

    try {
      const result = await petAPI.adminDeletePost({
        postId: selectedPost._id,
        reason: deleteReason
      });

      if (result.success) {
        showToast.success('帖子删除成功');
        setShowDeletePostModal(false);
        setSelectedPost(null);
        setDeleteReason('');
        // 如果有搜索条件，重新加载帖子列表
        if (postSearchUserId.trim()) {
          loadPosts(postSearchUserId.trim(), postFilterBreed);
        }
      } else {
        showToast.error(result.message || '删除失败');
      }
    } catch (error: any) {
      console.error('删除帖子失败:', error);
      showToast.error(error.message || '删除帖子失败');
    }
  };

  // 置顶/取消置顶帖子
  const handleTogglePin = async (postId: string, isPinned: boolean) => {
    try {
      await petAPI.togglePostPin({ post_id: postId, is_pinned: isPinned });

      // 更新本地状态
      setPosts(prev => prev.map(post =>
        post._id === postId
          ? { ...post, is_pinned: isPinned }
          : post
      ));

      showToast.success(isPinned ? '帖子已置顶' : '已取消置顶');
    } catch (error) {
      console.error('置顶操作失败:', error);
      showToast.error('操作失败');
    }
  };

  // 调整曝光度
  const handleAdjustExposure = async (postId: string, adjustment: number) => {
    try {
      const result = await petAPI.adjustPostExposure({
        post_id: postId,
        adjustment: adjustment
      });

      if (result.success) {
        // 更新本地状态
        setPosts(prev => prev.map(post =>
          post._id === postId
            ? { ...post, exposure_score: (post.exposure_score || 50) + adjustment }
            : post
        ));

        showToast.success(`曝光度${adjustment > 0 ? '增加' : '减少'}${Math.abs(adjustment)}分`);
      } else {
        showToast.error(result.message || '调整失败');
      }
    } catch (error) {
      console.error('调整曝光度失败:', error);
      showToast.error('调整失败');
    }
  };

  // 下架帖子
  const handleTakeDownPost = async (postId: string) => {
    try {
      const result = await petAPI.takeDownPost({ post_id: postId });

      if (result.success) {
        // 更新本地状态
        setPosts(prev => prev.map(post =>
          post._id === postId
            ? { ...post, status: 'taken_down', exposure_score: 0 }
            : post
        ));

        showToast.success('帖子已下架');
      } else {
        showToast.error(result.message || '下架失败');
      }
    } catch (error) {
      console.error('下架帖子失败:', error);
      showToast.error('下架失败');
    }
  };

  // 搜索帖子
  const handleSearchPosts = () => {
    if (!postSearchUserId.trim()) {
      showToast.error('请输入用户ID进行搜索');
      return;
    }
    loadPosts(postSearchUserId.trim(), postFilterBreed);
  };

  // 重置搜索
  const handleResetSearch = () => {
    setPostSearchUserId('');
    setPostFilterBreed('');
    setPosts([]); // 清空帖子列表
  };

  // 加载广告数据
  const loadAds = async () => {
    setAdsLoading(true);
    try {
      // 模拟广告数据
      const mockAds = [
        {
          _id: 'ad_001',
          title: '优质宠物用品推荐',
          advertiser_id: 'advertiser_001',
          advertiser_name: '宠物之家商城',
          position_id: 'home_banner',
          position_name: '首页横幅广告',
          ad_type: 'banner',
          content: '为您的爱宠提供最好的生活用品，健康快乐每一天！全场8折优惠中。',
          target_url: 'https://example.com/pet-products',
          start_date: '2025-01-01T00:00:00.000Z',
          end_date: '2025-03-31T23:59:59.000Z',
          status: 'active',
          priority: 1,
          budget: 5000,
          spent: 1250.50,
          impressions: 15420,
          clicks: 342,
          ctr: 0.0222,
          created_at: '2025-01-01T00:00:00.000Z'
        },
        {
          _id: 'ad_002',
          title: '专业宠物医院',
          advertiser_id: 'advertiser_002',
          advertiser_name: '爱宠医疗中心',
          position_id: 'home_feed',
          position_name: '首页信息流广告',
          ad_type: 'feed',
          content: '24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。',
          target_url: 'https://example.com/pet-hospital',
          start_date: '2025-01-10T00:00:00.000Z',
          end_date: '2025-02-28T23:59:59.000Z',
          status: 'active',
          priority: 2,
          budget: 3000,
          spent: 890.25,
          impressions: 8750,
          clicks: 156,
          ctr: 0.0178,
          created_at: '2025-01-10T00:00:00.000Z'
        },
        {
          _id: 'ad_003',
          title: '宠物美容服务',
          advertiser_id: 'advertiser_003',
          advertiser_name: '美宠工坊',
          position_id: 'home_feed',
          position_name: '首页信息流广告',
          ad_type: 'feed',
          content: '专业宠物美容，让您的爱宠更加美丽动人。新客户首次服务7折。',
          target_url: 'https://example.com/pet-grooming',
          start_date: '2025-01-15T00:00:00.000Z',
          end_date: '2025-04-15T23:59:59.000Z',
          status: 'paused',
          priority: 3,
          budget: 2000,
          spent: 450.75,
          impressions: 4200,
          clicks: 89,
          ctr: 0.0212,
          created_at: '2025-01-15T00:00:00.000Z'
        }
      ];

      const mockPositions = [
        {
          position_id: 'home_banner',
          name: '首页横幅广告',
          page: 'home',
          location: 'top',
          width: 728,
          height: 90,
          ad_type: 'banner',
          max_ads: 3,
          rotation_interval: 5000,
          status: 'active'
        },
        {
          position_id: 'home_feed',
          name: '首页信息流广告',
          page: 'home',
          location: 'feed',
          width: 300,
          height: 200,
          ad_type: 'feed',
          max_ads: 5,
          rotation_interval: 0,
          status: 'active'
        },
        {
          position_id: 'sidebar_banner',
          name: '侧边栏广告',
          page: 'all',
          location: 'sidebar',
          width: 300,
          height: 250,
          ad_type: 'banner',
          max_ads: 2,
          rotation_interval: 8000,
          status: 'inactive'
        }
      ];

      setAds(mockAds);
      setAdPositions(mockPositions);
    } catch (error) {
      console.error('加载广告数据失败:', error);
      showToast.error('加载广告数据失败');
    } finally {
      setAdsLoading(false);
    }
  };

  // 广告管理辅助函数
  const getAdStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '投放中', color: 'bg-green-100 text-green-800' },
      paused: { label: '已暂停', color: 'bg-yellow-100 text-yellow-800' },
      expired: { label: '已过期', color: 'bg-red-100 text-red-800' },
      pending: { label: '待审核', color: 'bg-blue-100 text-blue-800' },
      inactive: { label: '未启用', color: 'bg-gray-100 text-gray-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // 加载活动数据
  const loadActivities = async () => {
    setActivitiesLoading(true);
    try {
      // 模拟活动数据
      const mockActivities = [
        {
          _id: 'activity_001',
          title: '最萌宠物评选大赛',
          description: '展示你的宠物，参与最萌宠物评选活动，赢取丰厚奖品！',
          type: 'CONTEST',
          status: 'ACTIVE',
          start_time: '2025-01-01T00:00:00.000Z',
          end_time: '2025-01-31T23:59:59.000Z',
          result_display_end_time: '2025-02-03T23:59:59.000Z',
          duration_days: 31,
          result_display_days: 3,
          config: { comments_enabled: true },
          statistics_summary: {
            total_votes: 1250,
            total_comments: 340
          },
          created_at: '2024-12-25T00:00:00.000Z'
        },
        {
          _id: 'activity_002',
          title: '猫咪 VS 狗狗投票',
          description: '你更喜欢猫咪还是狗狗？快来投票表达你的观点！',
          type: 'VOTING',
          status: 'ACTIVE',
          start_time: '2025-01-15T00:00:00.000Z',
          end_time: '2025-02-15T23:59:59.000Z',
          result_display_end_time: '2025-02-18T23:59:59.000Z',
          duration_days: 31,
          result_display_days: 3,
          config: { comments_enabled: true },
          statistics_summary: {
            total_votes: 890,
            total_comments: 156
          },
          created_at: '2025-01-10T00:00:00.000Z'
        },
        {
          _id: 'activity_003',
          title: '宠物护理经验分享',
          description: '分享你的宠物护理经验，帮助更多宠物主人',
          type: 'DISCUSSION',
          status: 'ENDED',
          start_time: '2024-12-01T00:00:00.000Z',
          end_time: '2024-12-31T23:59:59.000Z',
          result_display_end_time: '2025-01-03T23:59:59.000Z',
          duration_days: 31,
          result_display_days: 3,
          config: { comments_enabled: true },
          statistics_summary: {
            total_votes: 0,
            total_comments: 245
          },
          created_at: '2024-11-25T00:00:00.000Z'
        }
      ];

      setActivities(mockActivities);

      // 模拟系统配置
      const mockSystemConfig = {
        enabled: true,
        comments_enabled: true,
        rate_limit_interval: 10,
        max_comment_length: 100,
        default_result_display_days: 3
      };

      setSystemConfig(mockSystemConfig);
    } catch (error) {
      console.error('加载活动数据失败:', error);
      showToast.error('加载活动数据失败');
    } finally {
      setActivitiesLoading(false);
    }
  };

  // 加载系统设置
  const loadSettings = async () => {
    setSettingsLoading(true);
    setReportThresholdsLoading(true);
    try {
      // 加载系统设置
      const settingsResult = await petAPI.getSystemSettings();
      if (settingsResult.success) {
        setSettings(settingsResult.data);
      }

      // 加载举报阈值设置
      const result = await petAPI.getReportThresholds();
      if (result.success) {
        setReportThresholds(result.data);
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      showToast.error('加载设置失败');
    } finally {
      setSettingsLoading(false);
      setReportThresholdsLoading(false);
    }
  };





  // 活动管理辅助函数
  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '🏆';
      case 'VOTING':
        return '🗳️';
      case 'DISCUSSION':
        return '💬';
      default:
        return '🏆';
    }
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'CONTEST':
        return '评选竞赛';
      case 'VOTING':
        return '投票话题';
      case 'DISCUSSION':
        return '讨论活动';
      default:
        return '未知类型';
    }
  };

  const getActivityStatusBadge = (status: string) => {
    const config = {
      DRAFT: { label: '草稿', color: 'bg-yellow-100 text-yellow-800' },
      ACTIVE: { label: '进行中', color: 'bg-green-100 text-green-800' },
      ENDED: { label: '已结束', color: 'bg-blue-100 text-blue-800' },
      ARCHIVED: { label: '已归档', color: 'bg-gray-100 text-gray-800' }
    };

    const cfg = config[status as keyof typeof config] || config.DRAFT;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${cfg.color}`}>
        {cfg.label}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  // 系统设置保存函数
  const saveSettings = async () => {
    try {
      setSettingsSaving(true);

      // 验证设置
      if (settings.maxImageSize <= 0 || settings.maxImageSize > 100) {
        showToast.error('图片大小限制必须在1-100MB之间');
        return;
      }

      if (settings.maxImagesPerPost <= 0 || settings.maxImagesPerPost > 20) {
        showToast.error('每帖图片数量必须在1-20张之间');
        return;
      }

      if (settings.autoReportThreshold <= 0 || settings.autoReportThreshold > 50) {
        showToast.error('自动处罚阈值必须在1-50之间');
        return;
      }

      if (settings.normalUserPostLimit <= 0 || settings.normalUserPostLimit > 1000) {
        showToast.error('普通用户帖子上限必须在1-1000之间');
        return;
      }

      if (settings.superUserPostLimit <= 0 || settings.superUserPostLimit > 5000) {
        showToast.error('超级用户帖子上限必须在1-5000之间');
        return;
      }

      // 保存到云端
      await petAPI.updateSystemSettings(settings);

      showToast.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      showToast.error('保存设置失败');
    } finally {
      setSettingsSaving(false);
    }
  };

  // 保存举报阈值设置
  const saveReportThresholds = async () => {
    try {
      setReportThresholdsLoading(true);

      // 验证设置
      if (reportThresholds.post_report_threshold <= 0 || reportThresholds.post_report_threshold > 100) {
        showToast.error('帖子举报阈值必须在1-100之间');
        return;
      }

      if (reportThresholds.user_report_threshold <= 0 || reportThresholds.user_report_threshold > 100) {
        showToast.error('用户举报阈值必须在1-100之间');
        return;
      }

      // 调用API保存设置
      const result = await petAPI.updateReportThresholds(reportThresholds);
      if (result.success) {
        showToast.success('举报阈值设置保存成功');
      } else {
        showToast.error(result.message || '保存失败');
      }
    } catch (error) {
      console.error('保存举报阈值设置失败:', error);
      showToast.error('保存设置失败');
    } finally {
      setReportThresholdsLoading(false);
    }
  };

  // 统一保存所有系统设置
  const saveAllSettings = async () => {
    try {
      setSettingsSaving(true);
      setReportThresholdsLoading(true);

      // 验证系统设置
      if (settings.maxImageSize <= 0 || settings.maxImageSize > 100) {
        showToast.error('图片大小限制必须在1-100MB之间');
        return;
      }

      if (settings.maxImagesPerPost <= 0 || settings.maxImagesPerPost > 20) {
        showToast.error('每帖图片数量必须在1-20张之间');
        return;
      }

      if (settings.normalUserPostLimit <= 0 || settings.normalUserPostLimit > 1000) {
        showToast.error('普通用户帖子上限必须在1-1000之间');
        return;
      }

      if (settings.superUserPostLimit <= 0 || settings.superUserPostLimit > 5000) {
        showToast.error('超级用户帖子上限必须在1-5000之间');
        return;
      }

      if (settings.normalUserDailyPostLimit <= 0 || settings.normalUserDailyPostLimit > 50) {
        showToast.error('普通用户每日发帖上限必须在1-50之间');
        return;
      }

      if (settings.vipUserDailyPostLimit <= 0 || settings.vipUserDailyPostLimit > 100) {
        showToast.error('VIP用户每日发帖上限必须在1-100之间');
        return;
      }

      // 验证举报阈值设置
      if (reportThresholds.post_report_threshold <= 0 || reportThresholds.post_report_threshold > 100) {
        showToast.error('帖子举报阈值必须在1-100之间');
        return;
      }

      if (reportThresholds.user_report_threshold <= 0 || reportThresholds.user_report_threshold > 100) {
        showToast.error('用户举报阈值必须在1-100之间');
        return;
      }

      // 保存系统设置
      await petAPI.updateSystemSettings(settings);

      // 保存举报阈值设置
      const reportResult = await petAPI.updateReportThresholds(reportThresholds);
      if (!reportResult.success) {
        throw new Error(reportResult.message || '保存举报阈值失败');
      }

      showToast.success('所有设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      showToast.error('保存设置失败');
    } finally {
      setSettingsSaving(false);
      setReportThresholdsLoading(false);
    }
  };

  // 用户搜索功能
  const searchUser = async () => {
    if (!searchUserId.trim()) {
      showToast.error('请输入用户ID');
      return;
    }

    try {
      setUserSearchLoading(true);
      setSearchedUser(null);
      setUserPermissions(null);
      setUserSearchError(null);

      console.log('开始搜索用户:', searchUserId.trim());

      // 获取用户信息
      const userResult = await petAPI.getUserInfo({ userId: searchUserId.trim() });
      console.log('用户搜索结果:', userResult);

      if (!userResult.success) {
        // 用户不存在时显示明确的错误信息
        const errorMessage = '该用户不存在，请检查用户ID是否正确';
        console.log('用户不存在，显示错误信息');
        showToast.error(errorMessage);
        setUserSearchError(errorMessage);
        // 确保状态保持清空状态
        setSearchedUser(null);
        setUserPermissions(null);
        return;
      }

      // 检查返回的用户数据是否有效
      if (!userResult.data || !userResult.data.userInfo || !userResult.data.userInfo._id) {
        const errorMessage = '该用户不存在，请检查用户ID是否正确';
        console.log('用户数据无效，显示错误信息');
        showToast.error(errorMessage);
        setUserSearchError(errorMessage);
        setSearchedUser(null);
        setUserPermissions(null);
        return;
      }

      console.log('用户存在，设置用户数据');
      // 将新的数据结构转换为前端期望的格式
      const userData = {
        id: userResult.data.userInfo._id,
        user_id: userResult.data.userInfo.user_id,
        nickname: userResult.data.userInfo.nickname,
        email: userResult.data.userInfo.email,
        avatar: userResult.data.userInfo.avatar,
        created_at: userResult.data.userInfo.created_at,
        // 添加信用分和VIP信息
        creditInfo: userResult.data.creditInfo,
        vipInfo: userResult.data.vipInfo
      };
      setSearchedUser(userData);
      setUserSearchError(null);

      // 并行加载用户权限、信用分、历史记录和VIP信息，避免相互阻塞
      const loadPromises = [
        // 获取用户权限
        loadUserPermissions(searchUserId.trim()).catch(permissionError => {
          console.error('加载用户权限失败:', permissionError);
          showToast.warning('权限加载失败');
        }),

        // 获取用户信用分
        loadUserCreditScore(searchUserId.trim()).catch(creditError => {
          console.error('加载用户信用分失败:', creditError);
          showToast.warning('信用分加载失败');
        }),



        // 获取VIP用户信息
        loadVipUserInfo(searchUserId.trim()).catch(vipError => {
          console.error('加载VIP用户信息失败:', vipError);
          showToast.warning('VIP信息加载失败');
        })
      ];

      // 等待所有加载完成
      await Promise.allSettled(loadPromises);
      console.log('所有用户数据加载完成');

      showToast.success('用户信息加载成功');
    } catch (error) {
      console.error('搜索用户失败:', error);
      // 网络错误或其他异常
      const errorMessage = '搜索失败，请检查网络连接或稍后重试';
      showToast.error(errorMessage);
      setUserSearchError(errorMessage);
      // 确保状态清空
      setSearchedUser(null);
      setUserPermissions(null);
    } finally {
      setUserSearchLoading(false);
    }
  };

  // 加载用户权限
  const loadUserPermissions = async (userId: string) => {
    try {
      setPermissionsLoading(true);
      console.log('开始加载用户权限:', userId);

      // 调用管理员专用的获取用户权限API
      const permissionsResult = await petAPI.getTargetUserPermissions({
        targetUserId: userId
      });

      console.log('权限API调用结果:', permissionsResult);

      if (permissionsResult.success) {
        console.log('权限加载成功:', permissionsResult.data);
        setUserPermissions(permissionsResult.data);
      } else {
        // API调用失败，设置默认权限
        console.log('权限API调用失败，使用默认权限');
        const defaultPermissions = {
          canLike: true,
          canDislike: true,
          canReportPost: true,
          canReportUser: true,
          canContact: true,
          canPublishPost: true,
          bannedUntil: null,
          banReason: null
        };
        setUserPermissions(defaultPermissions);
        showToast.warning('无法获取用户权限，已设置为默认权限');
      }
    } catch (error) {
      console.error('加载用户权限失败:', error);
      // 网络错误或其他异常，设置默认权限
      const defaultPermissions = {
        canLike: true,
        canDislike: true,
        canReportPost: true,
        canReportUser: true,
        canContact: true,
        canPublishPost: true,
        bannedUntil: null,
        banReason: null
      };
      setUserPermissions(defaultPermissions);
      showToast.warning('加载权限失败，已设置为默认权限');
    } finally {
      setPermissionsLoading(false);
    }
  };

  // 更新用户权限
  const updateUserPermissions = async (newPermissions: any) => {
    if (!searchedUser) {
      showToast.error('请先搜索用户');
      return;
    }

    try {
      setPermissionsSaving(true);

      const result = await petAPI.updateUserPermissions({
        targetUserId: searchedUser.id,
        permissions: newPermissions
      });

      if (result.success) {
        setUserPermissions(newPermissions);
        showToast.success('用户权限更新成功');
      } else {
        showToast.error(result.message || '更新权限失败');
      }
    } catch (error) {
      console.error('更新用户权限失败:', error);
      showToast.error('更新权限失败');
    } finally {
      setPermissionsSaving(false);
    }
  };

  // 加载用户信用分
  const loadUserCreditScore = async (userId: string) => {
    try {
      console.log('开始加载用户信用分:', userId);
      setCreditScoreLoading(true);
      const result = await petAPI.getUserCreditScore({ userId: userId });
      console.log('信用分API调用结果:', result);

      if (result.success) {
        console.log('信用分加载成功:', result.data);
        setUserCreditScore(result.data);
        setDailyPostLimit(result.data.daily_post_limit || 5);
      } else {
        console.log('用户没有信用分记录，创建默认记录');
        // 如果用户没有信用分记录，创建默认记录
        const defaultCreditScore = {
          user_id: userId,
          credit_score: 50,
          is_super_user: false,
          daily_post_limit: 5,
          last_updated: new Date().toISOString()
        };
        setUserCreditScore(defaultCreditScore);
        setDailyPostLimit(5);
      }
    } catch (error) {
      console.error('加载用户信用分失败:', error);
      // 设置默认信用分
      const defaultCreditScore = {
        user_id: userId,
        credit_score: 50,
        is_super_user: false,
        daily_post_limit: 5,
        last_updated: new Date().toISOString()
      };
      setUserCreditScore(defaultCreditScore);
      setDailyPostLimit(5);
    } finally {
      setCreditScoreLoading(false);
    }
  };

  // 调整用户信用分
  const adjustUserCreditScore = async () => {
    if (!searchedUser || !creditAdjustment.reason.trim()) {
      showToast.error('请填写调整原因');
      return;
    }

    if (creditAdjustment.score === 0) {
      showToast.error('请输入调整分数');
      return;
    }

    try {
      const result = await petAPI.updateUserCreditScore({
        userId: searchedUser.user_id,
        creditScore: creditAdjustment.score,
        reason: creditAdjustment.reason
      });

      if (result.success) {
        // 重新加载用户信用分
        await loadUserCreditScore(searchedUser.user_id);
        setShowCreditAdjustModal(false);
        setCreditAdjustment({ score: 0, reason: '' });
        showToast.success('信用分调整成功');
      } else {
        showToast.error(result.message || '调整失败');
      }
    } catch (error) {
      console.error('调整信用分失败:', error);
      showToast.error('调整失败');
    }
  };



  // 加载VIP用户信息
  const loadVipUserInfo = async (userId: string) => {
    try {
      console.log('开始加载VIP用户信息:', userId);
      setVipLoading(true);
      const result = await petAPI.getVipUserInfo({ user_id: userId });
      console.log('VIP用户信息API调用结果:', result);

      if (result.success) {
        console.log('VIP用户信息加载成功:', result.data);
        setVipUserInfo(result.data);
      } else {
        console.log('用户不是VIP用户');
        setVipUserInfo({
          user_id: userId,
          is_vip: false,
          vip_start_time: null,
          vip_end_time: null,
          vip_benefits: {
            daily_post_limit: 5,
            credit_score_limit: 100
          }
        });
      }
    } catch (error) {
      console.error('加载VIP用户信息失败:', error);
      setVipUserInfo(null);
    } finally {
      setVipLoading(false);
    }
  };

  // 设置VIP用户
  const handleSetVipUser = async () => {
    if (!searchedUser) return;

    try {
      setVipLoading(true);
      const result = await petAPI.setVipUser({
        user_id: searchedUser.user_id,
        duration_days: vipDuration,
        benefits: vipBenefits
      });

      if (result.success) {
        showToast.success('设置VIP用户成功');
        setShowVipModal(false);
        // 重新加载VIP信息
        await loadVipUserInfo(searchedUser.user_id);
        // 重新加载信用分信息
        await loadUserCreditScore(searchedUser.user_id);
      } else {
        showToast.error(result.message || '设置VIP用户失败');
      }
    } catch (error) {
      console.error('设置VIP用户失败:', error);
      showToast.error('设置VIP用户失败');
    } finally {
      setVipLoading(false);
    }
  };

  // 移除VIP用户
  const handleRemoveVipUser = async () => {
    if (!searchedUser) return;

    try {
      setVipLoading(true);
      const result = await petAPI.removeVipUser({
        user_id: searchedUser.user_id
      });

      if (result.success) {
        showToast.success('移除VIP用户成功');
        // 重新加载VIP信息
        await loadVipUserInfo(searchedUser.user_id);
        // 重新加载信用分信息
        await loadUserCreditScore(searchedUser.user_id);
      } else {
        showToast.error(result.message || '移除VIP用户失败');
      }
    } catch (error) {
      console.error('移除VIP用户失败:', error);
      showToast.error('移除VIP用户失败');
    } finally {
      setVipLoading(false);
    }
  };

  // 调整每日发帖限制
  const handleAdjustDailyLimit = async () => {
    if (!searchedUser) return;

    try {
      setVipLoading(true);
      const result = await petAPI.updateUserCreditScore({
        userId: searchedUser.user_id,
        creditScore: userCreditScore.credit_score, // 保持信用分不变
        dailyPostLimit: dailyPostLimit,
        reason: `管理员调整每日发帖限制为${dailyPostLimit}条`
      });

      if (result.success) {
        showToast.success('每日发帖限制调整成功');
        setShowDailyLimitModal(false);
        // 重新加载信用分信息
        await loadUserCreditScore(searchedUser.user_id);
      } else {
        showToast.error(result.message || '调整每日发帖限制失败');
      }
    } catch (error) {
      console.error('调整每日发帖限制失败:', error);
      showToast.error('调整每日发帖限制失败');
    } finally {
      setVipLoading(false);
    }
  };

  // 加载VIP用户列表
  const loadVipUserList = async () => {
    try {
      setVipUsersLoading(true);
      const result = await petAPI.getVipUserList({
        limit: 50,
        offset: 0,
        status: vipFilter
      });

      if (result.success) {
        setVipUsers(result.data);
      } else {
        showToast.error(result.message || '加载VIP用户列表失败');
        setVipUsers([]);
      }
    } catch (error) {
      console.error('加载VIP用户列表失败:', error);
      showToast.error('加载VIP用户列表失败');
      setVipUsers([]);
    } finally {
      setVipUsersLoading(false);
    }
  };

  // 处理VIP用户详情
  const handleVipUserDetail = (vipUser: any) => {
    setSelectedVipUser(vipUser);
    setShowVipDetailModal(true);
  };

  // 延长VIP用户时间
  const handleExtendVipUser = async (userId: string, days: number) => {
    try {
      setVipLoading(true);
      const result = await petAPI.updateVipUser({
        user_id: userId,
        duration_days: days
      });

      if (result.success) {
        showToast.success('延长VIP时间成功');
        await loadVipUserList();
        setShowVipDetailModal(false);
      } else {
        showToast.error(result.message || '延长VIP时间失败');
      }
    } catch (error) {
      console.error('延长VIP时间失败:', error);
      showToast.error('延长VIP时间失败');
    } finally {
      setVipLoading(false);
    }
  };

  // 重置为默认设置
  const resetToDefault = () => {
    showConfirm({
      title: '重置设置',
      message: '确定要重置为默认设置吗？当前的自定义设置将会丢失。',
      confirmText: '重置',
      cancelText: '取消',
      type: 'warning',
      onConfirm: () => {
        setSettings({
          maxImageSize: 5,
          maxImagesPerPost: 9,
          allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
          autoReportThreshold: 10,
          normalUserPostLimit: 100,
          superUserPostLimit: 500,
          autoArchiveEnabled: true
        });
        setReportThresholds({
          post_report_threshold: 5,
          user_report_threshold: 10,
          auto_hide_posts: true,
          auto_warn_users: true,
          notification_enabled: true
        });
        showToast.success('已重置为默认设置');
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div className="flex items-center space-x-3">
                <Shield className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">超级管理员控制台</h1>
                  <p className="text-sm text-gray-500">
                    欢迎，{currentAdmin?.username || '管理员'}
                  </p>
                </div>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                router.push('/admin');
              }}
            >
              退出登录
            </Button>
          </div>
        </div>
      </div>

      {/* 导航标签页 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1 overflow-x-auto">
          <button
            onClick={() => setActiveTab('dashboard')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'dashboard'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <BarChart3 className="w-4 h-4" />
            <span>数据概览</span>
          </button>
          <button
            onClick={() => setActiveTab('admins')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'admins'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Users className="w-4 h-4" />
            <span>管理员管理</span>
          </button>
          <button
            onClick={() => setActiveTab('appeals')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'appeals'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <MessageSquare className="w-4 h-4" />
            <span>申诉管理</span>
          </button>

          <button
            onClick={() => {
              setActiveTab('vip');
              if (vipUsers.length === 0) {
                loadVipUserList();
              }
            }}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'vip'
                ? 'bg-white text-purple-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Crown className="w-4 h-4" />
            <span>VIP管理</span>
          </button>

          <button
            onClick={() => setActiveTab('users')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'users'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <UserPlus className="w-4 h-4" />
            <span>用户管理</span>
          </button>
          <button
            onClick={() => setActiveTab('ads')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'ads'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            <span>广告管理</span>
          </button>
          <button
            onClick={() => setActiveTab('posts')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'posts'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
            <span>帖子管理</span>
          </button>
          <button
            onClick={() => setActiveTab('activities')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'activities'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
            <span>活动管理</span>
          </button>
          <button
            onClick={() => setActiveTab('posts')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'posts'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FileText className="w-4 h-4" />
            <span>内容管理</span>
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
              activeTab === 'settings'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Settings className="w-4 h-4" />
            <span>系统设置</span>
          </button>
        </div>

        {/* 数据概览仪表板 */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总用户数</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalUsers || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <FileText className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总宝贝数</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalPosts || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <UserPlus className="w-6 h-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">昨日新增用户</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.yesterdayNewUsers || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-teal-100 rounded-lg">
                    <Plus className="w-6 h-6 text-teal-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">昨日新增宝贝</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.yesterdayNewPosts || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <Flag className="w-6 h-6 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">待处理举报</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalReports || 0}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 第二行统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <MessageSquare className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">待处理申诉</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalAppeals || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-indigo-100 rounded-lg">
                    <Activity className="w-6 h-6 text-indigo-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">活跃用户</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.activeUsers || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-pink-100 rounded-lg">
                    <Globe className="w-6 h-6 text-pink-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">在线用户</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.onlineUsers || 0}</p>
                  </div>
                </div>
              </div>
            </div>


          </div>
        )}

        {/* 管理员管理 */}
        {activeTab === 'admins' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">管理员列表</h2>
              {/* 只有超级管理员才能创建其他管理员 */}
              {currentAdmin?.role === 'super_admin' && (
                <Button
                  onClick={() => setShowCreateAdminModal(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>创建管理员</span>
                </Button>
              )}
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-500 mt-4">加载中...</p>
              </div>
            ) : admins.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无管理员</h3>
                <p className="text-gray-500">点击上方按钮创建第一个管理员</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        管理员信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        角色权限
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {admins.filter(admin => admin.username !== 'superadminTT').map((admin) => (
                      <tr key={admin._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{admin.username}</div>
                            <div className="text-sm text-gray-500">管理员账号</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {admin.role === 'super_admin' ? '超级管理员' : '普通管理员'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {admin.role === 'super_admin' ? '拥有所有权限' : '拥有所有业务权限'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            admin.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : admin.status === 'suspended'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {admin.status === 'active' ? '正常' : admin.status === 'suspended' ? '暂停' : '禁用'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(admin.created_at).toLocaleDateString('zh-CN')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            {/* 只有超级管理员才能删除其他管理员，且不能删除系统账号 */}
                            {currentAdmin?.role === 'super_admin' && !admin.is_system_account && (
                              <button
                                onClick={() => deleteAdmin(admin._id)}
                                className="text-red-600 hover:text-red-900"
                                title="删除管理员"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                            {/* 如果没有可操作的按钮，显示占位文本 */}
                            {!(currentAdmin?.role === 'super_admin' && !admin.is_system_account) && (
                              <span className="text-gray-400 text-sm">无操作权限</span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* 申诉列表 */}
        {activeTab === 'appeals' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">申诉列表</h2>
            </div>
            
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-500 mt-4">加载中...</p>
              </div>
            ) : appeals.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无申诉</h3>
                <p className="text-gray-500">目前没有需要处理的申诉</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {appeals.map((appeal) => (
                  <div key={appeal._id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusStyle(appeal.status)}`}>
                            {getStatusText(appeal.status)}
                          </span>
                          <span className="text-sm text-gray-500">
                            {appeal.type === 'post' ? '帖子申诉' : '用户申诉'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {formatTime(appeal.created_at)}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-900 mb-2">
                          <span className="font-medium">申诉理由：</span>
                          {appeal.reason}
                        </p>
                        
                        <p className="text-xs text-gray-500">
                          举报ID: {appeal.report_id} | 申诉人ID: {appeal.appellant_id}
                        </p>
                        
                        {appeal.admin_reason && (
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-blue-900">
                              <span className="font-medium">管理员回复：</span>
                              {appeal.admin_reason}
                            </p>
                          </div>
                        )}
                      </div>
                      
                      {appeal.status === 'pending' && (
                        <div className="flex space-x-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedAppeal(appeal);
                              setProcessAction('approved');
                              setShowProcessModal(true);
                            }}
                            disabled={processingId === appeal._id}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            通过
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedAppeal(appeal);
                              setProcessAction('rejected');
                              setShowProcessModal(true);
                            }}
                            disabled={processingId === appeal._id}
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            驳回
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}



        {/* 用户管理 */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* 用户搜索 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Search className="w-6 h-6 text-blue-600" />
                  <h3 className="text-lg font-medium text-gray-900">用户搜索</h3>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  输入用户ID搜索用户信息和权限设置
                </p>
              </div>

              <div className="px-6 py-6">
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      用户ID
                    </label>
                    <input
                      type="text"
                      placeholder="请输入用户ID..."
                      value={searchUserId}
                      onChange={(e) => {
                        setSearchUserId(e.target.value);
                        // 清除之前的错误状态
                        if (userSearchError) {
                          setUserSearchError(null);
                        }
                      }}
                      onKeyPress={(e) => e.key === 'Enter' && searchUser()}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="pt-6">
                    <Button
                      onClick={searchUser}
                      loading={userSearchLoading}
                      disabled={userSearchLoading || !searchUserId.trim()}
                      className="flex items-center space-x-2"
                    >
                      <Search className="w-4 h-4" />
                      <span>{userSearchLoading ? '搜索中...' : '搜索用户'}</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* 用户信息显示 */}
            {searchedUser && (
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <User className="w-6 h-6 text-green-600" />
                    <h3 className="text-lg font-medium text-gray-900">用户信息</h3>
                  </div>
                </div>

                <div className="px-6 py-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* 基本信息 */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <img
                          src={searchedUser.avatar_url || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'}
                          alt={searchedUser.nickname}
                          className="w-16 h-16 rounded-full"
                        />
                        <div>
                          <h4 className="text-lg font-medium text-gray-900">{searchedUser.nickname}</h4>
                          <p className="text-sm text-gray-500">ID: {searchedUser.id}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">注册时间</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {new Date(searchedUser.created_at).toLocaleDateString('zh-CN')}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">最后登录</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {searchedUser.last_login ? new Date(searchedUser.last_login).toLocaleDateString('zh-CN') : '未知'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="space-y-4">
                      <h5 className="text-sm font-medium text-gray-700">统计信息</h5>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-blue-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-blue-600">{searchedUser.posts_count || 0}</div>
                          <div className="text-sm text-blue-800">发布帖子</div>
                        </div>
                        <div className="bg-green-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-green-600">{searchedUser.likes_count || 0}</div>
                          <div className="text-sm text-green-800">获得点赞</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 信用分管理 */}
                  {userCreditScore && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-center justify-between mb-4">
                        <h5 className="text-sm font-medium text-gray-700">信用分管理</h5>
                        <button
                          onClick={() => setShowCreditAdjustModal(true)}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          调整信用分
                        </button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* 当前信用分 */}
                        <div className={`rounded-lg p-4 ${
                          userCreditScore.credit_score >= 80 ? 'bg-green-50' :
                          userCreditScore.credit_score >= 60 ? 'bg-yellow-50' : 'bg-red-50'
                        }`}>
                          <div className={`text-3xl font-bold ${
                            userCreditScore.credit_score >= 80 ? 'text-green-600' :
                            userCreditScore.credit_score >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {userCreditScore.credit_score}
                          </div>
                          <div className={`text-sm ${
                            userCreditScore.credit_score >= 80 ? 'text-green-800' :
                            userCreditScore.credit_score >= 60 ? 'text-yellow-800' : 'text-red-800'
                          }`}>
                            当前信用分
                          </div>
                        </div>

                        {/* 每日发帖限制 */}
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-lg font-semibold text-gray-900">
                                {userCreditScore.daily_post_limit || 5}
                              </div>
                              <div className="text-sm text-gray-600">每日发帖限制</div>
                            </div>
                            <button
                              onClick={() => setShowDailyLimitModal(true)}
                              className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                            >
                              调整
                            </button>
                          </div>
                        </div>

                        {/* 升级VIP */}
                        <div className="bg-purple-50 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-lg font-semibold text-purple-600">
                                {vipUserInfo?.is_vip ? 'VIP用户' : '普通用户'}
                              </div>
                              <div className="text-sm text-gray-600">用户状态</div>
                            </div>
                            {!vipUserInfo?.is_vip && (
                              <button
                                onClick={() => setShowVipModal(true)}
                                disabled={vipLoading}
                                className="px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors disabled:opacity-50"
                              >
                                升级VIP
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 权限管理 */}
            {searchedUser && userPermissions && (
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Shield className="w-6 h-6 text-purple-600" />
                      <h3 className="text-lg font-medium text-gray-900">权限管理</h3>
                    </div>
                    <Button
                      onClick={() => updateUserPermissions(userPermissions)}
                      loading={permissionsSaving}
                      disabled={permissionsSaving}
                      className="flex items-center space-x-2"
                    >
                      <Save className="w-4 h-4" />
                      <span>{permissionsSaving ? '保存中...' : '保存权限'}</span>
                    </Button>
                  </div>
                </div>

                {permissionsLoading ? (
                  <div className="px-6 py-8 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">加载权限中...</p>
                  </div>
                ) : (
                  <div className="px-6 py-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* 基础权限 */}
                      <div className="space-y-4">
                        <h5 className="text-sm font-medium text-gray-700">基础权限</h5>

                        <div className="space-y-3">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={userPermissions.canLike}
                              onChange={(e) => setUserPermissions(prev => ({
                                ...prev,
                                canLike: e.target.checked
                              }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">点赞权限</span>
                          </label>

                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={userPermissions.canDislike}
                              onChange={(e) => setUserPermissions(prev => ({
                                ...prev,
                                canDislike: e.target.checked
                              }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">不喜欢权限</span>
                          </label>

                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={userPermissions.canContact}
                              onChange={(e) => setUserPermissions(prev => ({
                                ...prev,
                                canContact: e.target.checked
                              }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">联系权限</span>
                          </label>
                        </div>
                      </div>

                      {/* 高级权限 */}
                      <div className="space-y-4">
                        <h5 className="text-sm font-medium text-gray-700">高级权限</h5>

                        <div className="space-y-3">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={userPermissions.canPublishPost}
                              onChange={(e) => setUserPermissions(prev => ({
                                ...prev,
                                canPublishPost: e.target.checked
                              }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">发帖权限</span>
                          </label>

                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={userPermissions.canReportPost}
                              onChange={(e) => setUserPermissions(prev => ({
                                ...prev,
                                canReportPost: e.target.checked
                              }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">举报帖子权限</span>
                          </label>

                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={userPermissions.canReportUser}
                              onChange={(e) => setUserPermissions(prev => ({
                                ...prev,
                                canReportUser: e.target.checked
                              }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">举报用户权限</span>
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* 权限状态说明 */}
                    <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <Shield className="w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-gray-800">
                          <p className="font-medium mb-1">权限说明</p>
                          <ul className="list-disc list-inside space-y-1">
                            <li>点赞权限：控制用户是否可以给帖子点赞</li>
                            <li>不喜欢权限：控制用户是否可以给帖子点不喜欢</li>
                            <li>联系权限：控制用户是否可以查看和使用联系方式</li>
                            <li>发帖权限：控制用户是否可以发布新帖子</li>
                            <li>举报权限：控制用户是否可以举报帖子和其他用户</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 空状态和错误状态 */}
            {!searchedUser && !userSearchLoading && (
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-12 text-center">
                  {userSearchError ? (
                    <>
                      <X className="w-16 h-16 text-red-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-red-900 mb-2">搜索失败</h3>
                      <p className="text-red-600 mb-4">{userSearchError}</p>
                      <Button
                        onClick={() => {
                          setUserSearchError(null);
                          setSearchUserId('');
                        }}
                        variant="outline"
                        className="text-gray-600 border-gray-300 hover:bg-gray-50"
                      >
                        重新搜索
                      </Button>
                    </>
                  ) : (
                    <>
                      <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">请搜索用户</h3>
                      <p className="text-gray-500">输入用户ID来查看用户信息和管理权限</p>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 内容管理 */}
        {activeTab === 'posts' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">帖子管理</h2>
            </div>

            {/* 搜索和筛选 */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    按用户ID搜索
                  </label>
                  <input
                    type="text"
                    value={postSearchUserId}
                    onChange={(e) => setPostSearchUserId(e.target.value)}
                    placeholder="输入用户ID"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    按宠物品种筛选
                  </label>
                  <select
                    value={postFilterBreed}
                    onChange={(e) => setPostFilterBreed(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部品种</option>
                    {categories.map((category) => (
                      <optgroup key={category._id} label={category.name}>
                        {category.subcategories?.map((sub: any) => (
                          <option key={sub._id} value={sub.name}>
                            {sub.name}
                          </option>
                        ))}
                      </optgroup>
                    ))}
                  </select>
                </div>
                <div className="flex items-end space-x-2">
                  <button
                    onClick={handleSearchPosts}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    搜索用户帖子
                  </button>
                  <button
                    onClick={handleResetSearch}
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                  >
                    重置
                  </button>
                </div>
              </div>
            </div>

            {postsLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">加载中...</p>
              </div>
            ) : !postSearchUserId.trim() ? (
              <div className="p-6 text-center">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">请搜索用户帖子</h3>
                <p className="text-gray-500">输入用户ID并点击搜索按钮来查看该用户的所有帖子</p>
              </div>
            ) : posts.length === 0 ? (
              <div className="p-6 text-center">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">该用户暂无帖子</h3>
                <p className="text-gray-500">用户ID: {postSearchUserId}</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        帖子信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        作者
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        互动统计
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        举报统计
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        曝光度/评分
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        发布时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {posts.map((post) => (
                      <tr key={post._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {post.images && post.images[0] && (
                              <img
                                src={post.images[0]}
                                alt={post.title}
                                className="h-12 w-12 rounded-lg object-cover mr-4"
                              />
                            )}
                            <div>
                              <a
                                href={`/post/${post._id}`}
                                target="_blank"
                                className="text-sm font-medium text-blue-600 hover:text-blue-800 max-w-xs truncate block"
                              >
                                {post.title}
                              </a>
                              <div className="text-sm text-gray-500 max-w-xs truncate">
                                {post.description}
                              </div>
                              <div className="text-xs text-gray-400">
                                {post.category} • ¥{post.price}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <img
                              src={post.author_info?.avatar_url || '/default-avatar.png'}
                              alt={post.author_info?.nickname}
                              className="h-8 w-8 rounded-full mr-2"
                            />
                            <div>
                              <div className="text-sm text-gray-900">
                                {post.author_info?.nickname || '未知用户'}
                              </div>
                              <div className="text-xs text-gray-500">
                                ID: {post.user_id?.slice(0, 8)}...
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="space-y-1">
                            <div>👍 {post.likes_count || 0}</div>
                            <div>👎 {post.dislikes_count || 0}</div>
                            <div>💖 {post.wants_count || 0}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="space-y-1">
                            <div className="text-red-600">理由1: {post.report_reason1_count || 0}</div>
                            <div className="text-red-600">理由2: {post.report_reason2_count || 0}</div>
                            <div className="text-orange-600">总计: {post.reports_count || 0}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="space-y-1">
                            <div className={`font-medium ${
                              (post.exposure_score || 50) > 40 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              曝光度: {post.exposure_score || 50}分
                            </div>
                            <div className="text-blue-600">
                              评分: {(post.avg_rating || 0).toFixed(1)}⭐
                            </div>
                            <div className="text-xs">
                              {post.is_pinned ? '📌 已置顶' : (post.exposure_score || 50) > 40 ? '👁️ 可见' : '🚫 隐藏'}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {post.timeAgo}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex flex-col space-y-2">
                            {/* 曝光度调整 */}
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handleAdjustExposure(post._id, 10)}
                                className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                                title="增加曝光度 +10"
                              >
                                +10
                              </button>
                              <button
                                onClick={() => handleAdjustExposure(post._id, 50)}
                                className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                                title="增加曝光度 +50"
                              >
                                +50
                              </button>
                              <button
                                onClick={() => handleAdjustExposure(post._id, 100)}
                                className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                                title="增加曝光度 +100"
                              >
                                +100
                              </button>
                            </div>
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handleAdjustExposure(post._id, -10)}
                                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                                title="减少曝光度 -10"
                              >
                                -10
                              </button>
                              <button
                                onClick={() => handleAdjustExposure(post._id, -50)}
                                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                                title="减少曝光度 -50"
                              >
                                -50
                              </button>
                              <button
                                onClick={() => handleAdjustExposure(post._id, -100)}
                                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                                title="减少曝光度 -100"
                              >
                                -100
                              </button>
                            </div>
                            {/* 管理操作 */}
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handleTogglePin(post._id, !post.is_pinned)}
                                className={`px-2 py-1 text-xs rounded ${
                                  post.is_pinned
                                    ? 'bg-orange-100 text-orange-700 hover:bg-orange-200'
                                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                                }`}
                                title={post.is_pinned ? '取消置顶' : '置顶帖子'}
                              >
                                {post.is_pinned ? '取消置顶' : '置顶'}
                              </button>
                              <button
                                onClick={() => handleTakeDownPost(post._id)}
                                className="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200"
                                title="下架帖子"
                              >
                                下架
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedPost(post);
                                  setShowDeletePostModal(true);
                                }}
                                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                                title="删除帖子"
                              >
                                删除
                              </button>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* VIP管理 */}
        {activeTab === 'vip' && (
          <div className="space-y-6">
            {/* VIP管理头部 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Crown className="w-6 h-6 text-purple-600" />
                    <h2 className="text-lg font-semibold text-gray-900">VIP用户管理</h2>
                  </div>
                  <div className="flex items-center space-x-3">
                    <select
                      value={vipFilter}
                      onChange={(e) => {
                        setVipFilter(e.target.value as 'all' | 'active' | 'expired');
                        loadVipUserList();
                      }}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="all">全部VIP用户</option>
                      <option value="active">有效VIP用户</option>
                      <option value="expired">过期VIP用户</option>
                    </select>
                    <button
                      onClick={loadVipUserList}
                      disabled={vipUsersLoading}
                      className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors"
                    >
                      {vipUsersLoading ? '加载中...' : '刷新'}
                    </button>
                  </div>
                </div>
              </div>

              {/* VIP用户列表 */}
              <div className="p-6">
                {vipUsersLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                    <span className="ml-2 text-gray-600">加载VIP用户列表...</span>
                  </div>
                ) : vipUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <Crown className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">暂无VIP用户</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {vipUsers.map((vipUser) => (
                      <div
                        key={vipUser._id}
                        className={`border rounded-lg p-4 ${
                          vipUser.is_vip ? 'border-purple-200 bg-purple-50' : 'border-gray-200 bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <img
                              src={vipUser.user_info?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'}
                              alt={vipUser.user_info?.nickname || '未知用户'}
                              className="w-12 h-12 rounded-full"
                            />
                            <div>
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium text-gray-900">
                                  {vipUser.user_info?.nickname || '未知用户'}
                                </h4>
                                {vipUser.is_vip && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <Crown className="w-3 h-3 mr-1" />
                                    VIP
                                  </span>
                                )}
                                {vipUser.is_expired && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    已过期
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">ID: {vipUser.user_id}</p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="text-sm font-medium text-gray-900">
                                {vipUser.is_vip ? `剩余 ${vipUser.remaining_days} 天` : '已过期'}
                              </div>
                              <div className="text-xs text-gray-500">
                                到期时间: {new Date(vipUser.vip_end_time).toLocaleDateString('zh-CN')}
                              </div>
                            </div>
                            <button
                              onClick={() => handleVipUserDetail(vipUser)}
                              className="px-3 py-1 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                            >
                              管理
                            </button>
                          </div>
                        </div>

                        {/* VIP权益预览 */}
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">每日发帖:</span>
                              <span className="ml-1 font-medium">{vipUser.vip_benefits?.daily_post_limit || 5}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">信用分上限:</span>
                              <span className="ml-1 font-medium">{vipUser.vip_benefits?.credit_score_limit || 100}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">专属标识:</span>
                              <span className="ml-1 font-medium">{vipUser.vip_benefits?.special_badge ? '✓' : '✗'}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">优先支持:</span>
                              <span className="ml-1 font-medium">{vipUser.vip_benefits?.priority_support ? '✓' : '✗'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 广告管理 */}
        {activeTab === 'ads' && (
          <div className="space-y-6">
            {/* 广告统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Eye className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总展示量</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {ads.reduce((sum, ad) => sum + ad.impressions, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总点击量</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {ads.reduce((sum, ad) => sum + ad.clicks, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Database className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总收益</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(ads.reduce((sum, ad) => sum + ad.spent, 0))}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Activity className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">活跃广告</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {ads.filter(ad => ad.status === 'active').length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 广告管理子标签页 */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { key: 'ads', label: '广告列表', icon: Eye },
                  { key: 'positions', label: '广告位管理', icon: BarChart3 },
                  { key: 'statistics', label: '数据统计', icon: Database }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveAdTab(tab.key as any)}
                    className={`${
                      activeAdTab === tab.key
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* 创建广告按钮 */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {activeAdTab === 'ads' ? '广告列表' :
                   activeAdTab === 'positions' ? '广告位管理' : '数据统计'}
                </h2>
              </div>
              {activeAdTab === 'ads' && (
                <Button
                  onClick={() => setShowCreateAdModal(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>创建广告</span>
                </Button>
              )}
            </div>

            {/* 广告列表内容 */}
            {activeAdTab === 'ads' && (
              <div className="bg-white shadow rounded-lg">
                {adsLoading ? (
                  <div className="p-6 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-500">加载中...</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            广告信息
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            广告位
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            数据
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            收益
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {ads.length === 0 ? (
                          <tr>
                            <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                              暂无广告数据
                            </td>
                          </tr>
                        ) : (
                          ads.map((ad) => (
                            <tr key={ad._id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  {ad.image_url && (
                                    <img
                                      src={ad.image_url}
                                      alt={ad.title}
                                      className="h-10 w-10 rounded object-cover mr-3"
                                    />
                                  )}
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{ad.title}</div>
                                    <div className="text-sm text-gray-500">{ad.advertiser_name}</div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">{ad.position_name}</div>
                                <div className="text-sm text-gray-500">{ad.ad_type}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {getAdStatusBadge(ad.status)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>展示: {ad.impressions.toLocaleString()}</div>
                                <div>点击: {ad.clicks.toLocaleString()}</div>
                                <div>CTR: {formatPercentage(ad.ctr)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>预算: {formatCurrency(ad.budget)}</div>
                                <div>已花费: {formatCurrency(ad.spent)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div className="flex space-x-2">
                                  <button className="text-blue-600 hover:text-blue-900">
                                    <Eye className="w-4 h-4" />
                                  </button>
                                  <button className="text-green-600 hover:text-green-900">
                                    <Edit className="w-4 h-4" />
                                  </button>
                                  <button className="text-yellow-600 hover:text-yellow-900">
                                    {ad.status === 'active' ? <Clock className="w-4 h-4" /> : <Activity className="w-4 h-4" />}
                                  </button>
                                  <button className="text-red-600 hover:text-red-900">
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {/* 广告位管理内容 */}
            {activeAdTab === 'positions' && (
              <div className="bg-white shadow rounded-lg">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          广告位信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          位置
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          尺寸
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          类型
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adPositions.map((position) => (
                        <tr key={position.position_id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{position.name}</div>
                              <div className="text-sm text-gray-500">{position.description}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{position.page}</div>
                            <div className="text-sm text-gray-500">{position.location}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {position.width} × {position.height}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {position.ad_type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getAdStatusBadge(position.status)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button className="text-green-600 hover:text-green-900">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="text-yellow-600 hover:text-yellow-900">
                                {position.status === 'active' ? <Clock className="w-4 h-4" /> : <Activity className="w-4 h-4" />}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* 数据统计内容 */}
            {activeAdTab === 'statistics' && (
              <div className="space-y-6">
                {/* 用户体验策略 */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">用户体验策略</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">广告频率控制</h4>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          每日最大展示次数
                        </label>
                        <input
                          type="number"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue={10}
                          min={1}
                          max={50}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          最小展示间隔（分钟）
                        </label>
                        <input
                          type="number"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue={30}
                          min={5}
                          max={120}
                        />
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="respectUserChoice"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          defaultChecked
                        />
                        <label htmlFor="respectUserChoice" className="ml-2 block text-sm text-gray-900">
                          尊重用户隐藏选择
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="adaptiveFrequency"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          defaultChecked
                        />
                        <label htmlFor="adaptiveFrequency" className="ml-2 block text-sm text-gray-900">
                          自适应展示频率
                        </label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">广告位策略</h4>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">首页横幅</div>
                            <div className="text-sm text-gray-600">用户友好度：高</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">启用</span>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">信息流广告</div>
                            <div className="text-sm text-gray-600">用户友好度：中</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">启用</span>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">详情页底部</div>
                            <div className="text-sm text-gray-600">用户友好度：高</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">启用</span>
                            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">启动弹窗</div>
                            <div className="text-sm text-red-600">用户友好度：低</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">禁用</span>
                            <input type="checkbox" className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium text-gray-900">用户体验建议</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          基于用户行为数据的智能推荐
                        </p>
                      </div>
                      <Button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        保存设置
                      </Button>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-green-800">推荐</span>
                        </div>
                        <p className="text-sm text-green-700 mt-1">
                          原生信息流广告，用户接受度高
                        </p>
                      </div>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-yellow-800">谨慎</span>
                        </div>
                        <p className="text-sm text-yellow-700 mt-1">
                          横幅广告需要控制频率
                        </p>
                      </div>

                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-red-800">避免</span>
                        </div>
                        <p className="text-sm text-red-700 mt-1">
                          弹窗广告容易引起用户反感
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 数据统计图表 */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">广告效果分析</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">92%</div>
                      <div className="text-sm text-gray-600">用户满意度</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">3.2%</div>
                      <div className="text-sm text-gray-600">平均点击率</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">15s</div>
                      <div className="text-sm text-gray-600">平均停留时间</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">8%</div>
                      <div className="text-sm text-gray-600">广告隐藏率</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 活动管理 */}
        {activeTab === 'activities' && (
          <div className="space-y-6">
            {/* 页面标题和操作 */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">活动管理</h2>
                <p className="text-gray-600">管理社区活动和系统配置</p>
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowConfigModal(true)}
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <Settings className="h-4 w-4" />
                  <span>系统设置</span>
                </Button>
                <Button
                  onClick={() => setShowCreateActivityModal(true)}
                  className="flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>创建活动</span>
                </Button>
              </div>
            </div>

            {/* 系统状态提示 */}
            <div className={`p-4 rounded-lg ${systemConfig?.enabled ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
              <div className="flex items-center">
                <div className={`h-3 w-3 rounded-full mr-3 ${systemConfig?.enabled ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <span className={`font-medium ${systemConfig?.enabled ? 'text-green-800' : 'text-yellow-800'}`}>
                  活动系统状态：{systemConfig?.enabled ? '已启用' : '已禁用'}
                </span>
                {!systemConfig?.enabled && (
                  <span className="ml-2 text-yellow-700">（用户端不显示活动入口）</span>
                )}
              </div>
            </div>

            {/* 筛选器 */}
            <div className="bg-white p-4 rounded-lg shadow flex space-x-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="all">全部状态</option>
                  <option value="DRAFT">草稿</option>
                  <option value="ACTIVE">进行中</option>
                  <option value="ENDED">已结束</option>
                  <option value="ARCHIVED">已归档</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">类型筛选</label>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="all">全部类型</option>
                  <option value="CONTEST">评选竞赛</option>
                  <option value="VOTING">投票话题</option>
                  <option value="DISCUSSION">讨论活动</option>
                </select>
              </div>
            </div>

            {/* 活动列表 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">活动列表</h3>
              </div>
              {activitiesLoading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          活动信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          类型
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          时间
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          参与数据
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {activities.length === 0 ? (
                        <tr>
                          <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                            暂无活动数据
                          </td>
                        </tr>
                      ) : (
                        activities.map((activity) => (
                          <tr key={activity._id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                                  {activity.title}
                                </div>
                                <div className="text-sm text-gray-500 max-w-xs truncate">
                                  {activity.description}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <span className="text-lg mr-2">{getActivityTypeIcon(activity.type)}</span>
                                <span className="text-sm text-gray-900">
                                  {getActivityTypeLabel(activity.type)}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {getActivityStatusBadge(activity.status)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div>开始：{formatDate(activity.start_time)}</div>
                              <div>结束：{formatDate(activity.end_time)}</div>
                              <div className="text-xs text-gray-500">
                                持续 {activity.duration_days} 天
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div>投票：{activity.statistics_summary?.total_votes || 0}</div>
                              <div>评论：{activity.statistics_summary?.total_comments || 0}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => window.open(`/activities/${activity._id}`, '_blank')}
                                  className="text-blue-600 hover:text-blue-900"
                                  title="查看活动"
                                >
                                  <Eye className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => showToast.info('编辑功能开发中')}
                                  className="text-green-600 hover:text-green-900"
                                  title="编辑活动"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => {
                                    showConfirm({
                                      title: '删除活动',
                                      message: '确定要删除这个活动吗？此操作不可恢复。',
                                      confirmText: '删除',
                                      cancelText: '取消',
                                      type: 'danger',
                                      onConfirm: () => {
                                        showToast.info('删除功能开发中');
                                      }
                                    });
                                  }}
                                  className="text-red-600 hover:text-red-900"
                                  title="删除活动"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 系统设置 */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* 页面标题 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900">系统设置</h2>
              <p className="text-gray-600">配置系统参数和规则</p>
            </div>

            {/* 图片上传设置 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Image className="w-6 h-6 text-blue-600" />
                  <h3 className="text-lg font-medium text-gray-900">图片上传设置</h3>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  配置用户上传图片的限制和规则
                </p>
              </div>

              {settingsLoading ? (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">加载中...</p>
                </div>
              ) : (
                <div className="px-6 py-6 space-y-6">
                  {/* 单张图片大小限制 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      单张图片大小限制 (MB)
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="100"
                        step="1"
                        value={settings.maxImageSize}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          maxImageSize: parseInt(e.target.value) || 1
                        }))}
                        className="block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-500">
                        当前限制：{settings.maxImageSize}MB
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      建议设置在5-30MB之间，过大会影响上传速度
                    </p>
                  </div>

                  {/* 每帖最大图片数量 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      每帖最大图片数量
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="20"
                        step="1"
                        value={settings.maxImagesPerPost}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          maxImagesPerPost: parseInt(e.target.value) || 1
                        }))}
                        className="block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-500">
                        当前限制：{settings.maxImagesPerPost}张
                      </span>
                    </div>
                  </div>

                  {/* 支持的图片格式 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      支持的图片格式
                    </label>
                    <div className="space-y-2">
                      {['image/jpeg', 'image/png', 'image/webp', 'image/gif'].map((type) => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.allowedImageTypes.includes(type)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSettings(prev => ({
                                  ...prev,
                                  allowedImageTypes: [...prev.allowedImageTypes, type]
                                }));
                              } else {
                                setSettings(prev => ({
                                  ...prev,
                                  allowedImageTypes: prev.allowedImageTypes.filter(t => t !== type)
                                }));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {type.replace('image/', '').toUpperCase()}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 举报阈值设置 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                  <h3 className="text-lg font-medium text-gray-900">举报阈值设置</h3>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  配置自动处理举报的阈值和规则
                </p>
              </div>

              {reportThresholdsLoading ? (
                <div className="px-6 py-8 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">加载中...</p>
                </div>
              ) : (
                <div className="px-6 py-6 space-y-6">
                  {/* 帖子举报阈值 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      帖子举报阈值
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="100"
                        value={reportThresholds.post_report_threshold}
                        onChange={(e) => setReportThresholds(prev => ({
                          ...prev,
                          post_report_threshold: parseInt(e.target.value) || 5
                        }))}
                        className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-600">人举报后自动隐藏帖子</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      当帖子被举报达到此数量时，系统将自动隐藏该帖子
                    </p>
                  </div>

                  {/* 用户举报阈值 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      用户举报阈值
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="100"
                        value={reportThresholds.user_report_threshold}
                        onChange={(e) => setReportThresholds(prev => ({
                          ...prev,
                          user_report_threshold: parseInt(e.target.value) || 10
                        }))}
                        className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-600">人举报后发送警告通知</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      当用户被举报达到此数量时，系统将发送警告通知
                    </p>
                  </div>

                  {/* 自动处理选项 */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-700">自动处理选项</h4>

                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={reportThresholds.auto_hide_posts}
                          onChange={(e) => setReportThresholds(prev => ({
                            ...prev,
                            auto_hide_posts: e.target.checked
                          }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          自动隐藏被举报的帖子
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={reportThresholds.auto_warn_users}
                          onChange={(e) => setReportThresholds(prev => ({
                            ...prev,
                            auto_warn_users: e.target.checked
                          }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          自动警告被举报的用户
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={reportThresholds.notification_enabled}
                          onChange={(e) => setReportThresholds(prev => ({
                            ...prev,
                            notification_enabled: e.target.checked
                          }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          启用系统通知
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-yellow-800">
                        <p className="font-medium mb-1">注意事项</p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>阈值设置过低可能导致误处理，建议帖子阈值3-10，用户阈值5-15</li>
                          <li>被处理的用户可以通过申诉系统申请恢复</li>
                          <li>管理员可以在申诉管理中审核和处理申诉</li>
                          <li>系统通知将发送给被处理的用户</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>

            {/* 帖子数量限制设置 */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Database className="w-6 h-6 text-purple-600" />
                  <h3 className="text-lg font-medium text-gray-900">帖子数量限制</h3>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  配置用户发布帖子的数量限制和自动下架规则
                </p>
              </div>

              {settingsLoading ? (
                <div className="px-6 py-8 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">加载中...</p>
                </div>
              ) : (
                <div className="px-6 py-6 space-y-6">
                  {/* 普通用户帖子上限 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      普通用户帖子上限
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="10"
                        max="1000"
                        step="10"
                        value={settings.normalUserPostLimit}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          normalUserPostLimit: parseInt(e.target.value) || 100
                        }))}
                        className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-600">
                        当前限制：{settings.normalUserPostLimit}条
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      普通用户可发布的公开帖子数量上限
                    </p>
                  </div>

                  {/* 超级用户帖子上限 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      超级用户帖子上限
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="100"
                        max="5000"
                        step="50"
                        value={settings.superUserPostLimit}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          superUserPostLimit: parseInt(e.target.value) || 500
                        }))}
                        className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-600">
                        当前限制：{settings.superUserPostLimit}条
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      超级用户可发布的公开帖子数量上限
                    </p>
                  </div>

                  {/* 普通用户每日发帖上限 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      普通用户每日发帖上限
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="1"
                        max="50"
                        step="1"
                        value={settings.normalUserDailyPostLimit}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          normalUserDailyPostLimit: parseInt(e.target.value) || 5
                        }))}
                        className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-600">
                        当前限制：{settings.normalUserDailyPostLimit}条/天
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      普通用户每天可发布的帖子数量上限
                    </p>
                  </div>

                  {/* VIP用户每日发帖上限 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      VIP用户每日发帖上限
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        min="5"
                        max="100"
                        step="5"
                        value={settings.vipUserDailyPostLimit}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          vipUserDailyPostLimit: parseInt(e.target.value) || 20
                        }))}
                        className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-sm text-gray-600">
                        当前限制：{settings.vipUserDailyPostLimit}条/天
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      VIP用户每天可发布的帖子数量上限
                    </p>
                  </div>

                  {/* 自动下架开关 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      自动下架设置
                    </label>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.autoArchiveEnabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            autoArchiveEnabled: e.target.checked
                          }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          启用自动下架机制
                        </span>
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      当用户发布新帖子超过上限时，自动下架最早的帖子并转为草稿
                    </p>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <Database className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">帖子数量限制说明</p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>普通用户默认100条帖子上限，超级用户默认500条</li>
                          <li>普通用户默认每日5条发帖限制，VIP用户默认每日20条</li>
                          <li>个人设置的发帖限制优先级高于系统设置</li>
                          <li>发布新帖子时，如果超过上限，最早的帖子会被自动下架</li>
                          <li>下架的帖子会转为草稿，用户登录时可以看到并重新发布</li>
                          <li>草稿在云端保存30天后自动清理</li>
                          <li>用户在发布页面会看到接近上限的提醒</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
                <Button
                  variant="outline"
                  onClick={resetToDefault}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重置默认</span>
                </Button>

                <Button
                  onClick={saveAllSettings}
                  loading={settingsSaving || reportThresholdsLoading}
                  disabled={settingsSaving || reportThresholdsLoading}
                  className="flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>{(settingsSaving || reportThresholdsLoading) ? '保存中...' : '保存所有设置'}</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 创建管理员模态框 */}
      {showCreateAdminModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">创建管理员</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户名 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={newAdminData.username}
                  onChange={(e) => setNewAdminData(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入用户名"
                />
              </div>



              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  密码 <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={newAdminData.password}
                  onChange={(e) => setNewAdminData(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入密码"
                />
              </div>

              <div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm font-medium text-gray-700">角色权限</div>
                  <div className="text-sm text-gray-500 mt-1">
                    普通管理员 - 拥有所有业务权限（除删除其他管理员外）
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateAdminModal(false);
                  setNewAdminData({
                    username: '',
                    password: '',
                    role: 'admin',
                    level: 1,
                    permissions: []
                  });
                }}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={createAdmin}
                className="flex-1"
              >
                创建管理员
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 处理申诉模态框 */}
      {showProcessModal && selectedAppeal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {processAction === 'approved' ? '通过申诉' : '驳回申诉'}
            </h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">申诉内容：</p>
              <p className="text-sm bg-gray-100 p-3 rounded-lg">{selectedAppeal.reason}</p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                处理说明
              </label>
              <textarea
                value={adminReason}
                onChange={(e) => setAdminReason(e.target.value)}
                placeholder={`请说明${processAction === 'approved' ? '通过' : '驳回'}的理由...`}
                className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowProcessModal(false);
                  setSelectedAppeal(null);
                  setAdminReason('');
                }}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={handleAppeal}
                disabled={processingId === selectedAppeal._id}
                className="flex-1"
              >
                {processingId === selectedAppeal._id ? '处理中...' : '确认'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除帖子模态框 */}
      {showDeletePostModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">删除帖子</h3>

            <div className="mb-4">
              <div className="flex items-center mb-3">
                {selectedPost.images && selectedPost.images[0] && (
                  <img
                    src={selectedPost.images[0]}
                    alt={selectedPost.title}
                    className="h-16 w-16 rounded-lg object-cover mr-4"
                  />
                )}
                <div>
                  <div className="font-medium text-gray-900">{selectedPost.title}</div>
                  <div className="text-sm text-gray-500">{selectedPost.description}</div>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  删除原因 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={deleteReason}
                  onChange={(e) => setDeleteReason(e.target.value)}
                  placeholder="请输入删除原因..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                  rows={3}
                />
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      警告
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>删除帖子将同时删除：</p>
                      <ul className="list-disc list-inside mt-1">
                        <li>帖子的所有图片文件</li>
                        <li>所有点赞、收藏、评分记录</li>
                        <li>所有相关的举报和联系记录</li>
                      </ul>
                      <p className="mt-2 font-medium">此操作不可撤销！</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeletePostModal(false);
                  setSelectedPost(null);
                  setDeleteReason('');
                }}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={deletePost}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                disabled={!deleteReason.trim()}
              >
                确认删除
              </Button>
            </div>
          </div>
        </div>
      )}



      {/* 确认模态框 */}
      {showConfirmModal && confirmConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                confirmConfig.type === 'danger'
                  ? 'bg-red-100'
                  : confirmConfig.type === 'warning'
                  ? 'bg-yellow-100'
                  : 'bg-blue-100'
              }`}>
                {confirmConfig.type === 'danger' && (
                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                )}
                {confirmConfig.type === 'warning' && (
                  <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                )}
                {(!confirmConfig.type || confirmConfig.type === 'info') && (
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {confirmConfig.title}
              </h3>
            </div>

            <div className="mb-6">
              <p className="text-gray-600">
                {confirmConfig.message}
              </p>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="flex-1"
              >
                {confirmConfig.cancelText || '取消'}
              </Button>
              <Button
                onClick={handleConfirm}
                className={`flex-1 ${
                  confirmConfig.type === 'danger'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : confirmConfig.type === 'warning'
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {confirmConfig.confirmText || '确认'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 信用分调整模态框 */}
      {showCreditAdjustModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">调整用户信用分</h3>
              <button
                onClick={() => {
                  setShowCreditAdjustModal(false);
                  setCreditAdjustment({ score: 0, reason: '' });
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  调整分数
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCreditAdjustment(prev => ({ ...prev, score: -10 }))}
                    className="px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm"
                  >
                    -10
                  </button>
                  <button
                    onClick={() => setCreditAdjustment(prev => ({ ...prev, score: -5 }))}
                    className="px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm"
                  >
                    -5
                  </button>
                  <input
                    type="number"
                    value={creditAdjustment.score}
                    onChange={(e) => setCreditAdjustment(prev => ({ ...prev, score: parseInt(e.target.value) || 0 }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入分数"
                  />
                  <button
                    onClick={() => setCreditAdjustment(prev => ({ ...prev, score: 5 }))}
                    className="px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm"
                  >
                    +5
                  </button>
                  <button
                    onClick={() => setCreditAdjustment(prev => ({ ...prev, score: 10 }))}
                    className="px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm"
                  >
                    +10
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  调整原因
                </label>
                <textarea
                  value={creditAdjustment.reason}
                  onChange={(e) => setCreditAdjustment(prev => ({ ...prev, reason: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="请输入调整原因..."
                />
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">
                  当前信用分: <span className="font-medium">{userCreditScore?.credit_score || 50}</span>
                </div>
                <div className="text-sm text-gray-600">
                  调整后信用分: <span className={`font-medium ${
                    (userCreditScore?.credit_score || 50) + creditAdjustment.score >= 80 ? 'text-green-600' :
                    (userCreditScore?.credit_score || 50) + creditAdjustment.score >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {Math.max(0, Math.min(200, (userCreditScore?.credit_score || 50) + creditAdjustment.score))}
                  </span>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  分数范围: 0-100分（普通用户），100分以上（超级用户）
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCreditAdjustModal(false);
                  setCreditAdjustment({ score: 0, reason: '' });
                }}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                取消
              </button>
              <button
                onClick={adjustUserCreditScore}
                disabled={!creditAdjustment.reason.trim() || creditAdjustment.score === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                确认调整
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 每日发帖限制调整模态框 */}
      {showDailyLimitModal && searchedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">调整每日发帖限制</h3>
              <button
                onClick={() => setShowDailyLimitModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户信息
                </label>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-sm text-gray-900">{searchedUser.nickname}</div>
                  <div className="text-xs text-gray-500">ID: {searchedUser.user_id}</div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  每日发帖限制
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={dailyPostLimit}
                  onChange={(e) => setDailyPostLimit(parseInt(e.target.value) || 1)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="输入每日发帖限制"
                />
                <p className="text-xs text-gray-500 mt-1">
                  当前限制：{userCreditScore?.daily_post_limit || 5}条/天
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">注意事项</p>
                    <p>个人设置的发帖限制优先级高于系统设置</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowDailyLimitModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleAdjustDailyLimit}
                disabled={vipLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {vipLoading ? '调整中...' : '确认调整'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* VIP设置模态框 */}
      {showVipModal && searchedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">设置VIP用户</h3>
              <button
                onClick={() => setShowVipModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  VIP有效期（天）
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setVipDuration(7)}
                    className={`px-3 py-2 rounded-md text-sm ${vipDuration === 7 ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  >
                    7天
                  </button>
                  <button
                    onClick={() => setVipDuration(30)}
                    className={`px-3 py-2 rounded-md text-sm ${vipDuration === 30 ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  >
                    30天
                  </button>
                  <button
                    onClick={() => setVipDuration(90)}
                    className={`px-3 py-2 rounded-md text-sm ${vipDuration === 90 ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  >
                    90天
                  </button>
                  <input
                    type="number"
                    value={vipDuration}
                    onChange={(e) => setVipDuration(parseInt(e.target.value) || 30)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="自定义天数"
                    min="1"
                    max="3650"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  VIP权益设置
                </label>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">每日发帖限制</label>
                    <input
                      type="number"
                      value={vipBenefits.daily_post_limit}
                      onChange={(e) => setVipBenefits(prev => ({ ...prev, daily_post_limit: parseInt(e.target.value) || 50 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      min="1"
                      max="1000"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">信用分上限</label>
                    <input
                      type="number"
                      value={vipBenefits.credit_score_limit}
                      onChange={(e) => setVipBenefits(prev => ({ ...prev, credit_score_limit: parseInt(e.target.value) || 200 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      min="100"
                      max="1000"
                    />
                  </div>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={vipBenefits.special_badge}
                        onChange={(e) => setVipBenefits(prev => ({ ...prev, special_badge: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">专属VIP标识</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={vipBenefits.priority_support}
                        onChange={(e) => setVipBenefits(prev => ({ ...prev, priority_support: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">优先客服支持</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-3">
                <div className="text-sm text-purple-800">
                  <div>用户: <span className="font-medium">{searchedUser.nickname}</span></div>
                  <div>VIP有效期: <span className="font-medium">{vipDuration}天</span></div>
                  <div>到期时间: <span className="font-medium">
                    {new Date(Date.now() + vipDuration * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN')}
                  </span></div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowVipModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSetVipUser}
                disabled={vipLoading}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {vipLoading ? '设置中...' : '确认设置'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* VIP用户详情管理模态框 */}
      {showVipDetailModal && selectedVipUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Crown className="w-5 h-5 text-purple-600 mr-2" />
                VIP用户管理 - {selectedVipUser.user_info?.nickname || '未知用户'}
              </h3>
              <button
                onClick={() => setShowVipDetailModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* 用户基本信息 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">用户信息</h4>
                <div className="flex items-center space-x-4 mb-4">
                  <img
                    src={selectedVipUser.user_info?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'}
                    alt={selectedVipUser.user_info?.nickname || '未知用户'}
                    className="w-16 h-16 rounded-full"
                  />
                  <div>
                    <h5 className="font-medium text-gray-900">{selectedVipUser.user_info?.nickname || '未知用户'}</h5>
                    <p className="text-sm text-gray-500">ID: {selectedVipUser.user_id}</p>
                  </div>
                </div>
              </div>

              {/* VIP状态信息 */}
              <div className="bg-purple-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">VIP状态</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">当前状态</label>
                    <div className="mt-1 flex items-center">
                      {selectedVipUser.is_vip ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                          <Crown className="w-4 h-4 mr-1" />
                          有效VIP
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                          已过期
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">剩余天数</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVipUser.remaining_days} 天</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">开始时间</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedVipUser.vip_start_time).toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">到期时间</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedVipUser.vip_end_time).toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                </div>
              </div>

              {/* VIP权益 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">VIP权益</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">每日发帖限制</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVipUser.vip_benefits?.daily_post_limit || 5} 条</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">信用分上限</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVipUser.vip_benefits?.credit_score_limit || 100} 分</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">专属VIP标识</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVipUser.vip_benefits?.special_badge ? '✓ 已开启' : '✗ 未开启'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">优先客服支持</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVipUser.vip_benefits?.priority_support ? '✓ 已开启' : '✗ 未开启'}</p>
                  </div>
                </div>
              </div>

              {/* 管理操作 */}
              <div className="bg-yellow-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">管理操作</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <button
                    onClick={() => handleExtendVipUser(selectedVipUser.user_id, 7)}
                    disabled={vipLoading}
                    className="px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                  >
                    延长7天
                  </button>
                  <button
                    onClick={() => handleExtendVipUser(selectedVipUser.user_id, 30)}
                    disabled={vipLoading}
                    className="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
                  >
                    延长30天
                  </button>
                  <button
                    onClick={() => handleExtendVipUser(selectedVipUser.user_id, 90)}
                    disabled={vipLoading}
                    className="px-3 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors"
                  >
                    延长90天
                  </button>
                  <button
                    onClick={async () => {
                      try {
                        setVipLoading(true);
                        const result = await petAPI.removeVipUser({
                          user_id: selectedVipUser.user_id
                        });
                        if (result.success) {
                          showToast.success('移除VIP成功');
                          await loadVipUserList();
                          setShowVipDetailModal(false);
                        } else {
                          showToast.error(result.message || '移除VIP失败');
                        }
                      } catch (error) {
                        console.error('移除VIP失败:', error);
                        showToast.error('移除VIP失败');
                      } finally {
                        setVipLoading(false);
                      }
                    }}
                    disabled={vipLoading}
                    className="px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 transition-colors"
                  >
                    移除VIP
                  </button>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowVipDetailModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
