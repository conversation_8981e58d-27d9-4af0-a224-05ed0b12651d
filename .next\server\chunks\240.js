exports.id=240,exports.ids=[240],exports.modules={93113:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},88462:(e,t,s)=>{Promise.resolve().then(s.bind(s,35659)),Promise.resolve().then(s.bind(s,69596)),Promise.resolve().then(s.bind(s,20603))},35659:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>l,E:()=>c,Y:()=>p});var a=s(10326),r=s(17577),i=s(74131),o=s(16545);let n=(0,r.createContext)(void 0),l=({children:e})=>{let t=(0,i.a)();return t.isLoading&&!t.user?a.jsx(o.SX,{text:"正在初始化..."}):a.jsx(n.Provider,{value:t,children:e})},c=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e},p=({children:e,fallback:t})=>{let{isLoggedIn:s,isLoading:r}=c();return r?a.jsx(o.SX,{text:"验证登录状态..."}):s?a.jsx(a.Fragment,{children:e}):t||a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"需要登录"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录后再访问此页面"})]})})}},69596:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var a=s(10326);s(17577);var r=s(90434),i=s(35047),o=s(95920),n=s(88307),l=s(83855),c=s(6507),p=s(79635),d=s(35659),u=s(28295);let g=()=>{let e=(0,i.usePathname)(),{user:t,isLoggedIn:s}=(0,d.E)(),g=[{href:"/",icon:o.Z,label:"首页",active:"/"===e},{href:"/search",icon:n.Z,label:"搜索",active:"/search"===e},{href:"/upload",icon:l.Z,label:"发布",active:"/upload"===e,requireAuth:!0},{href:"/notifications",icon:c.Z,label:"通知",active:"/notifications"===e,requireAuth:!0},{href:s?"/profile":"/login",icon:p.Z,label:"我",active:"/profile"===e}];return a.jsx("nav",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 md:hidden",children:a.jsx("div",{className:"flex items-center justify-around h-16",children:g.map(e=>{let t=e.icon,i=e.requireAuth&&!s?"/login":e.href,o=e.active;return(0,a.jsxs)(r.default,{href:i,className:(0,u.cn)("flex flex-col items-center justify-center flex-1 h-full transition-colors",o?"text-primary-600":"text-gray-500 hover:text-gray-700"),children:[a.jsx(t,{className:(0,u.cn)("h-5 w-5 mb-1",o&&"text-primary-600")}),a.jsx("span",{className:(0,u.cn)("text-xs font-medium",o&&"text-primary-600"),children:e.label})]},e.label)})})})}},16545:(e,t,s)=>{"use strict";s.d(t,{LL:()=>l,SX:()=>o,gG:()=>n,gb:()=>i});var a=s(10326);s(17577);var r=s(28295);let i=({size:e="md",variant:t="spinner",className:s,text:i})=>{let o={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},n={sm:"text-sm",md:"text-base",lg:"text-lg"};if("spinner"===t)return a.jsx("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("svg",{className:(0,r.cn)("animate-spin text-primary-600",o[e]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i&&a.jsx("p",{className:(0,r.cn)("text-gray-500",n[e]),children:i})]})});if("dots"===t){let t="sm"===e?"w-2 h-2":"md"===e?"w-3 h-3":"w-4 h-4";return a.jsx("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",t),style:{animationDelay:"0ms"}}),a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",t),style:{animationDelay:"150ms"}}),a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",t),style:{animationDelay:"300ms"}})]}),i&&a.jsx("p",{className:(0,r.cn)("text-gray-500",n[e]),children:i})]})})}return"pulse"===t?a.jsx("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[a.jsx("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-pulse",o[e])}),i&&a.jsx("p",{className:(0,r.cn)("text-gray-500",n[e]),children:i})]})}):null},o=({text:e="加载中..."})=>a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsx(i,{size:"lg",text:e})}),n=()=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse",children:[a.jsx("div",{className:"aspect-square bg-gray-200"}),(0,a.jsxs)("div",{className:"p-3 space-y-2",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/4"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})]}),l=()=>a.jsx("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 p-6",children:[a.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full"}),a.jsx("div",{className:"h-6 bg-gray-200 rounded w-32"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-48"}),(0,a.jsxs)("div",{className:"flex space-x-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-8"})]})]}),a.jsx("div",{className:"h-10 bg-gray-200 rounded w-24"})]})})},20603:(e,t,s)=>{"use strict";s.d(t,{C:()=>u,ToastProvider:()=>g});var a=s(10326);s(17577);var r=s(40381),i=s(54659),o=s(91470),n=s(87888),l=s(18019),c=s(28295);let p={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},d=({message:e,type:t,onDismiss:s})=>{let r={success:a.jsx(i.Z,{className:"h-5 w-5 text-green-500"}),error:a.jsx(o.Z,{className:"h-5 w-5 text-red-500"}),warning:a.jsx(n.Z,{className:"h-5 w-5 text-yellow-500"}),info:a.jsx(l.Z,{className:"h-5 w-5 text-blue-500"})};return(0,a.jsxs)("div",{className:(0,c.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[t]),children:[r[t],a.jsx("span",{className:"flex-1 text-sm font-medium",children:e}),a.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})},u={success:e=>{r.Am.custom(t=>a.jsx(d,{message:e,type:"success",onDismiss:()=>r.Am.dismiss(t.id)}),p)},error:e=>{r.Am.custom(t=>a.jsx(d,{message:e,type:"error",onDismiss:()=>r.Am.dismiss(t.id)}),{...p,duration:6e3})},warning:e=>{r.Am.custom(t=>a.jsx(d,{message:e,type:"warning",onDismiss:()=>r.Am.dismiss(t.id)}),p)},info:e=>{r.Am.custom(t=>a.jsx(d,{message:e,type:"info",onDismiss:()=>r.Am.dismiss(t.id)}),p)},loading:e=>r.Am.loading(e,{style:p.style,position:p.position}),dismiss:e=>{r.Am.dismiss(e)},promise:(e,t)=>r.Am.promise(e,t,{style:p.style,position:p.position})},g=()=>a.jsx(r.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},74131:(e,t,s)=>{"use strict";s.d(t,{a:()=>n});var a=s(17577),r=s(41828),i=s(20603),o=s(88673);let n=()=>{let[e,t]=(0,a.useState)(null),[s,n]=(0,a.useState)(!0),[l,c]=(0,a.useState)(!1),p=(0,a.useCallback)(e=>{try{e?(localStorage.setItem("pet_platform_user",JSON.stringify(e)),localStorage.setItem("pet_platform_logged_in","true")):(localStorage.removeItem("pet_platform_user"),localStorage.removeItem("pet_platform_logged_in"))}catch(e){console.error("保存用户状态失败:",e)}},[]),d=(0,a.useCallback)(()=>{try{let e=localStorage.getItem("pet_platform_user"),s=localStorage.getItem("pet_platform_logged_in");if(e&&"true"===s){let s=JSON.parse(e);return t(s),c(!0),s}}catch(e){console.error("恢复用户状态失败:",e)}return null},[]),u=(0,a.useCallback)(async()=>{try{n(!0);let e=d();if(e)try{if(e.email){let s=await r.authAPI.getCurrentUser(e.email);s.success&&s.data?(t(s.data),c(!0),p(s.data)):(t(null),c(!1),p(null))}else console.log("使用本地保存的用户信息（匿名用户）"),t(e),c(!0)}catch(s){console.error("验证用户状态失败:",s),e?(console.log("验证失败，使用本地用户信息"),t(e),c(!0)):(t(null),c(!1),p(null))}else{console.log("没有保存的用户信息，尝试匿名登录");try{let e=await r.authAPI.getCurrentUser();e.success&&e.data?(t(e.data),c(!0),p(e.data)):(t(null),c(!1))}catch(e){console.error("匿名登录失败:",e),t(null),c(!1)}}}catch(e){console.error("检查登录状态失败:",e),t(null),c(!1)}finally{n(!1)}},[d,p]),g=(0,a.useCallback)(async(e,s)=>{try{if(n(!0),!e||!s)return i.C.error("请输入邮箱和密码"),!1;let a=await r.authAPI.loginWithEmail(e,s);if(!a.success)return i.C.error(a.message||"登录失败"),!1;t(a.data),c(!0),p(a.data);try{await (0,o.tD)(a.data._id)}catch(e){console.error("同步草稿失败:",e)}return a.data.isNewUser?i.C.success("欢迎加入宠物交易平台！"):i.C.success("登录成功！"),!0}catch(e){return console.error("登录失败:",e),i.C.error(e.message||"登录失败，请重试"),!1}finally{n(!1)}},[p]),m=(0,a.useCallback)(async()=>{try{return n(!0),await r.authAPI.logout(),t(null),c(!1),p(null),i.C.success("已退出登录"),!0}catch(e){return console.error("退出登录失败:",e),i.C.error("退出登录失败"),!1}finally{n(!1)}},[p]),y=(0,a.useCallback)(async s=>{if(!e)return!1;try{n(!0),console.log("更新用户资料:",s);let a={...e,...s};return t(a),p(a),i.C.success("资料更新成功"),!0}catch(e){return console.error("更新资料失败:",e),i.C.error("更新失败"),!1}finally{n(!1)}},[e,p]),h=(0,a.useCallback)(async()=>{if(l)try{let e=await r.authAPI.getCurrentUser();e.success&&e.data&&(t(e.data),p(e.data))}catch(e){console.error("刷新用户信息失败:",e)}},[l,p]),x=(0,a.useCallback)(async()=>{try{let e=d(),s=await r.authAPI.getCurrentUser(e?.email);s.success&&s.data?(t(s.data),c(!0),p(s.data)):(t(null),c(!1),p(null))}catch(e){console.error("刷新登录状态失败:",e),t(null),c(!1),p(null)}},[p,d]);return(0,a.useEffect)(()=>{(async()=>{let e=d();if(e){t(e),c(!0),n(!1);try{console.log("后台验证用户状态...");let s=await r.authAPI.getCurrentUser(e.email);s.success&&s.data?(t(s.data),p(s.data),console.log("用户状态验证成功")):console.warn("云端验证失败，但保持本地状态")}catch(e){console.error("后台验证失败:",e)}}else await u()})()},[u,d,p]),(0,a.useEffect)(()=>{},[]),{user:e,isLoading:s,isLoggedIn:l,login:g,logout:m,updateProfile:y,refreshUser:h,refreshLoginState:x}}},41828:(e,t,s)=>{"use strict";s.r(t),s.d(t,{activityAPI:()=>d,app:()=>a,auth:()=>r,authAPI:()=>u,db:()=>i,default:()=>f,getImageUrl:()=>m,initCloudBase:()=>o,petAPI:()=>p,uploadFile:()=>x,uploadFileToStatic:()=>y}),s(93386);let a=null,r=null,i=null,o=async()=>(console.log("非浏览器环境，跳过CloudBase初始化"),null),n=()=>{try{let e=localStorage.getItem("pet_platform_user"),t="true"===localStorage.getItem("pet_platform_logged_in");return e&&t}catch(e){return!1}},l=["toggleLike","toggleDislike","toggleBookmark","ratePet","exchangeContact","toggleFollow","reportPost","reportUser","wantPet","submitAppeal","getUserBookmarks","getUserPosts","getUserFollowing","getUserPermissions"],c=async(e,t,s)=>{try{if(console.log(`🚀 开始调用云函数: ${e}.${t}`,s),l.includes(t)){if(!n())throw console.log("❌ 操作需要登录，但用户未登录"),Error("请先登录后再进行此操作");console.log("✅ 用户已登录，可以执行操作")}console.log("\uD83D\uDD0D 检查CloudBase初始化状态...");let a=await o();if(!a)throw console.error("❌ CloudBase未初始化"),Error("CloudBase未初始化");console.log("\uD83D\uDD10 检查用户登录状态..."),r||(r=a.auth()),console.log("✅ CloudBase已初始化，准备调用云函数");let i=null,c=null;try{let e=localStorage.getItem("pet_platform_user");if(e){let t=JSON.parse(e);i=t._id,c=t._id,console.log("✅ 使用注册用户ID:",i)}else console.log("❌ 未找到注册用户信息")}catch(e){console.error("获取注册用户ID失败:",e)}let p=!["getUserInfo","getUserPosts","getUserBookmarks","getUserFollowing"].includes(t),d={name:e,data:{action:t,data:p?{...s,userId:i,openId:c}:{...s,currentUserId:i,openId:c}}};console.log("\uD83D\uDCCB 云函数调用参数:",d),console.log("\uD83D\uDCE1 正在调用云函数...");let u=await a.callFunction(d);if(console.log(`✅ ${e}.${t} 调用成功:`,u),u.result)return console.log("\uD83D\uDCE6 返回result字段:",u.result),u.result;return console.log("\uD83D\uDCE6 返回完整结果:",u),u}catch(s){console.error(`❌ ${e}.${t} 调用失败:`,s),console.error("❌ 错误类型:",typeof s),console.error("❌ 错误构造函数:",s?.constructor?.name),s?.message&&console.error(`❌ 错误消息: ${s.message}`),s?.code&&console.error(`❌ 错误代码: ${s.code}`),s?.name&&console.error(`❌ 错误名称: ${s.name}`),s?.stack&&console.error(`❌ 错误堆栈:`,s.stack);try{console.error("❌ 错误对象JSON:",JSON.stringify(s,null,2))}catch(e){console.error("❌ 无法序列化错误对象:",e),console.error("❌ 错误对象属性:",Object.keys(s)),console.error("❌ 错误对象值:",Object.values(s))}throw s?.response&&console.error("❌ 响应错误:",s.response),s?.request&&console.error("❌ 请求错误:",s.request),s}},p={getPosts:async(e={})=>c("pet-api","getPosts",e),getOptimizedPosts:async(e={})=>c("optimizedPostQuery","query",e),getPostDetail:async e=>c("pet-api","getPostDetail",e),createPost:async e=>c("pet-api","createPost",e),likePost:async e=>c("pet-api","likePost",e),toggleLike:async e=>c("pet-api","toggleLike",e),addLike:async e=>c("pet-api","addLike",e),toggleDislike:async e=>c("pet-api","toggleDislike",e),addDislike:async e=>c("pet-api","addDislike",e),toggleBookmark:async e=>c("pet-api","toggleBookmark",e),bookmarkPost:async e=>c("pet-api","bookmarkPost",e),exchangeContact:async e=>c("pet-api","exchangeContact",e),ratePost:async e=>c("pet-api","ratePost",e),toggleFollow:async e=>c("pet-api","toggleFollow",e),getCategories:async()=>c("pet-api","getCategories"),ratePet:async e=>c("pet-api","ratePet",e),reportPost:async e=>c("pet-api","reportPost",e),getUserBookmarks:async(e={})=>c("pet-api","getUserBookmarks",e),getUserPosts:async(e={})=>c("pet-api","getUserPosts",e),getUserFollowing:async(e={})=>c("pet-api","getUserFollowing",e),getUserFollowers:async(e={})=>c("pet-api","getUserFollowers",e),getUserNotifications:async(e={})=>c("pet-api","getUserNotifications",e),markNotificationRead:async e=>c("pet-api","markNotificationRead",e),deleteNotification:async e=>c("pet-api","deleteNotification",e),bulkDeleteNotifications:async e=>c("pet-api","bulkDeleteNotifications",e),updateProfile:async e=>c("pet-api","updateProfile",e),sendContactNotification:async e=>c("pet-api","sendContactNotification",e),getReports:async(e={})=>c("pet-api","getReports",e),handleReport:async e=>c("pet-api","handleReport",e),banUser:async e=>c("pet-api","banUser",e),getUserInfo:async e=>c("user-management-api","getUserInfo",e),uploadToStatic:async e=>c("pet-api","uploadToStatic",e),updateAvatar:async e=>c("pet-api","updateAvatar",e),getImage:async e=>c("pet-api","getImage",e),blockUser:async e=>c("pet-api","blockUser",e),unblockUser:async e=>c("pet-api","unblockUser",e),getBlockedUsers:async(e={})=>c("pet-api","getBlockedUsers",e),reportUser:async e=>c("pet-api","reportUser",e),submitAppeal:async e=>c("pet-api","submitAppeal",e),getUserPermissions:async e=>c("pet-api","getUserPermissions",e),getTargetUserPermissions:async e=>c("pet-api","getTargetUserPermissions",e),updateUserPermissions:async e=>c("pet-api","updateUserPermissions",e),getAppeals:async(e={})=>c("pet-api","getAppeals",e),handleAppeal:async e=>c("pet-api","handleAppeal",e),adminLogin:async e=>c("pet-api","adminLogin",e),getAdmins:async(e={})=>c("pet-api","getAdmins",e),createAdmin:async e=>c("pet-api","createAdmin",e),updateAdmin:async e=>c("pet-api","updateAdmin",e),deleteAdmin:async e=>c("pet-api","deleteAdmin",e),getAds:async e=>c("pet-api","getAds",e||{}),getAdPositions:async e=>c("pet-api","getAdPositions",e||{}),createAd:async e=>c("pet-api","createAd",e),updateAd:async e=>c("pet-api","updateAd",e),deleteAd:async e=>c("pet-api","deleteAd",e),getAdStatistics:async e=>c("pet-api","getAdStatistics",e||{}),updatePostPriority:async e=>c("pet-api","updatePostPriority",e),getPostQualityScore:async e=>c("pet-api","getPostQualityScore",e),batchUpdatePostPriority:async e=>c("pet-api","batchUpdatePostPriority",e),getPostsByPriority:async e=>c("pet-api","getPostsByPriority",e||{}),batchUpdateAllPostsQuality:async()=>c("pet-api","batchUpdateAllPostsQuality",{}),createActivity:async e=>c("pet-api","createActivity",e),getActivities:async e=>c("pet-api","getActivities",e||{}),getActivityDetail:async e=>c("pet-api","getActivityDetail",e),updateActivity:async e=>c("pet-api","updateActivity",e),deleteActivity:async e=>c("pet-api","deleteActivity",e),voteInActivity:async e=>c("pet-api","voteInActivity",e),addActivityComment:async e=>c("pet-api","addActivityComment",e),getActivityComments:async e=>c("pet-api","getActivityComments",e),getSystemConfig:async()=>c("pet-api","getSystemConfig",{}),updateSystemConfig:async e=>c("pet-api","updateSystemConfig",e),getReportThresholds:async()=>c("pet-api","getReportThresholds",{}),updateReportThresholds:async e=>c("pet-api","updateReportThresholds",e),getPermissions:async()=>c("pet-api","getPermissions",{}),getUserRatedPosts:async e=>c("pet-api","getUserRatedPosts",e),updatePostStatus:async e=>c("pet-api","updatePostStatus",e),deletePost:async e=>c("pet-api","deleteMyPost",e),adminDeletePost:async e=>c("pet-api","adminDeletePost",e),deleteCloudFile:async e=>c("pet-api","deleteCloudFile",e),getPostsForAdmin:async(e={})=>c("pet-api","getPostsForAdmin",e),getUserCreditScore:async e=>c("pet-api","getUserCreditScore",{user_id:e.userId}),updateUserCreditScore:async e=>c("pet-api","updateUserCreditScore",e),updateUserDailyPostLimit:async e=>c("pet-api","updateUserDailyPostLimit",e),setVipUser:async e=>c("pet-api","setVipUser",e),getVipUserInfo:async e=>c("pet-api","getVipUserInfo",e),removeVipUser:async e=>c("pet-api","removeVipUser",e),getVipUserList:async e=>c("pet-api","getVipUserList",e),setUserSuperStatus:async e=>c("user-management-api","setUserSuperStatus",e),getPostExposureScore:async e=>c("pet-api","getPostExposureScore",e),updatePostExposureScore:async e=>c("pet-api","updatePostExposureScore",e),getCreditScoreHistory:async e=>c("pet-api","getCreditScoreHistory",e),getVipUserInfo:async e=>c("vip-api","getVipUserInfo",e),setVipUser:async e=>c("vip-api","setVipUser",e),removeVipUser:async e=>c("vip-api","removeVipUser",e),updateVipUser:async e=>c("vip-api","updateVipUser",e),checkVipExpiry:async e=>c("vip-api","checkVipExpiry",e),getVipUserList:async e=>c("vip-api","getVipUserList",e),getExposureRanking:async e=>c("pet-api","getExposureRanking",e),setSuperUser:async e=>c("pet-api","setSuperUser",e),processReportPenalty:async e=>c("pet-api","processReportPenalty",e),handleAppeal:async e=>c("pet-api","handleAppeal",e),togglePostPin:async e=>c("pet-api","togglePostPin",e),getSystemSettings:async()=>c("pet-api","getSystemSettings",{}),updateSystemSettings:async e=>c("pet-api","updateSystemSettings",e),checkPostLimit:async e=>c("pet-api","checkPostLimit",e),archiveOldPosts:async e=>c("pet-api","archiveOldPosts",e),getUserDrafts:async e=>c("pet-api","getUserDrafts",e),deleteUserDraft:async e=>c("pet-api","deleteUserDraft",e)},d={getActiveActivities:async()=>c("pet-api","getActiveActivities"),getActivities:async(e={})=>c("pet-api","getActivities",e),getSystemConfig:async()=>c("pet-api","getSystemConfig"),getDashboardStats:async()=>c("pet-api","getDashboardStats")},u={sendVerificationCode:async(e,t="register")=>c("email-auth","sendVerificationCode",{email:e,type:t}),verifyCode:async(e,t,s="register")=>c("email-auth","verifyCode",{email:e,code:t,type:s}),registerWithEmail:async(e,t,s,a)=>c("email-auth","registerWithEmail",{email:e,password:t,nickname:s,verificationCode:a}),loginWithEmail:async(e,t)=>c("email-auth","loginWithEmail",{email:e,password:t}),resetPassword:async(e,t,s)=>c("email-auth","resetPassword",{email:e,verificationCode:t,newPassword:s}),changePassword:async(e,t,s,a)=>c("email-auth","changePassword",{email:e,verificationCode:t,oldPassword:s,newPassword:a}),getCurrentUser:async e=>c("user-auth","getCurrentUser",e?{email:e}:{}),logout:async()=>c("user-auth","logout")},g=async(e,t=800,s=.8)=>new Promise(a=>{let r=document.createElement("canvas"),i=r.getContext("2d"),o=new Image;o.onload=()=>{let{width:n,height:l}=o,c=n,p=l,d=s;n>2e3||l>2e3?(p=l*(c=Math.min(n,t))/n,d=.8):n>t?(p=l*t/n,c=t,d=s):d=Math.max(.9,s),r.width=c,r.height=p,i?.drawImage(o,0,0,c,p),r.toBlob(t=>{if(t){let s=new File([t],e.name,{type:"image/jpeg",lastModified:Date.now()}),r=Math.round((1-s.size/e.size)*100);console.log(`图片压缩完成: ${e.size} -> ${s.size} (${r}% 减少)`),console.log(`压缩参数: ${n}x${l} -> ${c}x${p}, 质量: ${Math.round(100*d)}%`),a(s)}else a(e)},"image/jpeg",d)},o.src=URL.createObjectURL(e)}),m=async e=>{try{let t=await o();if(!t)throw Error("CloudBase未初始化");let s=await t.callFunction({name:"pet-api",data:{action:"getImage",data:{fileId:e}}});if(s.result?.success&&s.result?.data?.url)return s.result.data.url;return console.error("获取图片URL失败:",s.result?.message),"/placeholder-image.png"}catch(e){return console.error("获取图片URL失败:",e),"/placeholder-image.png"}},y=async e=>{try{console.log("开始上传文件到静态托管:",e.name,"原始大小:",e.size);let t=e;e.type.startsWith("image/")&&(t=await g(e));let s=Date.now(),a=Math.random().toString(36).substring(2),r=e.name.split(".").pop(),i=`${s}_${a}.${r}`;console.log("压缩后大小:",t.size);let n=await h(t),l=await o();if(!l)throw Error("CloudBase未初始化");let c=await l.callFunction({name:"pet-api",data:{action:"uploadToStatic",data:{fileName:i,fileData:n,contentType:t.type}}});if(c.result?.success&&c.result?.data?.url)return console.log("文件上传成功，URL:",c.result.data.url),c.result.data.url;throw Error(c.result?.message||"上传失败")}catch(e){throw console.error("文件上传失败:",e),e}},h=e=>new Promise((t,s)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>{t(a.result.split(",")[1])},a.onerror=e=>s(e)}),x=async e=>await y(e),f={petAPI:p,authAPI:u,activityAPI:d,uploadFile:x}},88673:(e,t,s)=>{"use strict";s.d(t,{DO:()=>o,Oe:()=>r,XD:()=>n,deleteDraft:()=>l,tD:()=>c}),s(41828);let a="petDrafts",r=()=>{try{return[]}catch(e){return console.error("获取草稿失败:",e),[]}},i=async e=>{let t=[];for(let s of e)if(s instanceof File)try{let e=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});t.push(e)}catch(e){console.error("转换图片失败:",e),t.push("https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")}else"string"==typeof s&&t.push(s);return t},o=async e=>{try{throw Error("无法在服务器端保存草稿")}catch(e){throw console.error("保存草稿失败:",e),Error("保存草稿失败")}},n=async(e,t)=>{try{let s=r(),o=s.findIndex(t=>t.id===e);if(-1!==o){let e=await i(t.images);s[o]={...s[o],...t,images:e,updated_at:new Date().toISOString()},localStorage.setItem(a,JSON.stringify(s))}}catch(e){throw console.error("更新草稿失败:",e),Error("更新草稿失败")}},l=e=>{try{let t=r().filter(t=>t.id!==e);localStorage.setItem(a,JSON.stringify(t))}catch(e){throw console.error("删除草稿失败:",e),Error("删除草稿失败")}},c=async e=>{try{return}catch(e){console.error("同步云端草稿失败:",e)}}},28295:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i,uf:()=>o,vV:()=>n});var a=s(41135),r=s(31009);function i(...e){return(0,r.m6)((0,a.W)(e))}function o(e){return e<1e3?e.toString():e<1e4?`${(e/1e3).toFixed(1)}k`:e<1e5?`${(e/1e4).toFixed(1)}w`:`${Math.floor(e/1e4)}w`}function n(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}},16953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,generateViewport:()=>c,metadata:()=>l});var a=s(19510),r=s(68570);let i=(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\auth\AuthProvider.tsx#AuthProvider`);(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\auth\AuthProvider.tsx#useAuthContext`),(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\auth\AuthProvider.tsx#RequireAuth`),(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\ui\Toast.tsx#showToast`);let o=(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\ui\Toast.tsx#ToastProvider`),n=(0,r.createProxy)(String.raw`D:\web-cloudbase-project\src\components\layout\BottomNav.tsx#default`);s(5023);let l={title:"宠物交易平台 - TikTok风格的现代化宠物交易平台",description:"一个基于腾讯云开发的现代化宠物交易平台，采用TikTok风格的UI设计和完整的社交功能",keywords:["宠物","交易","平台","TikTok","社交","云开发"],authors:[{name:"Pet Trading Platform Team"}],manifest:"/pet-platform-v2/manifest.json",icons:{icon:"/pet-platform-v2/favicon.ico",apple:"/pet-platform-v2/apple-touch-icon.png"},openGraph:{title:"宠物交易平台",description:"TikTok风格的现代化宠物交易平台",type:"website",locale:"zh_CN",siteName:"宠物交易平台"},twitter:{card:"summary_large_image",title:"宠物交易平台",description:"TikTok风格的现代化宠物交易平台"}};function c(){return{width:"device-width",initialScale:1,themeColor:"#3b82f6"}}function p({children:e}){return(0,a.jsxs)("html",{lang:"zh-CN",children:[a.jsx("head",{}),a.jsx("body",{className:"font-sans",children:(0,a.jsxs)(i,{children:[a.jsx("div",{className:"pb-16 md:pb-0",children:e}),a.jsx(n,{}),a.jsx(o,{})]})})]})}},5023:()=>{}};