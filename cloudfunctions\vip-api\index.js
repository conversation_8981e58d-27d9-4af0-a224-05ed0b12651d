const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * VIP用户管理云函数
 */
exports.main = async (event, context) => {
  const { action, data, ...restParams } = event;
  const { OPENID } = cloud.getWXContext();

  // 如果没有data字段，说明参数直接在event中
  const actualData = data || restParams;

  // 获取OPENID，优先使用前端传递的openId，否则使用云开发的OPENID
  const finalOpenId = actualData?.openId || event?.openId || OPENID;
  console.log('VIP API - 获取到的OPENID:', finalOpenId);

  try {
    switch (action) {
      // 获取VIP用户信息
      case 'getVipUserInfo':
        return await getVipUserInfo(actualData, finalOpenId);

      // 设置用户为VIP
      case 'setVipUser':
        return await setVipUser(actualData, finalOpenId);

      // 移除VIP用户
      case 'removeVipUser':
        return await removeVipUser(actualData, finalOpenId);

      // 更新VIP用户信息
      case 'updateVipUser':
        return await updateVipUser(actualData, finalOpenId);

      // 检查VIP用户过期状态
      case 'checkVipExpiry':
        return await checkVipExpiry(actualData, finalOpenId);

      // 获取VIP用户列表
      case 'getVipUserList':
        return await getVipUserList(actualData, finalOpenId);

      default:
        return {
          success: false,
          message: `未知的操作: ${action}`
        };
    }
  } catch (error) {
    console.error('VIP API 错误:', error);
    return {
      success: false,
      message: error.message || 'VIP API 调用失败'
    };
  }
};

// 验证管理员权限
async function checkAdminPermission(openId) {
  try {
    const adminResult = await db.collection('admins').where({
      user_id: openId,
      status: 'active'
    }).get();

    return adminResult.data.length > 0;
  } catch (error) {
    console.error('验证管理员权限失败:', error);
    return false;
  }
}

// 获取VIP用户信息
async function getVipUserInfo({ user_id }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限访问');
    }

    // 查询VIP用户信息
    const vipResult = await db.collection('vip_users').where({
      user_id: user_id
    }).get();

    if (vipResult.data.length === 0) {
      return {
        success: true,
        data: {
          user_id: user_id,
          is_vip: false,
          vip_start_time: null,
          vip_end_time: null,
          vip_benefits: {
            daily_post_limit: 5,
            credit_score_limit: 100
          }
        }
      };
    }

    const vipInfo = vipResult.data[0];
    const now = new Date();
    const isExpired = vipInfo.vip_end_time && new Date(vipInfo.vip_end_time) < now;

    return {
      success: true,
      data: {
        ...vipInfo,
        is_vip: !isExpired,
        is_expired: isExpired
      }
    };
  } catch (error) {
    console.error('获取VIP用户信息失败:', error);
    return {
      success: false,
      message: error.message || '获取VIP用户信息失败'
    };
  }
}

// 设置用户为VIP
async function setVipUser({ user_id, duration_days, benefits }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const now = new Date();
    const endTime = new Date(now.getTime() + duration_days * 24 * 60 * 60 * 1000);

    // 默认VIP权益
    const defaultBenefits = {
      daily_post_limit: 50,
      credit_score_limit: 200,
      special_badge: true,
      priority_support: true
    };

    const vipData = {
      user_id: user_id,
      vip_start_time: now.toISOString(),
      vip_end_time: endTime.toISOString(),
      vip_benefits: { ...defaultBenefits, ...benefits },
      created_by: openId,
      created_at: now.toISOString(),
      updated_at: now.toISOString()
    };

    // 检查是否已存在VIP记录
    const existingResult = await db.collection('vip_users').where({
      user_id: user_id
    }).get();

    if (existingResult.data.length > 0) {
      // 更新现有记录
      await db.collection('vip_users').doc(existingResult.data[0]._id).update({
        vip_start_time: now.toISOString(),
        vip_end_time: endTime.toISOString(),
        vip_benefits: vipData.vip_benefits,
        updated_at: now.toISOString(),
        updated_by: openId
      });
    } else {
      // 创建新记录
      await db.collection('vip_users').add(vipData);
    }

    // 同时更新用户信用分表中的超级用户状态
    await updateUserSuperStatus(user_id, true, vipData.vip_benefits.daily_post_limit);

    return {
      success: true,
      data: vipData,
      message: '设置VIP用户成功'
    };
  } catch (error) {
    console.error('设置VIP用户失败:', error);
    return {
      success: false,
      message: error.message || '设置VIP用户失败'
    };
  }
}

// 移除VIP用户
async function removeVipUser({ user_id }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const now = new Date();

    // 更新VIP记录为过期
    const vipResult = await db.collection('vip_users').where({
      user_id: user_id
    }).get();

    if (vipResult.data.length > 0) {
      await db.collection('vip_users').doc(vipResult.data[0]._id).update({
        vip_end_time: now.toISOString(),
        updated_at: now.toISOString(),
        updated_by: openId,
        removed_by: openId
      });
    }

    // 同时更新用户信用分表中的超级用户状态
    await updateUserSuperStatus(user_id, false, 5);

    return {
      success: true,
      message: '移除VIP用户成功'
    };
  } catch (error) {
    console.error('移除VIP用户失败:', error);
    return {
      success: false,
      message: error.message || '移除VIP用户失败'
    };
  }
}

// 更新VIP用户信息
async function updateVipUser({ user_id, duration_days, benefits }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const now = new Date();

    // 查询现有VIP记录
    const vipResult = await db.collection('vip_users').where({
      user_id: user_id
    }).get();

    if (vipResult.data.length === 0) {
      throw new Error('用户不是VIP用户');
    }

    const vipInfo = vipResult.data[0];
    const updateData = {
      updated_at: now.toISOString(),
      updated_by: openId
    };

    // 如果提供了新的持续时间，更新结束时间
    if (duration_days !== undefined) {
      const endTime = new Date(now.getTime() + duration_days * 24 * 60 * 60 * 1000);
      updateData.vip_end_time = endTime.toISOString();
    }

    // 如果提供了新的权益，更新权益
    if (benefits) {
      updateData.vip_benefits = { ...vipInfo.vip_benefits, ...benefits };
    }

    await db.collection('vip_users').doc(vipInfo._id).update(updateData);

    // 同时更新用户信用分表中的信息
    if (updateData.vip_benefits && updateData.vip_benefits.daily_post_limit) {
      await updateUserSuperStatus(user_id, true, updateData.vip_benefits.daily_post_limit);
    }

    return {
      success: true,
      data: { ...vipInfo, ...updateData },
      message: '更新VIP用户成功'
    };
  } catch (error) {
    console.error('更新VIP用户失败:', error);
    return {
      success: false,
      message: error.message || '更新VIP用户失败'
    };
  }
}

// 检查VIP用户过期状态
async function checkVipExpiry({ user_id }, openId) {
  try {
    const now = new Date();

    // 查询VIP用户信息
    const vipResult = await db.collection('vip_users').where({
      user_id: user_id || openId
    }).get();

    if (vipResult.data.length === 0) {
      return {
        success: true,
        data: {
          is_vip: false,
          is_expired: false,
          message: '用户不是VIP用户'
        }
      };
    }

    const vipInfo = vipResult.data[0];
    const endTime = new Date(vipInfo.vip_end_time);
    const isExpired = endTime < now;

    // 如果已过期，自动降级为普通用户
    if (isExpired) {
      await updateUserSuperStatus(vipInfo.user_id, false, 5);
      
      // 记录过期日志
      await db.collection('vip_users').doc(vipInfo._id).update({
        expired_at: now.toISOString(),
        auto_downgraded: true,
        updated_at: now.toISOString()
      });
    }

    return {
      success: true,
      data: {
        is_vip: !isExpired,
        is_expired: isExpired,
        vip_end_time: vipInfo.vip_end_time,
        remaining_days: isExpired ? 0 : Math.ceil((endTime - now) / (24 * 60 * 60 * 1000)),
        vip_benefits: vipInfo.vip_benefits
      }
    };
  } catch (error) {
    console.error('检查VIP过期状态失败:', error);
    return {
      success: false,
      message: error.message || '检查VIP过期状态失败'
    };
  }
}

// 获取VIP用户列表
async function getVipUserList({ limit = 20, offset = 0, status = 'all' }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限访问');
    }

    const now = new Date();
    let query = db.collection('vip_users');

    // 根据状态筛选
    if (status === 'active') {
      query = query.where({
        vip_end_time: db.command.gt(now.toISOString())
      });
    } else if (status === 'expired') {
      query = query.where({
        vip_end_time: db.command.lte(now.toISOString())
      });
    }

    const result = await query
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    // 获取用户基本信息
    const vipUsers = [];
    for (const vipUser of result.data) {
      try {
        const userResult = await db.collection('users').where({
          user_id: vipUser.user_id
        }).get();

        const userInfo = userResult.data[0] || { nickname: '未知用户', avatar: '' };
        const isExpired = new Date(vipUser.vip_end_time) < now;

        vipUsers.push({
          ...vipUser,
          user_info: userInfo,
          is_vip: !isExpired,
          is_expired: isExpired,
          remaining_days: isExpired ? 0 : Math.ceil((new Date(vipUser.vip_end_time) - now) / (24 * 60 * 60 * 1000))
        });
      } catch (error) {
        console.error('获取用户信息失败:', error);
        vipUsers.push({
          ...vipUser,
          user_info: { nickname: '获取失败', avatar: '' },
          is_vip: false,
          is_expired: true,
          remaining_days: 0
        });
      }
    }

    return {
      success: true,
      data: vipUsers,
      total: result.data.length
    };
  } catch (error) {
    console.error('获取VIP用户列表失败:', error);
    return {
      success: false,
      message: error.message || '获取VIP用户列表失败'
    };
  }
}

// 辅助函数：更新用户超级用户状态
async function updateUserSuperStatus(userId, isSuperUser, dailyPostLimit) {
  try {
    // 查询现有信用分记录
    const creditResult = await db.collection('user_credit_scores').where({
      user_id: userId
    }).get();

    const updateData = {
      is_super_user: isSuperUser,
      daily_post_limit: dailyPostLimit,
      last_updated: new Date().toISOString()
    };

    if (creditResult.data.length > 0) {
      // 更新现有记录
      await db.collection('user_credit_scores').doc(creditResult.data[0]._id).update(updateData);
    } else {
      // 创建新记录
      await db.collection('user_credit_scores').add({
        user_id: userId,
        credit_score: 50,
        ...updateData
      });
    }

    return true;
  } catch (error) {
    console.error('更新用户超级用户状态失败:', error);
    return false;
  }
}
