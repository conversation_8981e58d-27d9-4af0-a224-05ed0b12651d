"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[423],{88941:function(e,s,t){t.d(s,{AuthProvider:function(){return c},E:function(){return o},Y:function(){return d}});var a=t(57437),r=t(2265),l=t(98734),n=t(31215);let i=(0,r.createContext)(void 0),c=e=>{let{children:s}=e,t=(0,l.a)();return t.isLoading&&!t.user?(0,a.jsx)(n.SX,{text:"正在初始化..."}):(0,a.jsx)(i.Provider,{value:t,children:s})},o=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e},d=e=>{let{children:s,fallback:t}=e,{isLoggedIn:r,isLoading:l}=o();return l?(0,a.jsx)(n.SX,{text:"验证登录状态..."}):r?(0,a.jsx)(a.Fragment,{children:s}):t||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"需要登录"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"请先登录后再访问此页面"})]})})}},89841:function(e,s,t){t.d(s,{Z:function(){return u}});var a=t(57437),r=t(2265),l=t(27648),n=t(32489),i=t(92369),c=t(83774),o=t(88997),d=t(68661),x=t(28819),m=e=>{let{src:s,alt:t,className:l,placeholder:n,fallback:i="/images/placeholder.jpg",onLoad:c,onError:o,priority:m=!1,quality:u=75,sizes:h}=e,[p,g]=(0,r.useState)("loading"),[f,b]=(0,r.useState)(n||""),y=(0,r.useRef)(null),{ref:v,inView:j}=(0,x.YD)({threshold:0,rootMargin:"200px",triggerOnce:!0,skip:m}),w=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:75;if(!e)return e;if(e.includes(".myqcloud.com")||e.includes(".cos.")){let t=e.includes("?")?"&":"?";return"".concat(e).concat(t,"imageMogr2/quality/").concat(s,"/format/webp")}return e.includes(".tcloudbaseapp.com"),e},N=e=>new Promise((s,t)=>{let a=new Image;a.onload=()=>{g("loaded"),b(e),null==c||c(),s()},a.onerror=()=>{g("error"),b(i),null==o||o(),t(Error("Image load failed"))},a.src=e});(0,r.useEffect)(()=>{if(m||j){let e=w(s,u),t=performance.now();N(e).then(()=>{let e=performance.now()-t;console.log("图片加载完成: ".concat(s,", 耗时: ").concat(e.toFixed(2),"ms"))}).catch(()=>{e!==s?(console.warn("优化图片加载失败，尝试原图:",s),N(s).catch(()=>{console.error("原图加载也失败:",s),g("error"),b(i)})):(g("error"),b(i))})}},[j,m,s,u,i,c,o]);let k=(0,r.useCallback)(()=>{if(!m&&!j&&"loading"===p){let e=w(s,u);console.log("鼠标悬停预加载:",s),N(e).catch(()=>{})}},[m,j,p,s,u]);return(0,a.jsxs)("div",{ref:v,className:"relative overflow-hidden w-full h-full",onMouseEnter:k,children:["loading"===p&&!f&&(0,a.jsx)("div",{className:(0,d.cn)("bg-gray-200 animate-pulse flex items-center justify-center w-full h-full",l),children:(0,a.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})})}),"error"===p&&(0,a.jsx)("div",{className:(0,d.cn)("bg-gray-100 flex items-center justify-center text-gray-400 w-full h-full",l),children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),("loaded"===p||f)&&(0,a.jsx)("img",{ref:y,src:f,alt:t,className:(0,d.cn)("transition-opacity duration-300","loaded"===p?"opacity-100":"opacity-0",l),sizes:h,loading:m?"eager":"lazy",decoding:"async"}),"loading"===p&&f&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]})},u=e=>{var s,t,r;let{post:x,className:u,isDraft:h=!1,showRemoveFromFavorites:p=!1,onRemoveFromFavorites:g}=e,f=(null===(s=x.images)||void 0===s?void 0:s[0])||x.image||"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",b=(e=>{switch(e){case"selling":case"sell":return{label:"出售",color:"bg-green-500"};case"adoption":case"adopt":return{label:"领养",color:"bg-blue-500"};case"breeding":case"mate":return{label:"配种",color:"bg-orange-500"};case"lost":return{label:"寻宠",color:"bg-red-500"};case"found":return{label:"招领",color:"bg-yellow-500"};case"wanted":return{label:"求购",color:"bg-purple-500"};case"sharing":return{label:"分享",color:"bg-indigo-500"};default:return null}})(x.type),y=(0,a.jsxs)("div",{className:(0,d.cn)("bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 h-full flex flex-col",!h&&"cursor-pointer",u),children:[(0,a.jsxs)("div",{className:"relative w-full overflow-hidden",style:{height:"200px"},children:[(0,a.jsx)(m,{src:f,alt:x.breed||"宠物图片",className:"w-full h-full object-cover rounded-t-lg",fallback:"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",quality:80,sizes:"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"}),h&&(0,a.jsx)("div",{className:"absolute top-2 left-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium",children:"待发布"}),!h&&b&&(0,a.jsx)("div",{className:(0,d.cn)("absolute top-2 left-2 text-white text-xs px-2 py-1 rounded-full font-medium",b.color),children:b.label}),x.images&&x.images.length>1&&(0,a.jsxs)("div",{className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full",children:["1/",x.images.length]}),p&&(0,a.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),null==g||g()},className:"absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1.5 rounded-full transition-colors",title:"从收藏中移除",children:(0,a.jsx)(n.Z,{className:"h-3 w-3"})})]}),(0,a.jsx)("div",{className:"p-3 flex-1 flex flex-col justify-center",style:{minHeight:"72px"},children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-full",children:[(0,a.jsxs)("div",{className:"flex flex-col justify-center flex-1 min-w-0 pr-2",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 text-sm leading-tight truncate mb-1",children:x.breed||"未知品种"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-600",children:[(0,a.jsx)(i.Z,{className:"h-3 w-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:(null===(t=x.user)||void 0===t?void 0:t.nickname)||(null===(r=x.author)||void 0===r?void 0:r.nickname)||"匿名用户"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col justify-center items-end flex-shrink-0",children:[(x.location||x.city)&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500 mb-1",children:[(0,a.jsx)(c.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"truncate max-w-20",title:x.location||"#".concat(x.province).concat(x.city),children:x.city||(e=>{if(!e)return"未知地区";let s=e.split(/[省市区县]/);return s[s.length-2]||e})(x.location)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[(0,a.jsx)(o.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:x.likes||0})]})]})]})})]});return h?y:(0,a.jsx)(l.default,{href:"/post/detail?id=".concat(x._id),children:y})}},47465:function(e,s,t){t.d(s,{Z:function(){return E}});var a=t(57437),r=t(2265),l=t(82718),n=t(92369),i=t(47692),c=t(32489),o=t(58293),d=t(56334),x=t(88941),m=t(98702),u=t(92827),h=t(89345),p=t(66337),g=t(98734),f=t(98011),b=t(68661),y=e=>{let{isOpen:s,onClose:t,onSuccess:l}=e,{login:n,isLoading:i}=(0,g.a)(),[o,x]=(0,r.useState)("main"),[y,v]=(0,r.useState)({email:"",password:"",nickname:"",confirmPassword:"",verificationCode:""}),[j,w]=(0,r.useState)({}),[N,k]=(0,r.useState)(!1),[C,Z]=(0,r.useState)(0),I=()=>{v({email:"",password:"",nickname:"",confirmPassword:"",verificationCode:""}),w({}),x("main"),k(!1),Z(0)},S=()=>{I(),t()},z=()=>{let e={};return("email"===o||"register"===o)&&(y.email.trim()?(0,b.vV)(y.email)||(e.email="请输入有效的邮箱地址"):e.email="请输入邮箱地址",y.password.trim()?y.password.length<6&&(e.password="密码至少6位"):e.password="请输入密码"),"register"===o&&(y.nickname.trim()?y.nickname.length<2?e.nickname="昵称至少需要2个字符":y.nickname.length>20&&(e.nickname="昵称不能超过20个字符"):e.nickname="请输入昵称",y.confirmPassword.trim()?y.password!==y.confirmPassword&&(e.confirmPassword="两次密码输入不一致"):e.confirmPassword="请确认密码"),w(e),0===Object.keys(e).length},_=async()=>{console.log("微信登录")},P=async()=>{if(!y.email.trim()){w({email:"请输入邮箱地址"});return}if(!(0,b.vV)(y.email)){w({email:"请输入有效的邮箱地址"});return}try{k(!0);let e=await f.authAPI.sendVerificationCode(y.email,"register");if(e.success){Z(60);let e=setInterval(()=>{Z(s=>s<=1?(clearInterval(e),0):s-1)},1e3)}else w({verificationCode:e.message})}catch(e){w({verificationCode:e.message||"发送验证码失败"})}finally{k(!1)}},T=async()=>{z()&&await n(y.email,y.password)&&(null==l||l(),S())},E=async()=>{if(z())try{let e=await f.authAPI.registerWithEmail(y.email,y.password,y.nickname,y.verificationCode);e.success?await n(y.email,y.password)&&(null==l||l(),S()):w({verificationCode:e.message})}catch(e){w({verificationCode:e.message||"注册失败"})}},A=(e,s)=>{v(t=>({...t,[e]:s})),j[e]&&w(s=>({...s,[e]:""}))};return(0,a.jsx)(m.u_,{isOpen:s,onClose:S,size:"sm",children:(0,a.jsxs)(m.fe,{children:[(0,a.jsx)("button",{onClick:S,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(c.Z,{className:"w-5 h-5"})}),"main"===o&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"欢迎回来"}),(0,a.jsx)("p",{className:"text-gray-600",children:"选择登录方式继续使用"})]}),(0,a.jsxs)(d.Z,{onClick:_,className:"w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg flex items-center justify-center space-x-2",disabled:i,children:[(0,a.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.91-4.624-6.364-4.624zm-2.44 5.738a.868.868 0 0 1-.869-.855c0-.472.39-.856.869-.856s.868.384.868.856a.868.868 0 0 1-.868.855zm4.928 0a.868.868 0 0 1-.868-.855c0-.472.39-.856.868-.856s.869.384.869.856a.868.868 0 0 1-.869.855z"}),(0,a.jsx)("path",{d:"M24 14.388c0-3.14-2.956-5.69-6.594-5.69-3.638 0-6.594 2.55-6.594 5.69 0 3.14 2.956 5.69 6.594 5.69a7.842 7.842 0 0 0 2.208-.32.671.671 0 0 1 .556.075l1.462.855a.25.25 0 0 0 .128.042.226.226 0 0 0 .223-.227.166.166 0 0 0-.037-.164l-.3-1.14a.454.454 0 0 1 .164-.512C22.84 17.64 24 16.125 24 14.388zm-8.738-1.14a.67.67 0 0 1-.669-.66c0-.365.3-.66.67-.66a.67.67 0 0 1 .668.66c0 .365-.3.66-.669.66zm3.348 0a.67.67 0 0 1-.668-.66c0-.365.3-.66.668-.66a.67.67 0 0 1 .669.66c0 .365-.3.66-.669.66z"})]}),(0,a.jsx)("span",{children:"微信登录"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)(d.Z,{onClick:()=>x("email"),variant:"outline",className:"w-full py-3 rounded-lg flex items-center justify-center space-x-2",children:[(0,a.jsx)(h.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"邮箱登录"})]})]}),"email"===o&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:()=>x("main"),className:"absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"邮箱登录"}),(0,a.jsx)("p",{className:"text-gray-600",children:"使用邮箱和密码登录"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(u.I,{label:"邮箱地址",type:"email",placeholder:"请输入邮箱地址",value:y.email,onChange:e=>A("email",e.target.value),error:j.email,leftIcon:(0,a.jsx)(h.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsx)(u.I,{label:"密码",type:"password",placeholder:"请输入密码",value:y.password,onChange:e=>A("password",e.target.value),error:j.password,leftIcon:(0,a.jsx)(p.Z,{className:"h-4 w-4"}),disabled:i})]}),(0,a.jsx)(d.Z,{onClick:T,className:"w-full py-3 rounded-lg",disabled:i,children:i?"登录中...":"登录"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"还没有账号？"}),(0,a.jsx)("button",{onClick:()=>x("register"),className:"text-blue-600 hover:text-blue-700 ml-1",children:"立即注册"})]})]}),"register"===o&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:()=>x("email"),className:"absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"注册账号"}),(0,a.jsx)("p",{className:"text-gray-600",children:"创建您的宠物交易账号"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(u.I,{label:"邮箱地址",type:"email",placeholder:"请输入邮箱地址",value:y.email,onChange:e=>A("email",e.target.value),error:j.email,leftIcon:(0,a.jsx)(h.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsx)(u.I,{label:"昵称",type:"text",placeholder:"请输入昵称",value:y.nickname,onChange:e=>A("nickname",e.target.value),error:j.nickname,disabled:i,maxLength:20}),(0,a.jsx)(u.I,{label:"密码",type:"password",placeholder:"请输入密码（至少6位）",value:y.password,onChange:e=>A("password",e.target.value),error:j.password,leftIcon:(0,a.jsx)(p.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsx)(u.I,{label:"确认密码",type:"password",placeholder:"请再次输入密码",value:y.confirmPassword,onChange:e=>A("confirmPassword",e.target.value),error:j.confirmPassword,leftIcon:(0,a.jsx)(p.Z,{className:"h-4 w-4"}),disabled:i}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"邮箱验证码"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(u.I,{type:"text",placeholder:"请输入6位验证码",value:y.verificationCode,onChange:e=>A("verificationCode",e.target.value),error:j.verificationCode,disabled:i,maxLength:6,className:"flex-1"}),(0,a.jsx)(d.Z,{onClick:P,disabled:N||C>0||!y.email||!(0,b.vV)(y.email),variant:"outline",className:"whitespace-nowrap",children:N?"发送中...":C>0?"".concat(C,"s"):"发送验证码"})]})]})]}),(0,a.jsx)(d.Z,{onClick:E,className:"w-full py-3 rounded-lg",disabled:i,children:i?"注册中...":"注册"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"已有账号？"}),(0,a.jsx)("button",{onClick:()=>x("email"),className:"text-blue-600 hover:text-blue-700 ml-1",children:"立即登录"})]})]})]})})},v=t(27648),j=t(82023),w=()=>{var e;let[s,t]=(0,r.useState)([]),[l,n]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{(async()=>{try{let e=await f.activityAPI.getActiveActivities();e.success&&e.data.length>0&&(t(e.data),n(!0))}catch(e){console.error("获取活动失败:",e)}})()},[]),l&&0!==s.length)?(0,a.jsx)("div",{className:"hidden md:flex items-center",children:(0,a.jsxs)("button",{onClick:()=>{window.location.href="/activities"},className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"max-w-32 truncate",children:s.length>1?"".concat(s.length,"个活动"):null===(e=s[0])||void 0===e?void 0:e.title})]})}):null};t(13041);var N=t(42449),k=t(88997),C=t(73247),Z=t(98728),I=t(63639),S=t(65302),z=t(79994);let _=e=>{let{isOpen:s,onClose:t}=e,{user:n,isLoggedIn:i}=(0,x.E)(),[o,d]=(0,r.useState)([]),[m,u]=(0,r.useState)(!1),[h,p]=(0,r.useState)("trade"),[g,b]=(0,r.useState)(null),[y,v]=(0,r.useState)(null),j=async()=>{if(i&&s)try{u(!0);let e=await f.petAPI.getUserNotifications({limit:50,type:"system"===h?void 0:"contact"});if(e.success){let s=(e.data||[]).map(e=>{var s,t,a,r,l,i,c,o,d,x,m,u,h,p,g,f,b;if("contact"===e.type){let h=e.sender_id===(null==n?void 0:n.uid);return{id:e._id,type:"contact",fromUser:{id:e.sender_id,nickname:(null===(s=e.data)||void 0===s?void 0:s.sender_nickname)||(null===(t=e.data)||void 0===t?void 0:t.author_nickname)||"用户",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:e.recipient_id,nickname:(null==n?void 0:n.nickname)||"",avatar:(null==n?void 0:n.avatar_url)||""},postInfo:{id:e.post_id,title:(null===(a=e.data)||void 0===a?void 0:a.post_title)||"宠物帖子",image:"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop",postType:(null===(r=e.data)||void 0===r?void 0:r.post_type)||"selling"},contactInfo:{method:(null===(i=e.data)||void 0===i?void 0:null===(l=i.author_contact)||void 0===l?void 0:l.type)||(null===(o=e.data)||void 0===o?void 0:null===(c=o.sender_contact)||void 0===c?void 0:c.type)||"wechat",value:(null===(x=e.data)||void 0===x?void 0:null===(d=x.author_contact)||void 0===d?void 0:d.value)||(null===(u=e.data)||void 0===u?void 0:null===(m=u.sender_contact)||void 0===m?void 0:m.value)||"未提供"},timestamp:e.created_at,read:e.read||!1,isInitiator:h}}return"system"===e.type||"punishment"===e.type||"appeal"===e.type?{id:e._id,type:"system",title:e.message,content:e.message,timestamp:e.created_at,read:e.read||!1,severity:"punishment"===e.type?"warning":"info",canAppeal:"punishment"===e.type,reportId:null===(h=e.data)||void 0===h?void 0:h.reportId,appealStatus:(null===(p=e.data)||void 0===p?void 0:p.appealStatus)||"none",notificationType:(null===(g=e.data)||void 0===g?void 0:g.notificationType)||"general",postTitle:null===(f=e.data)||void 0===f?void 0:f.postTitle,reportReason:null===(b=e.data)||void 0===b?void 0:b.reportReason}:null}).filter(Boolean);d(s)}else d([])}catch(e){console.error("加载通知失败:",e),d([])}finally{u(!1)}};(0,r.useEffect)(()=>{s&&i&&j()},[s,i,h]);let w=async e=>{try{await f.petAPI.markNotificationRead({notificationId:e}),d(s=>s.map(s=>s.id===e?{...s,read:!0}:s))}catch(e){console.error("标记已读失败:",e)}},I=async e=>{if(!g){b(e);try{await f.petAPI.deleteNotification({notificationId:e}),d(s=>s.filter(s=>s.id!==e)),setTimeout(()=>{j()},500)}catch(e){console.error("删除失败:",e),v({title:"操作失败",message:"删除失败，请稍后重试",onConfirm:()=>v(null),confirmText:"确定",showCancel:!1,showIcon:!1})}finally{b(null)}}},S=async()=>{v({title:"清空通知",message:{trade:"确定清空所有买卖通知吗？",breeding:"确定清空所有配种通知吗？",lost:"确定清空所有寻回通知吗？",system:"确定清空所有系统通知吗？"}[h],showIcon:!1,onConfirm:async()=>{try{let e=_.map(e=>e.id);if(0===e.length){v(null);return}await f.petAPI.bulkDeleteNotifications({notificationIds:e}),d(s=>s.filter(s=>!e.includes(s.id))),console.log("已清空".concat(_.length,"条通知")),v(null),setTimeout(()=>{j()},500)}catch(e){console.error("清空失败:",e),v({title:"操作失败",message:"清空失败，请稍后重试",onConfirm:()=>v(null),confirmText:"确定",showCancel:!1,showIcon:!1})}}})},_=o.filter(e=>"system"===h?"system"===e.type:"breeding"===h?"contact"===e.type&&"breeding"===e.postInfo.postType:"lost"===h?"contact"===e.type&&"lost"===e.postInfo.postType:"trade"!==h||"contact"===e.type&&("selling"===e.postInfo.postType||"wanted"===e.postInfo.postType));return s?(0,a.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:t,children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"通知中心"}),(0,a.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(c.Z,{className:"h-5 w-5 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1 m-4 flex-shrink-0",children:[(0,a.jsxs)("button",{onClick:()=>p("trade"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("trade"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(N.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"买卖通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("breeding"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("breeding"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(k.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"配种通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("lost"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("lost"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(C.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"寻回通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("system"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("system"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(Z.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"系统通知"})]})]}),!m&&_.length>0&&(0,a.jsx)("div",{className:"flex justify-end px-4 py-2 flex-shrink-0",children:(0,a.jsx)("button",{onClick:S,className:"px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:"清空当前分类"})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto px-6 pt-2 pb-6",children:m?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"加载中..."})]}):0===_.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(l.Z,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"暂无通知"})]}):(0,a.jsx)("div",{className:"space-y-4",children:_.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ".concat(e.read?"border-gray-200":"border-blue-200 bg-blue-50"),onClick:()=>w(e.id),children:"system"===e.type?(0,a.jsx)(T,{message:e,onDelete:I,deletingId:g}):(0,a.jsx)(P,{message:e,onDelete:I,deletingId:g})},e.id))})})]}),y&&(0,a.jsx)(z.Z,{isOpen:!0,onClose:()=>v(null),onConfirm:()=>{y.onConfirm(),!1!==y.showCancel&&v(null)},title:y.title,message:y.message,confirmText:y.confirmText||"确定",type:"info",showIcon:y.showIcon})]}):null},P=e=>{let{message:s,onDelete:t,deletingId:r}=e;s.isInitiator;let l=async()=>{try{await navigator.clipboard.writeText(s.contactInfo.value);let e=document.createElement("div");e.textContent="联系方式已复制到剪贴板",e.style.cssText="\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        background: #10b981;\n        color: white;\n        padding: 12px 24px;\n        border-radius: 8px;\n        font-size: 14px;\n        z-index: 10000;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      ",document.body.appendChild(e),setTimeout(()=>{document.body.contains(e)&&document.body.removeChild(e)},2e3)}catch(t){console.error("复制失败:",t);let e=document.createElement("textarea");e.value=s.contactInfo.value,document.body.appendChild(e),e.select();try{document.execCommand("copy");let e=document.createElement("div");e.textContent="联系方式已复制到剪贴板",e.style.cssText="\n          position: fixed;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          background: #10b981;\n          color: white;\n          padding: 12px 24px;\n          border-radius: 8px;\n          font-size: 14px;\n          z-index: 10000;\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        ",document.body.appendChild(e),setTimeout(()=>{document.body.contains(e)&&document.body.removeChild(e)},2e3)}catch(e){console.error("降级复制也失败:",e)}document.body.removeChild(e)}};return(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=new Date(e),t=s.getFullYear(),a=s.getMonth()+1,r=s.getDate(),l=s.getHours(),n=s.getMinutes();return"".concat(t,"年").concat(a,"月").concat(r,"日").concat(l,"时").concat(n.toString().padStart(2,"0"),"分")})(s.timestamp)}),!s.read&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),t(s.id)},disabled:r===s.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:r===s.id?(0,a.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"}):(0,a.jsx)(c.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:(0,a.jsxs)(a.Fragment,{children:["已取得",(0,a.jsx)("span",{className:"font-medium",children:s.fromUser.nickname}),"的联系方式：",s.contactInfo.value]})}),(0,a.jsx)("button",{onClick:l,className:"text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors flex-shrink-0 ml-2",children:"复制"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm text-gray-700",children:s.postInfo.title}),(0,a.jsx)("button",{onClick:()=>{window.open("/pet-trading-platform/post/detail/?id=".concat(s.postInfo.id),"_blank")},className:"text-xs text-blue-600 hover:text-blue-800 transition-colors flex-shrink-0 ml-2",children:"查看帖子"})]})]})]})},T=e=>{let{message:s,onDelete:t,deletingId:r}=e;return(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 mb-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(e=>{switch(e){case"warning":return(0,a.jsx)(I.Z,{className:"w-5 h-5 text-orange-500"});case"error":return(0,a.jsx)(I.Z,{className:"w-5 h-5 text-red-500"});default:return(0,a.jsx)(S.Z,{className:"w-5 h-5 text-blue-500"})}})(s.severity)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=new Date(e),t=s.getFullYear(),a=s.getMonth()+1,r=s.getDate(),l=s.getHours(),n=s.getMinutes();return"".concat(t,"年").concat(a,"月").concat(r,"日").concat(l,"时").concat(n.toString().padStart(2,"0"),"分")})(s.timestamp)}),!s.read&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),t(s.id)},disabled:r===s.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:r===s.id?(0,a.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"}):(0,a.jsx)(c.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 flex-1",children:"post_reported"===s.notificationType?"您发布的宝贝".concat(s.postTitle||"","被人多次举报已经隐藏，举报理由为：").concat(s.reportReason||"违规内容","。如要申诉请点击右边的申诉按钮。"):"user_reported"===s.notificationType?"您被人多次举报涉嫌诈骗，如要申诉请点击右边申诉按钮。":s.content}),s.canAppeal&&(0,a.jsxs)("div",{className:"ml-3 flex-shrink-0",children:["none"===s.appealStatus&&(0,a.jsx)("button",{onClick:e=>{e.stopPropagation()},className:"text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors",children:"申诉"}),"pending"===s.appealStatus&&(0,a.jsx)("span",{className:"text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:"申诉处理中..."}),"approved"===s.appealStatus&&(0,a.jsx)("span",{className:"text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full",children:"申诉已通过"}),"rejected"===s.appealStatus&&(0,a.jsx)("span",{className:"text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full",children:"申诉已驳回"})]})]})]})]})})};var E=()=>{let{user:e,isLoggedIn:s,logout:t,refreshLoginState:m}=(0,x.E)(),[u,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),[f,b]=(0,r.useState)(!1),j=()=>{h(!0)},N=async()=>{await t(),g(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-40",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(v.default,{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"\uD83D\uDC3E"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"宠物交易平台"})]}),(0,a.jsx)(w,{}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-3",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Z,{variant:"outline",icon:(0,a.jsx)(l.Z,{className:"h-4 w-4"}),onClick:()=>b(!0),children:"通知"}),(0,a.jsx)(v.default,{href:"/profile",children:(0,a.jsx)(d.Z,{variant:"outline",icon:(0,a.jsx)(n.Z,{className:"h-4 w-4"}),children:"我"})}),(0,a.jsx)(d.Z,{variant:"outline",icon:(0,a.jsx)(i.Z,{className:"h-4 w-4"}),onClick:N,className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:"退出"})]}):(0,a.jsx)(d.Z,{onClick:j,children:"登录"})}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{onClick:()=>g(!p),className:"p-2 rounded-lg hover:bg-gray-100",children:p?(0,a.jsx)(c.Z,{className:"h-6 w-6"}):(0,a.jsx)(o.Z,{className:"h-6 w-6"})})})]})}),p&&(0,a.jsx)("div",{className:"md:hidden border-t border-gray-200 bg-white",children:(0,a.jsx)("div",{className:"px-4 py-4 space-y-3",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"pb-3 border-b border-gray-200",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:null==e?void 0:e.nickname}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:null==e?void 0:e.email})]})}),(0,a.jsxs)("button",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 w-full text-left",onClick:()=>{b(!0),g(!1)},children:[(0,a.jsx)(l.Z,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{children:"通知"})]}),(0,a.jsxs)(v.default,{href:"/profile",className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100",onClick:()=>g(!1),children:[(0,a.jsx)(n.Z,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{children:"我的主页"})]}),(0,a.jsxs)("button",{onClick:N,className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-red-50 w-full text-left",children:[(0,a.jsx)(i.Z,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-600",children:"退出"})]})]}):(0,a.jsx)(d.Z,{onClick:()=>{j(),g(!1)},className:"w-full",children:"登录"})})})]}),(0,a.jsx)(y,{isOpen:u,onClose:()=>h(!1),onSuccess:async()=>{g(!1),await m()}}),(0,a.jsx)(_,{isOpen:f,onClose:()=>b(!1)})]})}},56334:function(e,s,t){var a=t(57437),r=t(2265),l=t(68661);let n=r.forwardRef((e,s)=>{let{className:t,variant:r="primary",size:n="md",loading:i=!1,icon:c,children:o,disabled:d,...x}=e;return(0,a.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],t),ref:s,disabled:d||i,...x,children:[i&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!i&&c&&(0,a.jsx)("span",{className:"mr-2",children:c}),o]})});n.displayName="Button",s.Z=n},79994:function(e,s,t){var a=t(57437);t(2265);var r=t(98702),l=t(56334),n=t(63639),i=t(65302),c=t(33245);s.Z=e=>{let{isOpen:s,onClose:t,onConfirm:o,title:d,message:x,confirmText:m="确认",cancelText:u="取消",type:h="info",loading:p=!1,showIcon:g=!0}=e;return(0,a.jsx)(r.u_,{isOpen:s,onClose:t,title:"",size:"sm",showCloseButton:!1,children:(0,a.jsx)(r.fe,{children:(0,a.jsxs)("div",{className:"text-center",children:[g&&(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4",children:(()=>{switch(h){case"danger":return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-red-600"});case"warning":return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-yellow-600"});case"success":return(0,a.jsx)(i.Z,{className:"w-6 h-6 text-green-600"});default:return(0,a.jsx)(c.Z,{className:"w-6 h-6 text-blue-600"})}})()}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2 ".concat(g?"":"mt-2"),children:d}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:x}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-center",children:[(0,a.jsx)(l.Z,{variant:"outline",onClick:t,disabled:p,className:"min-w-[80px]",children:u}),(0,a.jsx)(l.Z,{variant:(()=>{switch(h){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:o,loading:p,disabled:p,className:"min-w-[80px]",children:m})]})]})})})}},92827:function(e,s,t){t.d(s,{I:function(){return n},g:function(){return i}});var a=t(57437),r=t(2265),l=t(68661);let n=r.forwardRef((e,s)=>{let{className:t,type:r="text",label:n,error:i,helperText:c,leftIcon:o,rightIcon:d,variant:x="default",...m}=e,u=i?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,a.jsxs)("div",{className:"w-full",children:[n&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:n}),(0,a.jsxs)("div",{className:"relative",children:[o&&(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-gray-400",children:o})}),(0,a.jsx)("input",{type:r,className:(0,l.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[x],u,o&&"pl-10",d&&"pr-10",t),ref:s,...m}),d&&(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-gray-400",children:d})})]}),(i||c)&&(0,a.jsx)("p",{className:(0,l.cn)("mt-1 text-xs",i?"text-red-600":"text-gray-500"),children:i||c})]})});n.displayName="Input";let i=r.forwardRef((e,s)=>{let{className:t,label:r,error:n,helperText:i,variant:c="default",...o}=e,d=n?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r}),(0,a.jsx)("textarea",{className:(0,l.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed resize-none",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[c],d,t),ref:s,...o}),(n||i)&&(0,a.jsx)("p",{className:(0,l.cn)("mt-1 text-xs",n?"text-red-600":"text-gray-500"),children:n||i})]})});i.displayName="Textarea"},31215:function(e,s,t){t.d(s,{LL:function(){return c},SX:function(){return n},gG:function(){return i},gb:function(){return l}});var a=t(57437);t(2265);var r=t(68661);let l=e=>{let{size:s="md",variant:t="spinner",className:l,text:n}=e,i={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},c={sm:"text-sm",md:"text-base",lg:"text-lg"};if("spinner"===t)return(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",l),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("svg",{className:(0,r.cn)("animate-spin text-primary-600",i[s]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n&&(0,a.jsx)("p",{className:(0,r.cn)("text-gray-500",c[s]),children:n})]})});if("dots"===t){let e="sm"===s?"w-2 h-2":"md"===s?"w-3 h-3":"w-4 h-4";return(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",l),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"300ms"}})]}),n&&(0,a.jsx)("p",{className:(0,r.cn)("text-gray-500",c[s]),children:n})]})})}return"pulse"===t?(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",l),children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:(0,r.cn)("bg-primary-600 rounded-full animate-pulse",i[s])}),n&&(0,a.jsx)("p",{className:(0,r.cn)("text-gray-500",c[s]),children:n})]})}):null},n=e=>{let{text:s="加载中..."}=e;return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsx)(l,{size:"lg",text:s})})},i=()=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse",children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,a.jsxs)("div",{className:"p-3 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})]}),c=()=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 p-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48"}),(0,a.jsxs)("div",{className:"flex space-x-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]})]}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded w-24"})]})})},98702:function(e,s,t){t.d(s,{fe:function(){return d},u_:function(){return o}});var a=t(57437),r=t(2265),l=t(54887),n=t(32489),i=t(68661),c=t(56334);let o=e=>{let{isOpen:s,onClose:t,title:o,children:d,size:x="md",showCloseButton:m=!0,closeOnOverlayClick:u=!0,className:h}=e;if((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,t]),!s)return null;let p=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:u?t:void 0}),(0,a.jsxs)("div",{className:(0,i.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[x],h),onClick:e=>e.stopPropagation(),children:[(o||m)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[o&&(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:o}),m&&(0,a.jsx)(c.Z,{variant:"ghost",size:"sm",onClick:t,className:"p-1 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,l.createPortal)(p,document.body)},d=e=>{let{children:s,className:t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("p-4",t),children:s})}}}]);