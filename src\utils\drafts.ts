import { PostFormData } from '@/types';
import { petAPI } from '@/lib/cloudbase';

export interface Draft extends Omit<PostFormData, 'images'> {
  id: string;
  images: string[]; // 草稿中的图片是序列化后的字符串数组
  created_at: string;
  updated_at: string;
  // 可选的归档相关字段
  isArchived?: boolean;
  originalPostId?: string;
}

const DRAFTS_KEY = 'petDrafts';

// 获取所有草稿
export const getDrafts = (): Draft[] => {
  try {
    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      return [];
    }

    const drafts = localStorage.getItem(DRAFTS_KEY);
    return drafts ? JSON.parse(drafts) : [];
  } catch (error) {
    console.error('获取草稿失败:', error);
    return [];
  }
};

// 将File对象转换为可序列化的格式
const serializeImages = async (images: File[]): Promise<string[]> => {
  const serializedImages: string[] = [];

  for (const file of images) {
    if (file instanceof File) {
      try {
        // 将File转换为base64字符串
        const base64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
        serializedImages.push(base64);
      } catch (error) {
        console.error('转换图片失败:', error);
        // 如果转换失败，使用默认图片
        serializedImages.push('https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center');
      }
    } else if (typeof file === 'string') {
      // 如果已经是字符串，直接使用
      serializedImages.push(file);
    }
  }

  return serializedImages;
};

// 保存草稿
export const saveDraft = async (formData: PostFormData): Promise<string> => {
  try {
    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      throw new Error('无法在服务器端保存草稿');
    }

    // 序列化图片
    const serializedImages = await serializeImages(formData.images);

    const existingDrafts = getDrafts();
    const draft: Draft = {
      id: Date.now().toString(),
      ...formData,
      images: serializedImages as any, // 使用序列化后的图片
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const updatedDrafts = [draft, ...existingDrafts];
    localStorage.setItem(DRAFTS_KEY, JSON.stringify(updatedDrafts));

    return draft.id;
  } catch (error) {
    console.error('保存草稿失败:', error);
    throw new Error('保存草稿失败');
  }
};

// 更新草稿
export const updateDraft = async (id: string, formData: PostFormData): Promise<void> => {
  try {
    const drafts = getDrafts();
    const index = drafts.findIndex(draft => draft.id === id);

    if (index !== -1) {
      // 序列化图片
      const serializedImages = await serializeImages(formData.images);

      drafts[index] = {
        ...drafts[index],
        ...formData,
        images: serializedImages as any, // 使用序列化后的图片
        updated_at: new Date().toISOString(),
      };
      localStorage.setItem(DRAFTS_KEY, JSON.stringify(drafts));
    }
  } catch (error) {
    console.error('更新草稿失败:', error);
    throw new Error('更新草稿失败');
  }
};

// 删除草稿
export const deleteDraft = (id: string): void => {
  try {
    const drafts = getDrafts();
    const filteredDrafts = drafts.filter(draft => draft.id !== id);
    localStorage.setItem(DRAFTS_KEY, JSON.stringify(filteredDrafts));
  } catch (error) {
    console.error('删除草稿失败:', error);
    throw new Error('删除草稿失败');
  }
};

// 获取单个草稿
export const getDraft = (id: string): Draft | null => {
  try {
    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      return null;
    }

    const drafts = getDrafts();
    const draft = drafts.find(draft => draft.id === id) || null;
    console.log(`获取草稿 ${id}:`, draft);
    return draft;
  } catch (error) {
    console.error('获取草稿失败:', error);
    return null;
  }
};

// 清空所有草稿
export const clearAllDrafts = (): void => {
  try {
    localStorage.removeItem(DRAFTS_KEY);
  } catch (error) {
    console.error('清空草稿失败:', error);
    throw new Error('清空草稿失败');
  }
};

// 从云端同步草稿（自动下架的帖子）
export const syncDraftsFromCloud = async (userId: string): Promise<void> => {
  try {
    if (typeof window === 'undefined') {
      return;
    }

    // 获取云端草稿
    const result = await petAPI.getUserDrafts({ user_id: userId });
    const cloudDrafts = result.data || [];

    if (cloudDrafts.length === 0) {
      return;
    }

    // 获取本地草稿
    const localDrafts = getDrafts();

    // 转换云端草稿格式
    const convertedDrafts: Draft[] = cloudDrafts.map((cloudDraft: any) => ({
      id: cloudDraft._id,
      title: cloudDraft.title,
      content: cloudDraft.content,
      images: cloudDraft.images || [],
      category: cloudDraft.category,
      type: cloudDraft.type,
      location: cloudDraft.location,
      breed: cloudDraft.breed,
      age: cloudDraft.age,
      gender: cloudDraft.gender,
      price: cloudDraft.price,
      contact_info: cloudDraft.contact_info,
      tags: cloudDraft.tags || [],
      created_at: cloudDraft.created_at,
      updated_at: new Date().toISOString(),
      isArchived: cloudDraft.draft_type === 'auto_archived',
      originalPostId: cloudDraft.original_post_id
    }));

    // 合并草稿（去重）
    const existingIds = new Set(localDrafts.map(draft => draft.id));
    const newDrafts = convertedDrafts.filter(draft => !existingIds.has(draft.id));

    if (newDrafts.length > 0) {
      const allDrafts = [...localDrafts, ...newDrafts];
      localStorage.setItem(DRAFTS_KEY, JSON.stringify(allDrafts));

      // 删除云端已同步的草稿
      for (const draft of newDrafts) {
        try {
          await petAPI.deleteUserDraft({ draft_id: draft.id });
        } catch (error) {
          console.error('删除云端草稿失败:', error);
        }
      }
    }
  } catch (error) {
    console.error('同步云端草稿失败:', error);
  }
};

// 获取自动下架的草稿数量
export const getArchivedDraftsCount = (): number => {
  try {
    const drafts = getDrafts();
    return drafts.filter(draft => draft.isArchived).length;
  } catch (error) {
    console.error('获取下架草稿数量失败:', error);
    return 0;
  }
};
