const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 用户管理云函数
 */
exports.main = async (event, context) => {
  const { action, data, ...restParams } = event;
  const { OPENID } = cloud.getWXContext();

  // 如果没有data字段，说明参数直接在event中
  const actualData = data || restParams;

  // 获取OPENID，优先使用前端传递的openId，否则使用云开发的OPENID
  const finalOpenId = actualData?.openId || event?.openId || OPENID;
  console.log('用户管理 API - 获取到的OPENID:', finalOpenId);

  try {
    switch (action) {
      // 获取用户信息
      case 'getUserInfo':
        return await getUserInfo(actualData, finalOpenId);

      // 获取用户信用分信息
      case 'getUserCreditScore':
        return await getUserCreditScore(actualData, finalOpenId);

      // 更新用户信用分
      case 'updateUserCreditScore':
        return await updateUserCreditScore(actualData, finalOpenId);

      // 设置用户超级用户状态
      case 'setUserSuperStatus':
        return await setUserSuperStatus(actualData, finalOpenId);

      // 获取用户VIP信息
      case 'getUserVipInfo':
        return await getUserVipInfo(actualData, finalOpenId);

      default:
        return {
          success: false,
          message: `未知的操作: ${action}`
        };
    }
  } catch (error) {
    console.error('用户管理 API 错误:', error);
    return {
      success: false,
      message: error.message || '用户管理 API 调用失败'
    };
  }
};

// 验证管理员权限
async function checkAdminPermission(openId) {
  try {
    const adminResult = await db.collection('admins').where({
      user_id: openId,
      status: 'active'
    }).get();

    return adminResult.data.length > 0;
  } catch (error) {
    console.error('验证管理员权限失败:', error);
    return false;
  }
}

// 获取用户信息
async function getUserInfo({ userId }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限访问');
    }

    console.log('开始查询用户信息，用户ID:', userId);

    // 查询用户基本信息 - 支持通过user_id或_id字段查询
    let userResult = await db.collection('users').where({
      user_id: userId
    }).get();

    // 如果通过user_id没找到，尝试通过_id查询
    if (userResult.data.length === 0) {
      console.log('通过user_id未找到用户，尝试通过_id查询');
      userResult = await db.collection('users').doc(userId).get();

      // 如果通过_id找到了用户，需要将结果格式化为数组
      if (userResult.data) {
        userResult.data = [userResult.data];
      }
    }

    if (!userResult.data || userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const userInfo = userResult.data[0];
    console.log('用户基本信息查询成功:', userInfo);

    // 查询用户信用分信息
    let creditInfo = null;
    try {
      const creditResult = await db.collection('user_credit_scores').where({
        user_id: userId
      }).get();

      if (creditResult.data.length > 0) {
        creditInfo = creditResult.data[0];
        console.log('用户信用分信息查询成功:', creditInfo);
      }
    } catch (error) {
      console.error('查询用户信用分失败:', error);
    }

    // 查询用户VIP信息
    let vipInfo = null;
    try {
      const vipResult = await db.collection('vip_users').where({
        user_id: userId
      }).get();

      if (vipResult.data.length > 0) {
        const vip = vipResult.data[0];
        const now = new Date();
        const isExpired = vip.vip_end_time && new Date(vip.vip_end_time) < now;
        
        vipInfo = {
          ...vip,
          is_vip: !isExpired,
          is_expired: isExpired,
          remaining_days: isExpired ? 0 : Math.ceil((new Date(vip.vip_end_time) - now) / (24 * 60 * 60 * 1000))
        };
        console.log('用户VIP信息查询成功:', vipInfo);
      }
    } catch (error) {
      console.error('查询用户VIP信息失败:', error);
    }

    return {
      success: true,
      data: {
        userInfo,
        creditInfo,
        vipInfo
      }
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      message: error.message || '获取用户信息失败'
    };
  }
}

// 获取用户信用分信息
async function getUserCreditScore({ userId }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限访问');
    }

    // 支持通过user_id或_id字段查询信用分
    let creditResult = await db.collection('user_credit_scores').where({
      user_id: userId
    }).get();

    // 如果通过user_id没找到，尝试通过_id查询（某些用户可能使用_id作为关联字段）
    if (creditResult.data.length === 0) {
      creditResult = await db.collection('user_credit_scores').where({
        user_id: userId  // 保持原有逻辑，因为信用分表中应该统一使用user_id字段
      }).get();
    }

    if (creditResult.data.length === 0) {
      return {
        success: true,
        data: {
          user_id: userId,
          credit_score: 50, // 默认信用分
          is_super_user: false,
          daily_post_limit: 5,
          created_at: new Date().toISOString()
        }
      };
    }

    return {
      success: true,
      data: creditResult.data[0]
    };
  } catch (error) {
    console.error('获取用户信用分失败:', error);
    return {
      success: false,
      message: error.message || '获取用户信用分失败'
    };
  }
}

// 更新用户信用分
async function updateUserCreditScore({ userId, creditScore, reason }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const now = new Date();

    // 查询现有信用分记录 - 统一使用userId作为关联字段
    const creditResult = await db.collection('user_credit_scores').where({
      user_id: userId
    }).get();

    const updateData = {
      credit_score: creditScore,
      last_updated: now.toISOString(),
      updated_by: openId,
      update_reason: reason || '管理员手动调整'
    };

    if (creditResult.data.length > 0) {
      // 更新现有记录
      await db.collection('user_credit_scores').doc(creditResult.data[0]._id).update(updateData);
    } else {
      // 创建新记录
      await db.collection('user_credit_scores').add({
        user_id: userId,
        is_super_user: false,
        daily_post_limit: 5,
        created_at: now.toISOString(),
        ...updateData
      });
    }

    return {
      success: true,
      message: '更新用户信用分成功'
    };
  } catch (error) {
    console.error('更新用户信用分失败:', error);
    return {
      success: false,
      message: error.message || '更新用户信用分失败'
    };
  }
}

// 设置用户超级用户状态
async function setUserSuperStatus({ userId, isSuperUser, dailyPostLimit }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const now = new Date();

    // 查询现有信用分记录 - 统一使用userId作为关联字段
    const creditResult = await db.collection('user_credit_scores').where({
      user_id: userId
    }).get();

    const updateData = {
      is_super_user: isSuperUser,
      daily_post_limit: dailyPostLimit || (isSuperUser ? 50 : 5),
      last_updated: now.toISOString(),
      updated_by: openId
    };

    if (creditResult.data.length > 0) {
      // 更新现有记录
      await db.collection('user_credit_scores').doc(creditResult.data[0]._id).update(updateData);
    } else {
      // 创建新记录
      await db.collection('user_credit_scores').add({
        user_id: userId,
        credit_score: 50,
        created_at: now.toISOString(),
        ...updateData
      });
    }

    return {
      success: true,
      message: `${isSuperUser ? '设置' : '取消'}超级用户成功`
    };
  } catch (error) {
    console.error('设置用户超级用户状态失败:', error);
    return {
      success: false,
      message: error.message || '设置用户超级用户状态失败'
    };
  }
}

// 获取用户VIP信息
async function getUserVipInfo({ userId }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限访问');
    }

    // 支持通过user_id查询VIP信息 - 统一使用userId作为关联字段
    const vipResult = await db.collection('vip_users').where({
      user_id: userId
    }).get();

    if (vipResult.data.length === 0) {
      return {
        success: true,
        data: {
          user_id: userId,
          is_vip: false,
          vip_start_time: null,
          vip_end_time: null,
          vip_benefits: {
            daily_post_limit: 5,
            credit_score_limit: 100
          }
        }
      };
    }

    const vipInfo = vipResult.data[0];
    const now = new Date();
    const isExpired = vipInfo.vip_end_time && new Date(vipInfo.vip_end_time) < now;

    return {
      success: true,
      data: {
        ...vipInfo,
        is_vip: !isExpired,
        is_expired: isExpired,
        remaining_days: isExpired ? 0 : Math.ceil((new Date(vipInfo.vip_end_time) - now) / (24 * 60 * 60 * 1000))
      }
    };
  } catch (error) {
    console.error('获取用户VIP信息失败:', error);
    return {
      success: false,
      message: error.message || '获取用户VIP信息失败'
    };
  }
}
