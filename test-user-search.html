<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户搜索测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>用户搜索功能测试</h1>
    
    <div class="test-section">
        <h2>测试1：搜索不存在的用户</h2>
        <input type="text" id="nonExistentUserId" placeholder="输入不存在的用户ID" value="nonexistent123">
        <button class="btn-danger" onclick="testNonExistentUser()">测试搜索不存在用户</button>
        <div id="nonExistentResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>测试2：搜索存在的用户</h2>
        <input type="text" id="existentUserId" placeholder="输入存在的用户ID" value="d77d384f6877626a0574989b7124319b">
        <button class="btn-primary" onclick="testExistentUser()">测试搜索存在用户</button>
        <div id="existentResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>测试3：权限管理功能</h2>
        <button class="btn-primary" onclick="testPermissionManagement()">测试权限管理</button>
        <div id="permissionResult" class="result"></div>
    </div>

    <script>
        // 云开发初始化
        const cloudbase = window.cloudbase;
        const app = cloudbase.init({
            env: 'yichongyuzhou-3g9112qwf5f3487b'
        });

        // 模拟登录
        async function initAuth() {
            const auth = app.auth();
            try {
                await auth.signInAnonymously();
                console.log('匿名登录成功');
            } catch (error) {
                console.error('登录失败:', error);
            }
        }

        // 调用云函数
        async function callCloudFunction(name, action, data) {
            try {
                const result = await app.callFunction({
                    name: name,
                    data: {
                        action: action,
                        ...data
                    }
                });
                return result.result;
            } catch (error) {
                console.error('云函数调用失败:', error);
                return {
                    success: false,
                    message: error.message || '调用失败'
                };
            }
        }

        // 测试搜索不存在的用户
        async function testNonExistentUser() {
            const userId = document.getElementById('nonExistentUserId').value;
            const resultDiv = document.getElementById('nonExistentResult');

            resultDiv.innerHTML = '<div class="info">正在搜索用户...</div>';

            try {
                const result = await callCloudFunction('pet-api', 'getUserInfo', { userId });

                if (result.success) {
                    resultDiv.innerHTML = `<div class="error">❌ 测试失败：应该返回用户不存在错误，但返回了用户信息</div>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    if (result.message === '用户不存在') {
                        resultDiv.innerHTML = `<div class="success">✅ 测试通过：正确返回"用户不存在"错误</div>`;
                        resultDiv.innerHTML += `<div>完整响应：${JSON.stringify(result)}</div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ 测试失败：错误信息不正确</div>`;
                        resultDiv.innerHTML += `<div>期望：用户不存在</div>`;
                        resultDiv.innerHTML += `<div>实际：${result.message}</div>`;
                        resultDiv.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试异常：${error.message}</div>`;
                resultDiv.innerHTML += `<pre>${JSON.stringify(error, null, 2)}</pre>`;
            }
        }

        // 测试搜索存在的用户
        async function testExistentUser() {
            const userId = document.getElementById('existentUserId').value;
            const resultDiv = document.getElementById('existentResult');

            resultDiv.innerHTML = '<div class="info">正在搜索用户...</div>';

            try {
                const result = await callCloudFunction('pet-api', 'getUserInfo', { userId });

                if (result.success && result.data) {
                    resultDiv.innerHTML = `<div class="success">✅ 测试通过：成功获取用户信息</div>`;
                    resultDiv.innerHTML += `<div>用户昵称：${result.data.nickname || '未设置'}</div>`;
                    resultDiv.innerHTML += `<div>用户ID：${result.data.id || result.data._id}</div>`;
                    resultDiv.innerHTML += `<div>帖子数量：${result.data.posts_count || 0}</div>`;
                    resultDiv.innerHTML += `<div>粉丝数量：${result.data.followers_count || 0}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 测试失败：无法获取用户信息</div>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试异常：${error.message}</div>`;
                resultDiv.innerHTML += `<pre>${JSON.stringify(error, null, 2)}</pre>`;
            }
        }

        // 测试权限管理功能
        async function testPermissionManagement() {
            const resultDiv = document.getElementById('permissionResult');
            
            resultDiv.innerHTML = '<div class="info">正在测试权限管理...</div>';
            
            // 测试获取用户权限
            const permissionResult = await callCloudFunction('pet-api', 'getTargetUserPermissions', { 
                targetUserId: 'd77d384f6877626a0574989b7124319b' 
            });
            
            if (permissionResult.success) {
                resultDiv.innerHTML = `<div class="success">✅ 权限获取成功</div>`;
                resultDiv.innerHTML += `<pre>${JSON.stringify(permissionResult.data, null, 2)}</pre>`;
            } else {
                if (permissionResult.message === '无权限操作') {
                    resultDiv.innerHTML = `<div class="info">ℹ️ 正常：非管理员用户无权限操作</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 权限测试失败</div>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(permissionResult, null, 2)}</pre>`;
                }
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            initAuth();
        };
    </script>

    <!-- 引入云开发SDK -->
    <script src="https://imgcache.qq.com/qcloud/cloudbase-js-sdk/1.7.1/cloudbase.full.js"></script>
</body>
</html>
