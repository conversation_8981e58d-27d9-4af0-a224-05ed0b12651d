(()=>{var e={};e.id=427,e.ids=[427],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},32868:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>h,tree:()=>n}),t(13624),t(9457),t(16953),t(35866);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),c=t(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);t.d(s,d);let n=["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13624)),"D:\\web-cloudbase-project\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9457)),"D:\\web-cloudbase-project\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\web-cloudbase-project\\src\\app\\admin\\dashboard\\page.tsx"],m="/admin/dashboard/page",o={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},85787:(e,s,t)=>{Promise.resolve().then(t.bind(t,8868))},64271:(e,s,t)=>{Promise.resolve().then(t.bind(t,5264))},66697:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},37202:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},88319:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},82200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},70003:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},88378:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74975:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},94019:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},8868:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var a=t(10326),r=t(17577),l=t(35047),i=t(41828),c=t(20603),d=t(99837),n=t(86333),x=t(58038),m=t(76557);let o=(0,m.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),h=(0,m.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),p=(0,m.Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),u=(0,m.Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]);var g=t(74975);let j=(0,m.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);var y=t(88378),b=t(83855),N=t(82200),v=t(66697);let f=(0,m.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var w=t(98091),k=t(54659),_=t(91470),C=t(88307),I=t(79635);let P=(0,m.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var S=t(94019),Z=t(12714),D=t(88319),V=t(70003),L=t(48998);let A=(0,m.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var M=t(37202);let U=(0,m.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function z(){let e=(0,l.useRouter)(),[s,t]=(0,r.useState)("dashboard"),[m,z]=(0,r.useState)(null),[T,$]=(0,r.useState)(null),[R,E]=(0,r.useState)([]),[q,F]=(0,r.useState)([]),[H,B]=(0,r.useState)(!0),[O,G]=(0,r.useState)(null),[W,X]=(0,r.useState)(null),[K,J]=(0,r.useState)(""),[Q,Y]=(0,r.useState)(!1),[ee,es]=(0,r.useState)("approved"),[et,ea]=(0,r.useState)(!1),[er,el]=(0,r.useState)({username:"",password:"",role:"admin",level:1,permissions:[]}),[ei,ec]=(0,r.useState)([]),[ed,en]=(0,r.useState)(!1),[ex,em]=(0,r.useState)(!1),[eo,eh]=(0,r.useState)(null),[ep,eu]=(0,r.useState)(""),[eg,ej]=(0,r.useState)(""),[ey,eb]=(0,r.useState)(""),[eN,ev]=(0,r.useState)([]),[ef,ew]=(0,r.useState)([]),[ek,e_]=(0,r.useState)([]),[eC,eI]=(0,r.useState)(!1),[eP,eS]=(0,r.useState)("ads"),[eZ,eD]=(0,r.useState)(!1),[eV,eL]=(0,r.useState)([]),[eA,eM]=(0,r.useState)(!1),[eU,ez]=(0,r.useState)(null),[eT,e$]=(0,r.useState)(!1),[eR,eE]=(0,r.useState)(!1),[eq,eF]=(0,r.useState)("all"),[eH,eB]=(0,r.useState)("all"),[eO,eG]=(0,r.useState)({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10,normalUserPostLimit:100,superUserPostLimit:500,autoArchiveEnabled:!0,normalUserDailyPostLimit:5,vipUserDailyPostLimit:20}),[eW,eX]=(0,r.useState)(!1),[eK,eJ]=(0,r.useState)({post_report_threshold:5,user_report_threshold:10,auto_hide_posts:!0,auto_warn_users:!0,notification_enabled:!0}),[eQ,eY]=(0,r.useState)(!1),[e0,e2]=(0,r.useState)(""),[e1,e6]=(0,r.useState)(null),[e4,e3]=(0,r.useState)(null),[e5,e7]=(0,r.useState)(!1),[e8,e9]=(0,r.useState)(!1),[se,ss]=(0,r.useState)(null),[st,sa]=(0,r.useState)(!1),[sr,sl]=(0,r.useState)(!1),[si,sc]=(0,r.useState)({score:0,reason:""}),[sd,sn]=(0,r.useState)(null),[sx,sm]=(0,r.useState)(!1),[so,sh]=(0,r.useState)(!1),[sp,su]=(0,r.useState)(30),[sg,sj]=(0,r.useState)({daily_post_limit:50,credit_score_limit:200,special_badge:!0,priority_support:!0}),[sy,sb]=(0,r.useState)([]),[sN,sv]=(0,r.useState)(!1),[sf,sw]=(0,r.useState)("all"),[sk,s_]=(0,r.useState)(null),[sC,sI]=(0,r.useState)(!1),[sP,sS]=(0,r.useState)(!1),[sZ,sD]=(0,r.useState)(null),[sV,sL]=(0,r.useState)(!1),[sA,sM]=(0,r.useState)(5),[sU,sz]=(0,r.useState)(!1),[sT,s$]=(0,r.useState)(null),[sR,sE]=(0,r.useState)(!1),sq=e=>{s$(e),sz(!0)},sF=async()=>{try{let e=await i.petAPI.getAdmins({limit:50,offset:0});e.success&&E(e.data||[])}catch(e){console.error("加载管理员列表失败:",e)}},sH=async(e,s)=>{en(!0);try{let t={limit:50};e&&(t.userId=e),s&&(t.breed=s);let a=await i.petAPI.getPostsForAdmin(t);a.success&&ec(a.data)}catch(e){console.error("加载帖子列表失败:",e),c.C.error("加载帖子列表失败")}finally{en(!1)}},sB=async()=>{try{let e=await i.petAPI.getAppeals({status:"all",limit:50,offset:0});e.success&&F(e.data||[])}catch(e){console.error("加载申诉列表失败:",e)}},sO=async()=>{if(!er.username||!er.password){c.C.error("请填写用户名和密码");return}try{let e=await i.petAPI.createAdmin({...er,permissions:["*"]});e.success?(c.C.success("管理员创建成功"),ea(!1),el({username:"",password:"",role:"admin",level:1,permissions:[]}),sF()):c.C.error(e.message||"创建失败")}catch(e){console.error("创建管理员失败:",e),c.C.error(e.message||"创建管理员失败")}},sG=async e=>{sq({title:"删除管理员",message:"确定要删除这个管理员吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:async()=>{try{let s=await i.petAPI.deleteAdmin({adminId:e});s.success?(c.C.success("管理员删除成功"),sF()):c.C.error(s.message||"删除失败")}catch(e){console.error("删除管理员失败:",e),c.C.error(e.message||"删除管理员失败")}}})},sW=async()=>{if(W)try{G(W._id);let e=await i.petAPI.handleAppeal({appealId:W._id,action:ee,adminReason:K});e.success?(c.C.success("申诉处理成功"),Y(!1),X(null),J(""),sB()):c.C.error(e.message||"处理失败")}catch(e){console.error("处理申诉失败:",e),c.C.error(e.message||"处理申诉失败")}finally{G(null)}},sX=e=>new Date(e).toLocaleString("zh-CN"),sK=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},sJ=e=>{switch(e){case"pending":return"待处理";case"approved":return"已通过";case"rejected":return"已驳回";default:return"未知"}},sQ=async()=>{if(!eo||!ep.trim()){c.C.error("请填写删除原因");return}try{let e=await i.petAPI.adminDeletePost({postId:eo._id,reason:ep});e.success?(c.C.success("帖子删除成功"),em(!1),eh(null),eu(""),eg.trim()&&sH(eg.trim(),ey)):c.C.error(e.message||"删除失败")}catch(e){console.error("删除帖子失败:",e),c.C.error(e.message||"删除帖子失败")}},sY=async(e,s)=>{try{await i.petAPI.togglePostPin({post_id:e,is_pinned:s}),ec(t=>t.map(t=>t._id===e?{...t,is_pinned:s}:t)),c.C.success(s?"帖子已置顶":"已取消置顶")}catch(e){console.error("置顶操作失败:",e),c.C.error("操作失败")}},s0=async(e,s)=>{try{let t=await i.petAPI.adjustPostExposure({post_id:e,adjustment:s});t.success?(ec(t=>t.map(t=>t._id===e?{...t,exposure_score:(t.exposure_score||50)+s}:t)),c.C.success(`曝光度${s>0?"增加":"减少"}${Math.abs(s)}分`)):c.C.error(t.message||"调整失败")}catch(e){console.error("调整曝光度失败:",e),c.C.error("调整失败")}},s2=async e=>{try{let s=await i.petAPI.takeDownPost({post_id:e});s.success?(ec(s=>s.map(s=>s._id===e?{...s,status:"taken_down",exposure_score:0}:s)),c.C.success("帖子已下架")):c.C.error(s.message||"下架失败")}catch(e){console.error("下架帖子失败:",e),c.C.error("下架失败")}},s1=e=>{let s={active:{label:"投放中",color:"bg-green-100 text-green-800"},paused:{label:"已暂停",color:"bg-yellow-100 text-yellow-800"},expired:{label:"已过期",color:"bg-red-100 text-red-800"},pending:{label:"待审核",color:"bg-blue-100 text-blue-800"},inactive:{label:"未启用",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.pending;return a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t.color}`,children:t.label})},s6=e=>`\xa5${e.toFixed(2)}`,s4=e=>`${(100*e).toFixed(2)}%`,s3=e=>{switch(e){case"CONTEST":default:return"\uD83C\uDFC6";case"VOTING":return"\uD83D\uDDF3️";case"DISCUSSION":return"\uD83D\uDCAC"}},s5=e=>{switch(e){case"CONTEST":return"评选竞赛";case"VOTING":return"投票话题";case"DISCUSSION":return"讨论活动";default:return"未知类型"}},s7=e=>{let s={DRAFT:{label:"草稿",color:"bg-yellow-100 text-yellow-800"},ACTIVE:{label:"进行中",color:"bg-green-100 text-green-800"},ENDED:{label:"已结束",color:"bg-blue-100 text-blue-800"},ARCHIVED:{label:"已归档",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.DRAFT;return a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t.color}`,children:t.label})},s8=e=>new Date(e).toLocaleDateString("zh-CN"),s9=async()=>{try{if(sE(!0),eY(!0),eO.maxImageSize<=0||eO.maxImageSize>100){c.C.error("图片大小限制必须在1-100MB之间");return}if(eO.maxImagesPerPost<=0||eO.maxImagesPerPost>20){c.C.error("每帖图片数量必须在1-20张之间");return}if(eO.normalUserPostLimit<=0||eO.normalUserPostLimit>1e3){c.C.error("普通用户帖子上限必须在1-1000之间");return}if(eO.superUserPostLimit<=0||eO.superUserPostLimit>5e3){c.C.error("超级用户帖子上限必须在1-5000之间");return}if(eO.normalUserDailyPostLimit<=0||eO.normalUserDailyPostLimit>50){c.C.error("普通用户每日发帖上限必须在1-50之间");return}if(eO.vipUserDailyPostLimit<=0||eO.vipUserDailyPostLimit>100){c.C.error("VIP用户每日发帖上限必须在1-100之间");return}if(eK.post_report_threshold<=0||eK.post_report_threshold>100){c.C.error("帖子举报阈值必须在1-100之间");return}if(eK.user_report_threshold<=0||eK.user_report_threshold>100){c.C.error("用户举报阈值必须在1-100之间");return}await i.petAPI.updateSystemSettings(eO);let e=await i.petAPI.updateReportThresholds(eK);if(!e.success)throw Error(e.message||"保存举报阈值失败");c.C.success("所有设置保存成功")}catch(e){console.error("保存设置失败:",e),c.C.error("保存设置失败")}finally{sE(!1),eY(!1)}},te=async()=>{if(!e0.trim()){c.C.error("请输入用户ID");return}try{e7(!0),e6(null),e3(null),sD(null),console.log("开始搜索用户:",e0.trim());let e=await i.petAPI.getUserInfo({userId:e0.trim()});if(console.log("用户搜索结果:",e),!e.success){let e="该用户不存在，请检查用户ID是否正确";console.log("用户不存在，显示错误信息"),c.C.error(e),sD(e),e6(null),e3(null);return}if(!e.data||!e.data.userInfo||!e.data.userInfo._id){let e="该用户不存在，请检查用户ID是否正确";console.log("用户数据无效，显示错误信息"),c.C.error(e),sD(e),e6(null),e3(null);return}console.log("用户存在，设置用户数据");let s={id:e.data.userInfo._id,user_id:e.data.userInfo.user_id,nickname:e.data.userInfo.nickname,email:e.data.userInfo.email,avatar:e.data.userInfo.avatar,created_at:e.data.userInfo.created_at,creditInfo:e.data.creditInfo,vipInfo:e.data.vipInfo};e6(s),sD(null);let t=[ts(e0.trim()).catch(e=>{console.error("加载用户权限失败:",e),c.C.warning("权限加载失败")}),ta(e0.trim()).catch(e=>{console.error("加载用户信用分失败:",e),c.C.warning("信用分加载失败")}),tl(e0.trim()).catch(e=>{console.error("加载VIP用户信息失败:",e),c.C.warning("VIP信息加载失败")})];await Promise.allSettled(t),console.log("所有用户数据加载完成"),c.C.success("用户信息加载成功")}catch(s){console.error("搜索用户失败:",s);let e="搜索失败，请检查网络连接或稍后重试";c.C.error(e),sD(e),e6(null),e3(null)}finally{e7(!1)}},ts=async e=>{try{e9(!0),console.log("开始加载用户权限:",e);let s=await i.petAPI.getTargetUserPermissions({targetUserId:e});(console.log("权限API调用结果:",s),s.success)?(console.log("权限加载成功:",s.data),e3(s.data)):(console.log("权限API调用失败，使用默认权限"),e3({canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!0,canPublishPost:!0,bannedUntil:null,banReason:null}),c.C.warning("无法获取用户权限，已设置为默认权限"))}catch(e){console.error("加载用户权限失败:",e),e3({canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!0,canPublishPost:!0,bannedUntil:null,banReason:null}),c.C.warning("加载权限失败，已设置为默认权限")}finally{e9(!1)}},tt=async e=>{if(!e1){c.C.error("请先搜索用户");return}try{sS(!0);let s=await i.petAPI.updateUserPermissions({targetUserId:e1.id,permissions:e});s.success?(e3(e),c.C.success("用户权限更新成功")):c.C.error(s.message||"更新权限失败")}catch(e){console.error("更新用户权限失败:",e),c.C.error("更新权限失败")}finally{sS(!1)}},ta=async e=>{try{console.log("开始加载用户信用分:",e),sa(!0);let s=await i.petAPI.getUserCreditScore({userId:e});if(console.log("信用分API调用结果:",s),s.success)console.log("信用分加载成功:",s.data),ss(s.data),sM(s.data.daily_post_limit||5);else{console.log("用户没有信用分记录，创建默认记录");let s={user_id:e,credit_score:50,is_super_user:!1,daily_post_limit:5,last_updated:new Date().toISOString()};ss(s),sM(5)}}catch(s){console.error("加载用户信用分失败:",s),ss({user_id:e,credit_score:50,is_super_user:!1,daily_post_limit:5,last_updated:new Date().toISOString()}),sM(5)}finally{sa(!1)}},tr=async()=>{if(!e1||!si.reason.trim()){c.C.error("请填写调整原因");return}if(0===si.score){c.C.error("请输入调整分数");return}try{let e=await i.petAPI.updateUserCreditScore({userId:e1.user_id,creditScore:si.score,reason:si.reason});e.success?(await ta(e1.user_id),sl(!1),sc({score:0,reason:""}),c.C.success("信用分调整成功")):c.C.error(e.message||"调整失败")}catch(e){console.error("调整信用分失败:",e),c.C.error("调整失败")}},tl=async e=>{try{console.log("开始加载VIP用户信息:",e),sm(!0);let s=await i.petAPI.getVipUserInfo({user_id:e});console.log("VIP用户信息API调用结果:",s),s.success?(console.log("VIP用户信息加载成功:",s.data),sn(s.data)):(console.log("用户不是VIP用户"),sn({user_id:e,is_vip:!1,vip_start_time:null,vip_end_time:null,vip_benefits:{daily_post_limit:5,credit_score_limit:100}}))}catch(e){console.error("加载VIP用户信息失败:",e),sn(null)}finally{sm(!1)}},ti=async()=>{if(e1)try{sm(!0);let e=await i.petAPI.setVipUser({user_id:e1.user_id,duration_days:sp,benefits:sg});e.success?(c.C.success("设置VIP用户成功"),sh(!1),await tl(e1.user_id),await ta(e1.user_id)):c.C.error(e.message||"设置VIP用户失败")}catch(e){console.error("设置VIP用户失败:",e),c.C.error("设置VIP用户失败")}finally{sm(!1)}},tc=async()=>{if(e1)try{sm(!0);let e=await i.petAPI.updateUserCreditScore({userId:e1.user_id,creditScore:se.credit_score,dailyPostLimit:sA,reason:`管理员调整每日发帖限制为${sA}条`});e.success?(c.C.success("每日发帖限制调整成功"),sL(!1),await ta(e1.user_id)):c.C.error(e.message||"调整每日发帖限制失败")}catch(e){console.error("调整每日发帖限制失败:",e),c.C.error("调整每日发帖限制失败")}finally{sm(!1)}},td=async()=>{try{sv(!0);let e=await i.petAPI.getVipUserList({limit:50,offset:0,status:sf});e.success?sb(e.data):(c.C.error(e.message||"加载VIP用户列表失败"),sb([]))}catch(e){console.error("加载VIP用户列表失败:",e),c.C.error("加载VIP用户列表失败"),sb([])}finally{sv(!1)}},tn=e=>{s_(e),sI(!0)},tx=async(e,s)=>{try{sm(!0);let t=await i.petAPI.updateVipUser({user_id:e,duration_days:s});t.success?(c.C.success("延长VIP时间成功"),await td(),sI(!1)):c.C.error(t.message||"延长VIP时间失败")}catch(e){console.error("延长VIP时间失败:",e),c.C.error("延长VIP时间失败")}finally{sm(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>e.push("/admin"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(x.Z,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"超级管理员控制台"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["欢迎，",m?.username||"管理员"]})]})]})]}),a.jsx(d.Z,{variant:"outline",onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),e.push("/admin")},children:"退出登录"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1 overflow-x-auto",children:[(0,a.jsxs)("button",{onClick:()=>t("dashboard"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"dashboard"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(o,{className:"w-4 h-4"}),a.jsx("span",{children:"数据概览"})]}),(0,a.jsxs)("button",{onClick:()=>t("admins"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"admins"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(h,{className:"w-4 h-4"}),a.jsx("span",{children:"管理员管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("appeals"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"appeals"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(p,{className:"w-4 h-4"}),a.jsx("span",{children:"申诉管理"})]}),(0,a.jsxs)("button",{onClick:()=>{t("vip"),0===sy.length&&td()},className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"vip"===s?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(u,{className:"w-4 h-4"}),a.jsx("span",{children:"VIP管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("users"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"users"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(g.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"用户管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("ads"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"ads"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),a.jsx("span",{children:"广告管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("posts"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"posts"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"})}),a.jsx("span",{children:"帖子管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("activities"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"activities"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),a.jsx("span",{children:"活动管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("content"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"content"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(j,{className:"w-4 h-4"}),a.jsx("span",{children:"内容管理"})]}),(0,a.jsxs)("button",{onClick:()=>t("settings"),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${"settings"===s?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(y.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"系统设置"})]})]}),"dashboard"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(h,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总用户数"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalUsers||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(j,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总宝贝数"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalPosts||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(g.Z,{className:"w-6 h-6 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增用户"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.yesterdayNewUsers||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-teal-100 rounded-lg",children:a.jsx(b.Z,{className:"w-6 h-6 text-teal-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增宝贝"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.yesterdayNewPosts||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:a.jsx(N.Z,{className:"w-6 h-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"待处理举报"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalReports||0})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:a.jsx(p,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"待处理申诉"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.totalAppeals||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-indigo-100 rounded-lg",children:a.jsx(v.Z,{className:"w-6 h-6 text-indigo-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"活跃用户"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.activeUsers||0})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-pink-100 rounded-lg",children:a.jsx(f,{className:"w-6 h-6 text-pink-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"在线用户"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:T?.onlineUsers||0})]})]})})]})]}),"admins"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 flex items-center justify-between",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"管理员列表"}),m?.role==="super_admin"&&(0,a.jsxs)(d.Z,{onClick:()=>ea(!0),className:"flex items-center space-x-2",children:[a.jsx(b.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"创建管理员"})]})]}),H?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===R.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(h,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无管理员"}),a.jsx("p",{className:"text-gray-500",children:"点击上方按钮创建第一个管理员"})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"管理员信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色权限"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:R.filter(e=>"superadminTT"!==e.username).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.username}),a.jsx("div",{className:"text-sm text-gray-500",children:"管理员账号"})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:"super_admin"===e.role?"超级管理员":"普通管理员"}),a.jsx("div",{className:"text-sm text-gray-500",children:"super_admin"===e.role?"拥有所有权限":"拥有所有业务权限"})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"active"===e.status?"bg-green-100 text-green-800":"suspended"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:"active"===e.status?"正常":"suspended"===e.status?"暂停":"禁用"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("zh-CN")}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[m?.role==="super_admin"&&!e.is_system_account&&a.jsx("button",{onClick:()=>sG(e._id),className:"text-red-600 hover:text-red-900",title:"删除管理员",children:a.jsx(w.Z,{className:"w-4 h-4"})}),!(m?.role==="super_admin"&&!e.is_system_account)&&a.jsx("span",{className:"text-gray-400 text-sm",children:"无操作权限"})]})})]},e._id))})]})})]}),"appeals"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"申诉列表"})}),H?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===q.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(p,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无申诉"}),a.jsx("p",{className:"text-gray-500",children:"目前没有需要处理的申诉"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:q.map(e=>a.jsx("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${sK(e.status)}`,children:sJ(e.status)}),a.jsx("span",{className:"text-sm text-gray-500",children:"post"===e.type?"帖子申诉":"用户申诉"}),a.jsx("span",{className:"text-sm text-gray-500",children:sX(e.created_at)})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-900 mb-2",children:[a.jsx("span",{className:"font-medium",children:"申诉理由："}),e.reason]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["举报ID: ",e.report_id," | 申诉人ID: ",e.appellant_id]}),e.admin_reason&&a.jsx("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-900",children:[a.jsx("span",{className:"font-medium",children:"管理员回复："}),e.admin_reason]})})]}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,a.jsxs)(d.Z,{size:"sm",variant:"outline",onClick:()=>{X(e),es("approved"),Y(!0)},disabled:O===e._id,children:[a.jsx(k.Z,{className:"w-4 h-4 mr-1"}),"通过"]}),(0,a.jsxs)(d.Z,{size:"sm",variant:"outline",onClick:()=>{X(e),es("rejected"),Y(!0)},disabled:O===e._id,children:[a.jsx(_.Z,{className:"w-4 h-4 mr-1"}),"驳回"]})]})]})},e._id))})]}),"users"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(C.Z,{className:"w-6 h-6 text-blue-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"用户搜索"})]}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"输入用户ID搜索用户信息和权限设置"})]}),a.jsx("div",{className:"px-6 py-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户ID"}),a.jsx("input",{type:"text",placeholder:"请输入用户ID...",value:e0,onChange:e=>{e2(e.target.value),sZ&&sD(null)},onKeyPress:e=>"Enter"===e.key&&te(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),a.jsx("div",{className:"pt-6",children:(0,a.jsxs)(d.Z,{onClick:te,loading:e5,disabled:e5||!e0.trim(),className:"flex items-center space-x-2",children:[a.jsx(C.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e5?"搜索中...":"搜索用户"})]})})]})})]}),e1&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(I.Z,{className:"w-6 h-6 text-green-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"用户信息"})]})}),(0,a.jsxs)("div",{className:"px-6 py-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("img",{src:e1.avatar_url||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:e1.nickname,className:"w-16 h-16 rounded-full"}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:e1.nickname}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",e1.id]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"注册时间"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900",children:new Date(e1.created_at).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"最后登录"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900",children:e1.last_login?new Date(e1.last_login).toLocaleDateString("zh-CN"):"未知"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h5",{className:"text-sm font-medium text-gray-700",children:"统计信息"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e1.posts_count||0}),a.jsx("div",{className:"text-sm text-blue-800",children:"发布帖子"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:e1.likes_count||0}),a.jsx("div",{className:"text-sm text-green-800",children:"获得点赞"})]})]})]})]}),se&&(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[a.jsx("div",{className:"mb-4",children:a.jsx("h5",{className:"text-sm font-medium text-gray-700",children:"信用分管理"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsx("div",{className:`rounded-lg p-4 ${se.credit_score>=80?"bg-green-50":se.credit_score>=60?"bg-yellow-50":"bg-red-50"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:`text-3xl font-bold ${se.credit_score>=80?"text-green-600":se.credit_score>=60?"text-yellow-600":"text-red-600"}`,children:se.credit_score}),a.jsx("div",{className:`text-sm ${se.credit_score>=80?"text-green-800":se.credit_score>=60?"text-yellow-800":"text-red-800"}`,children:"当前信用分"})]}),a.jsx("button",{onClick:()=>sl(!0),className:"px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"调整"})]})}),a.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-lg font-semibold text-gray-900",children:se.daily_post_limit||5}),a.jsx("div",{className:"text-sm text-gray-600",children:"每日发帖限制"})]}),a.jsx("button",{onClick:()=>sL(!0),className:"px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"调整"})]})}),a.jsx("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-lg font-semibold text-purple-600",children:sd?.is_vip?"VIP用户":"普通用户"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户状态"})]}),!sd?.is_vip&&a.jsx("button",{onClick:()=>sh(!0),disabled:sx,className:"px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors disabled:opacity-50",children:"升级VIP"})]})})]})]})]})]}),e1&&e4&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(x.Z,{className:"w-6 h-6 text-purple-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"权限管理"})]}),(0,a.jsxs)(d.Z,{onClick:()=>tt(e4),loading:sP,disabled:sP,className:"flex items-center space-x-2",children:[a.jsx(P,{className:"w-4 h-4"}),a.jsx("span",{children:sP?"保存中...":"保存权限"})]})]})}),e8?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-2",children:"加载权限中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h5",{className:"text-sm font-medium text-gray-700",children:"基础权限"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e4.canLike,onChange:e=>e3(s=>({...s,canLike:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"点赞权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e4.canDislike,onChange:e=>e3(s=>({...s,canDislike:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"不喜欢权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e4.canContact,onChange:e=>e3(s=>({...s,canContact:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"联系权限"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h5",{className:"text-sm font-medium text-gray-700",children:"高级权限"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e4.canPublishPost,onChange:e=>e3(s=>({...s,canPublishPost:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"发帖权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e4.canReportPost,onChange:e=>e3(s=>({...s,canReportPost:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"举报帖子权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e4.canReportUser,onChange:e=>e3(s=>({...s,canReportUser:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"举报用户权限"})]})]})]})]}),a.jsx("div",{className:"mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(x.Z,{className:"w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-gray-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"权限说明"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[a.jsx("li",{children:"点赞权限：控制用户是否可以给帖子点赞"}),a.jsx("li",{children:"不喜欢权限：控制用户是否可以给帖子点不喜欢"}),a.jsx("li",{children:"联系权限：控制用户是否可以查看和使用联系方式"}),a.jsx("li",{children:"发帖权限：控制用户是否可以发布新帖子"}),a.jsx("li",{children:"举报权限：控制用户是否可以举报帖子和其他用户"})]})]})]})})]})]}),!e1&&!e5&&a.jsx("div",{className:"bg-white rounded-lg shadow",children:a.jsx("div",{className:"px-6 py-12 text-center",children:sZ?(0,a.jsxs)(a.Fragment,{children:[a.jsx(S.Z,{className:"w-16 h-16 text-red-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-red-900 mb-2",children:"搜索失败"}),a.jsx("p",{className:"text-red-600 mb-4",children:sZ}),a.jsx(d.Z,{onClick:()=>{sD(null),e2("")},variant:"outline",className:"text-gray-600 border-gray-300 hover:bg-gray-50",children:"重新搜索"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(C.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"请搜索用户"}),a.jsx("p",{className:"text-gray-500",children:"输入用户ID来查看用户信息和管理权限"})]})})})]}),"posts"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"帖子管理"})}),a.jsx("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"按用户ID搜索"}),a.jsx("input",{type:"text",value:eg,onChange:e=>ej(e.target.value),placeholder:"输入用户ID",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"按宠物品种筛选"}),(0,a.jsxs)("select",{value:ey,onChange:e=>eb(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"",children:"全部品种"}),eN.map(e=>a.jsx("optgroup",{label:e.name,children:e.subcategories?.map(e=>a.jsx("option",{value:e.name,children:e.name},e._id))},e._id))]})]}),(0,a.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>{if(!eg.trim()){c.C.error("请输入用户ID进行搜索");return}sH(eg.trim(),ey)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),"搜索用户帖子"]}),a.jsx("button",{onClick:()=>{ej(""),eb(""),ec([])},className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:"重置"})]})]})}),ed?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):eg.trim()?0===ei.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(j,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"该用户暂无帖子"}),(0,a.jsxs)("p",{className:"text-gray-500",children:["用户ID: ",eg]})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"帖子信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"作者"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"互动统计"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"举报统计"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"曝光度/评分"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"发布时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:ei.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.images&&e.images[0]&&a.jsx("img",{src:e.images[0],alt:e.title,className:"h-12 w-12 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[a.jsx("a",{href:`/post/${e._id}`,target:"_blank",className:"text-sm font-medium text-blue-600 hover:text-blue-800 max-w-xs truncate block",children:e.title}),a.jsx("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.category," • \xa5",e.price]})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("img",{src:e.author_info?.avatar_url||"/default-avatar.png",alt:e.author_info?.nickname,className:"h-8 w-8 rounded-full mr-2"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.author_info?.nickname||"未知用户"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",e.user_id?.slice(0,8),"..."]})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["\uD83D\uDC4D ",e.likes_count||0]}),(0,a.jsxs)("div",{children:["\uD83D\uDC4E ",e.dislikes_count||0]}),(0,a.jsxs)("div",{children:["\uD83D\uDC96 ",e.wants_count||0]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-red-600",children:["理由1: ",e.report_reason1_count||0]}),(0,a.jsxs)("div",{className:"text-red-600",children:["理由2: ",e.report_reason2_count||0]}),(0,a.jsxs)("div",{className:"text-orange-600",children:["总计: ",e.reports_count||0]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:`font-medium ${(e.exposure_score||50)>40?"text-green-600":"text-red-600"}`,children:["曝光度: ",e.exposure_score||50,"分"]}),(0,a.jsxs)("div",{className:"text-blue-600",children:["评分: ",(e.avg_rating||0).toFixed(1),"⭐"]}),a.jsx("div",{className:"text-xs",children:e.is_pinned?"\uD83D\uDCCC 已置顶":(e.exposure_score||50)>40?"\uD83D\uDC41️ 可见":"\uD83D\uDEAB 隐藏"})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.timeAgo}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx("button",{onClick:()=>s0(e._id,10),className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200",title:"增加曝光度 +10",children:"+10"}),a.jsx("button",{onClick:()=>s0(e._id,50),className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200",title:"增加曝光度 +50",children:"+50"}),a.jsx("button",{onClick:()=>s0(e._id,100),className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200",title:"增加曝光度 +100",children:"+100"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx("button",{onClick:()=>s0(e._id,-10),className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"减少曝光度 -10",children:"-10"}),a.jsx("button",{onClick:()=>s0(e._id,-50),className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"减少曝光度 -50",children:"-50"}),a.jsx("button",{onClick:()=>s0(e._id,-100),className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"减少曝光度 -100",children:"-100"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx("button",{onClick:()=>sY(e._id,!e.is_pinned),className:`px-2 py-1 text-xs rounded ${e.is_pinned?"bg-orange-100 text-orange-700 hover:bg-orange-200":"bg-blue-100 text-blue-700 hover:bg-blue-200"}`,title:e.is_pinned?"取消置顶":"置顶帖子",children:e.is_pinned?"取消置顶":"置顶"}),a.jsx("button",{onClick:()=>s2(e._id),className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200",title:"下架帖子",children:"下架"}),a.jsx("button",{onClick:()=>{eh(e),em(!0)},className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"删除帖子",children:"删除"})]})]})})]},e._id))})]})}):(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(j,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"请搜索用户帖子"}),a.jsx("p",{className:"text-gray-500",children:"输入用户ID并点击搜索按钮来查看该用户的所有帖子"})]})]}),"content"===s&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"内容管理"})}),(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(j,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"内容管理功能"}),a.jsx("p",{className:"text-gray-500",children:"此功能正在开发中，敬请期待..."})]})]}),"vip"===s&&a.jsx("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u,{className:"w-6 h-6 text-purple-600"}),a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"VIP用户管理"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:sf,onChange:e=>{sw(e.target.value),td()},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",children:[a.jsx("option",{value:"all",children:"全部VIP用户"}),a.jsx("option",{value:"active",children:"有效VIP用户"}),a.jsx("option",{value:"expired",children:"过期VIP用户"})]}),a.jsx("button",{onClick:td,disabled:sN,className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors",children:sN?"加载中...":"刷新"})]})]})}),a.jsx("div",{className:"p-6",children:sN?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"加载VIP用户列表..."})]}):0===sy.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(u,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"暂无VIP用户"})]}):a.jsx("div",{className:"space-y-4",children:sy.map(e=>(0,a.jsxs)("div",{className:`border rounded-lg p-4 ${e.is_vip?"border-purple-200 bg-purple-50":"border-gray-200 bg-gray-50"}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("img",{src:e.user_info?.avatar||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:e.user_info?.nickname||"未知用户",className:"w-12 h-12 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:e.user_info?.nickname||"未知用户"}),e.is_vip&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:[a.jsx(u,{className:"w-3 h-3 mr-1"}),"VIP"]}),e.is_expired&&a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"已过期"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",e.user_id]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.is_vip?`剩余 ${e.remaining_days} 天`:"已过期"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["到期时间: ",new Date(e.vip_end_time).toLocaleDateString("zh-CN")]})]}),a.jsx("button",{onClick:()=>tn(e),className:"px-3 py-1 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"管理"})]})]}),a.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-600",children:"每日发帖:"}),a.jsx("span",{className:"ml-1 font-medium",children:e.vip_benefits?.daily_post_limit||5})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-600",children:"信用分上限:"}),a.jsx("span",{className:"ml-1 font-medium",children:e.vip_benefits?.credit_score_limit||100})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-600",children:"专属标识:"}),a.jsx("span",{className:"ml-1 font-medium",children:e.vip_benefits?.special_badge?"✓":"✗"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-600",children:"优先支持:"}),a.jsx("span",{className:"ml-1 font-medium",children:e.vip_benefits?.priority_support?"✓":"✗"})]})]})})]},e._id))})})]})}),"ads"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(Z.Z,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总展示量"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ef.reduce((e,s)=>e+s.impressions,0).toLocaleString()})]})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(o,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总点击量"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ef.reduce((e,s)=>e+s.clicks,0).toLocaleString()})]})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:a.jsx(D.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"总收益"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:s6(ef.reduce((e,s)=>e+s.spent,0))})]})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(v.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"活跃广告"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ef.filter(e=>"active"===e.status).length})]})]})})]}),a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:"-mb-px flex space-x-8",children:[{key:"ads",label:"广告列表",icon:Z.Z},{key:"positions",label:"广告位管理",icon:o},{key:"statistics",label:"数据统计",icon:D.Z}].map(e=>(0,a.jsxs)("button",{onClick:()=>eS(e.key),className:`${eP===e.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`,children:[a.jsx(e.icon,{className:"w-5 h-5"}),a.jsx("span",{children:e.label})]},e.key))})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("div",{children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"ads"===eP?"广告列表":"positions"===eP?"广告位管理":"数据统计"})}),"ads"===eP&&(0,a.jsxs)(d.Z,{onClick:()=>eD(!0),className:"flex items-center space-x-2",children:[a.jsx(b.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"创建广告"})]})]}),"ads"===eP&&a.jsx("div",{className:"bg-white shadow rounded-lg",children:eC?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"数据"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"收益"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===ef.length?a.jsx("tr",{children:a.jsx("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无广告数据"})}):ef.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.image_url&&a.jsx("img",{src:e.image_url,alt:e.title,className:"h-10 w-10 rounded object-cover mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.title}),a.jsx("div",{className:"text-sm text-gray-500",children:e.advertiser_name})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.position_name}),a.jsx("div",{className:"text-sm text-gray-500",children:e.ad_type})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s1(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["展示: ",e.impressions.toLocaleString()]}),(0,a.jsxs)("div",{children:["点击: ",e.clicks.toLocaleString()]}),(0,a.jsxs)("div",{children:["CTR: ",s4(e.ctr)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["预算: ",s6(e.budget)]}),(0,a.jsxs)("div",{children:["已花费: ",s6(e.spent)]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"text-blue-600 hover:text-blue-900",children:a.jsx(Z.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-green-600 hover:text-green-900",children:a.jsx(V.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?a.jsx(L.Z,{className:"w-4 h-4"}):a.jsx(v.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-red-600 hover:text-red-900",children:a.jsx(w.Z,{className:"w-4 h-4"})})]})})]},e._id))})]})})}),"positions"===eP&&a.jsx("div",{className:"bg-white shadow rounded-lg",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"尺寸"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:ek.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),a.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.page}),a.jsx("div",{className:"text-sm text-gray-500",children:e.location})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.width," \xd7 ",e.height]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.ad_type}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s1(e.status)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"text-green-600 hover:text-green-900",children:a.jsx(V.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?a.jsx(L.Z,{className:"w-4 h-4"}):a.jsx(v.Z,{className:"w-4 h-4"})})]})})]},e.position_id))})]})})}),"statistics"===eP&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"用户体验策略"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"广告频率控制"}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日最大展示次数"}),a.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:10,min:1,max:50})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最小展示间隔（分钟）"}),a.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:30,min:5,max:120})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"respectUserChoice",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),a.jsx("label",{htmlFor:"respectUserChoice",className:"ml-2 block text-sm text-gray-900",children:"尊重用户隐藏选择"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"adaptiveFrequency",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),a.jsx("label",{htmlFor:"adaptiveFrequency",className:"ml-2 block text-sm text-gray-900",children:"自适应展示频率"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"广告位策略"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"首页横幅"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"启用"}),a.jsx("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"信息流广告"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户友好度：中"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"启用"}),a.jsx("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"详情页底部"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"启用"}),a.jsx("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"启动弹窗"}),a.jsx("div",{className:"text-sm text-red-600",children:"用户友好度：低"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"禁用"}),a.jsx("input",{type:"checkbox",className:"h-4 w-4 text-blue-600"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"用户体验建议"}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"基于用户行为数据的智能推荐"})]}),a.jsx(d.Z,{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"保存设置"})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm font-medium text-green-800",children:"推荐"})]}),a.jsx("p",{className:"text-sm text-green-700 mt-1",children:"原生信息流广告，用户接受度高"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm font-medium text-yellow-800",children:"谨慎"})]}),a.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"横幅广告需要控制频率"})]}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm font-medium text-red-800",children:"避免"})]}),a.jsx("p",{className:"text-sm text-red-700 mt-1",children:"弹窗广告容易引起用户反感"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"广告效果分析"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:"92%"}),a.jsx("div",{className:"text-sm text-gray-600",children:"用户满意度"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:"3.2%"}),a.jsx("div",{className:"text-sm text-gray-600",children:"平均点击率"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:"15s"}),a.jsx("div",{className:"text-sm text-gray-600",children:"平均停留时间"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-purple-600",children:"8%"}),a.jsx("div",{className:"text-sm text-gray-600",children:"广告隐藏率"})]})]})]})]})]}),"activities"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"活动管理"}),a.jsx("p",{className:"text-gray-600",children:"管理社区活动和系统配置"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(d.Z,{onClick:()=>e$(!0),variant:"outline",className:"flex items-center space-x-2",children:[a.jsx(y.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"系统设置"})]}),(0,a.jsxs)(d.Z,{onClick:()=>eE(!0),className:"flex items-center space-x-2",children:[a.jsx(b.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"创建活动"})]})]})]}),a.jsx("div",{className:`p-4 rounded-lg ${eU?.enabled?"bg-green-50 border border-green-200":"bg-yellow-50 border border-yellow-200"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:`h-3 w-3 rounded-full mr-3 ${eU?.enabled?"bg-green-500":"bg-yellow-500"}`}),(0,a.jsxs)("span",{className:`font-medium ${eU?.enabled?"text-green-800":"text-yellow-800"}`,children:["活动系统状态：",eU?.enabled?"已启用":"已禁用"]}),!eU?.enabled&&a.jsx("span",{className:"ml-2 text-yellow-700",children:"（用户端不显示活动入口）"})]})}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow flex space-x-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态筛选"}),(0,a.jsxs)("select",{value:eq,onChange:e=>eF(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[a.jsx("option",{value:"all",children:"全部状态"}),a.jsx("option",{value:"DRAFT",children:"草稿"}),a.jsx("option",{value:"ACTIVE",children:"进行中"}),a.jsx("option",{value:"ENDED",children:"已结束"}),a.jsx("option",{value:"ARCHIVED",children:"已归档"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型筛选"}),(0,a.jsxs)("select",{value:eH,onChange:e=>eB(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[a.jsx("option",{value:"all",children:"全部类型"}),a.jsx("option",{value:"CONTEST",children:"评选竞赛"}),a.jsx("option",{value:"VOTING",children:"投票话题"}),a.jsx("option",{value:"DISCUSSION",children:"讨论活动"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"活动列表"})}),eA?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"活动信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"参与数据"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===eV.length?a.jsx("tr",{children:a.jsx("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无活动数据"})}):eV.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),a.jsx("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-lg mr-2",children:s3(e.type)}),a.jsx("span",{className:"text-sm text-gray-900",children:s5(e.type)})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s7(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["开始：",s8(e.start_time)]}),(0,a.jsxs)("div",{children:["结束：",s8(e.end_time)]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["持续 ",e.duration_days," 天"]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["投票：",e.statistics_summary?.total_votes||0]}),(0,a.jsxs)("div",{children:["评论：",e.statistics_summary?.total_comments||0]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>window.open(`/activities/${e._id}`,"_blank"),className:"text-blue-600 hover:text-blue-900",title:"查看活动",children:a.jsx(Z.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>c.C.info("编辑功能开发中"),className:"text-green-600 hover:text-green-900",title:"编辑活动",children:a.jsx(V.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>{sq({title:"删除活动",message:"确定要删除这个活动吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:()=>{c.C.info("删除功能开发中")}})},className:"text-red-600 hover:text-red-900",title:"删除活动",children:a.jsx(w.Z,{className:"h-4 w-4"})})]})})]},e._id))})]})})]})]}),"settings"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"系统设置"}),a.jsx("p",{className:"text-gray-600",children:"配置系统参数和规则"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(A,{className:"w-6 h-6 text-blue-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"图片上传设置"})]}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户上传图片的限制和规则"})]}),eW?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"单张图片大小限制 (MB)"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"100",step:"1",value:eO.maxImageSize,onChange:e=>eG(s=>({...s,maxImageSize:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",eO.maxImageSize,"MB"]})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"建议设置在5-30MB之间，过大会影响上传速度"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每帖最大图片数量"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"20",step:"1",value:eO.maxImagesPerPost,onChange:e=>eG(s=>({...s,maxImagesPerPost:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",eO.maxImagesPerPost,"张"]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"支持的图片格式"}),a.jsx("div",{className:"space-y-2",children:["image/jpeg","image/png","image/webp","image/gif"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:eO.allowedImageTypes.includes(e),onChange:s=>{s.target.checked?eG(s=>({...s,allowedImageTypes:[...s.allowedImageTypes,e]})):eG(s=>({...s,allowedImageTypes:s.allowedImageTypes.filter(s=>s!==e)}))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:e.replace("image/","").toUpperCase()})]},e))})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(M.Z,{className:"w-6 h-6 text-red-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"举报阈值设置"})]}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"配置自动处理举报的阈值和规则"})]}),eQ?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-2",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"帖子举报阈值"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"100",value:eK.post_report_threshold,onChange:e=>eJ(s=>({...s,post_report_threshold:parseInt(e.target.value)||5})),className:"w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),a.jsx("span",{className:"text-sm text-gray-600",children:"人举报后自动隐藏帖子"})]}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"当帖子被举报达到此数量时，系统将自动隐藏该帖子"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户举报阈值"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"100",value:eK.user_report_threshold,onChange:e=>eJ(s=>({...s,user_report_threshold:parseInt(e.target.value)||10})),className:"w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),a.jsx("span",{className:"text-sm text-gray-600",children:"人举报后发送警告通知"})]}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"当用户被举报达到此数量时，系统将发送警告通知"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"自动处理选项"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:eK.auto_hide_posts,onChange:e=>eJ(s=>({...s,auto_hide_posts:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"自动隐藏被举报的帖子"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:eK.auto_warn_users,onChange:e=>eJ(s=>({...s,auto_warn_users:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"自动警告被举报的用户"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:eK.notification_enabled,onChange:e=>eJ(s=>({...s,notification_enabled:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"启用系统通知"})]})]})]}),a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(M.Z,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"注意事项"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[a.jsx("li",{children:"阈值设置过低可能导致误处理，建议帖子阈值3-10，用户阈值5-15"}),a.jsx("li",{children:"被处理的用户可以通过申诉系统申请恢复"}),a.jsx("li",{children:"管理员可以在申诉管理中审核和处理申诉"}),a.jsx("li",{children:"系统通知将发送给被处理的用户"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(D.Z,{className:"w-6 h-6 text-purple-600"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"帖子数量限制"})]}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户发布帖子的数量限制和自动下架规则"})]}),eW?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"text-gray-500 mt-2",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"普通用户帖子上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"10",max:"1000",step:"10",value:eO.normalUserPostLimit,onChange:e=>eG(s=>({...s,normalUserPostLimit:parseInt(e.target.value)||100})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",eO.normalUserPostLimit,"条"]})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"普通用户可发布的公开帖子数量上限"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"超级用户帖子上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"100",max:"5000",step:"50",value:eO.superUserPostLimit,onChange:e=>eG(s=>({...s,superUserPostLimit:parseInt(e.target.value)||500})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",eO.superUserPostLimit,"条"]})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"超级用户可发布的公开帖子数量上限"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"普通用户每日发帖上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"1",max:"50",step:"1",value:eO.normalUserDailyPostLimit,onChange:e=>eG(s=>({...s,normalUserDailyPostLimit:parseInt(e.target.value)||5})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",eO.normalUserDailyPostLimit,"条/天"]})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"普通用户每天可发布的帖子数量上限"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"VIP用户每日发帖上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("input",{type:"number",min:"5",max:"100",step:"5",value:eO.vipUserDailyPostLimit,onChange:e=>eG(s=>({...s,vipUserDailyPostLimit:parseInt(e.target.value)||20})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",eO.vipUserDailyPostLimit,"条/天"]})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"VIP用户每天可发布的帖子数量上限"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"自动下架设置"}),a.jsx("div",{className:"space-y-3",children:(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:eO.autoArchiveEnabled,onChange:e=>eG(s=>({...s,autoArchiveEnabled:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"启用自动下架机制"})]})}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"当用户发布新帖子超过上限时，自动下架最早的帖子并转为草稿"})]}),a.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(D.Z,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"帖子数量限制说明"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[a.jsx("li",{children:"普通用户默认100条帖子上限，超级用户默认500条"}),a.jsx("li",{children:"普通用户默认每日5条发帖限制，VIP用户默认每日20条"}),a.jsx("li",{children:"个人设置的发帖限制优先级高于系统设置"}),a.jsx("li",{children:"发布新帖子时，如果超过上限，最早的帖子会被自动下架"}),a.jsx("li",{children:"下架的帖子会转为草稿，用户登录时可以看到并重新发布"}),a.jsx("li",{children:"草稿在云端保存30天后自动清理"}),a.jsx("li",{children:"用户在发布页面会看到接近上限的提醒"})]})]})]})})]}),(0,a.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between",children:[(0,a.jsxs)(d.Z,{variant:"outline",onClick:()=>{sq({title:"重置设置",message:"确定要重置为默认设置吗？当前的自定义设置将会丢失。",confirmText:"重置",cancelText:"取消",type:"warning",onConfirm:()=>{eG({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10,normalUserPostLimit:100,superUserPostLimit:500,autoArchiveEnabled:!0}),eJ({post_report_threshold:5,user_report_threshold:10,auto_hide_posts:!0,auto_warn_users:!0,notification_enabled:!0}),c.C.success("已重置为默认设置")}})},className:"flex items-center space-x-2",children:[a.jsx(U,{className:"w-4 h-4"}),a.jsx("span",{children:"重置默认"})]}),(0,a.jsxs)(d.Z,{onClick:s9,loading:sR||eQ,disabled:sR||eQ,className:"flex items-center space-x-2",children:[a.jsx(P,{className:"w-4 h-4"}),a.jsx("span",{children:sR||eQ?"保存中...":"保存所有设置"})]})]})]})]})]}),et&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"创建管理员"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["用户名 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("input",{type:"text",value:er.username,onChange:e=>el(s=>({...s,username:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入用户名"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["密码 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("input",{type:"password",value:er.password,onChange:e=>el(s=>({...s,password:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入密码"})]}),a.jsx("div",{children:(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm font-medium text-gray-700",children:"角色权限"}),a.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"普通管理员 - 拥有所有业务权限（除删除其他管理员外）"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[a.jsx(d.Z,{variant:"outline",onClick:()=>{ea(!1),el({username:"",password:"",role:"admin",level:1,permissions:[]})},className:"flex-1",children:"取消"}),a.jsx(d.Z,{onClick:sO,className:"flex-1",children:"创建管理员"})]})]})}),Q&&W&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"approved"===ee?"通过申诉":"驳回申诉"}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"申诉内容："}),a.jsx("p",{className:"text-sm bg-gray-100 p-3 rounded-lg",children:W.reason})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"处理说明"}),a.jsx("textarea",{value:K,onChange:e=>J(e.target.value),placeholder:`请说明${"approved"===ee?"通过":"驳回"}的理由...`,className:"w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(d.Z,{variant:"outline",onClick:()=>{Y(!1),X(null),J("")},className:"flex-1",children:"取消"}),a.jsx(d.Z,{onClick:sW,disabled:O===W._id,className:"flex-1",children:O===W._id?"处理中...":"确认"})]})]})}),ex&&eo&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"删除帖子"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[eo.images&&eo.images[0]&&a.jsx("img",{src:eo.images[0],alt:eo.title,className:"h-16 w-16 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:eo.title}),a.jsx("div",{className:"text-sm text-gray-500",children:eo.description})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["删除原因 ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx("textarea",{value:ep,onChange:e=>eu(e.target.value),placeholder:"请输入删除原因...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none",rows:3})]}),a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"text-sm font-medium text-red-800",children:"警告"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-red-700",children:[a.jsx("p",{children:"删除帖子将同时删除："}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-1",children:[a.jsx("li",{children:"帖子的所有图片文件"}),a.jsx("li",{children:"所有点赞、收藏、评分记录"}),a.jsx("li",{children:"所有相关的举报和联系记录"})]}),a.jsx("p",{className:"mt-2 font-medium",children:"此操作不可撤销！"})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(d.Z,{variant:"outline",onClick:()=>{em(!1),eh(null),eu("")},className:"flex-1",children:"取消"}),a.jsx(d.Z,{onClick:sQ,className:"flex-1 bg-red-600 hover:bg-red-700 text-white",disabled:!ep.trim(),children:"确认删除"})]})]})}),sU&&sT&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsxs)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${"danger"===sT.type?"bg-red-100":"warning"===sT.type?"bg-yellow-100":"bg-blue-100"}`,children:["danger"===sT.type&&a.jsx("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),"warning"===sT.type&&a.jsx("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(!sT.type||"info"===sT.type)&&a.jsx("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:sT.title})]}),a.jsx("div",{className:"mb-6",children:a.jsx("p",{className:"text-gray-600",children:sT.message})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(d.Z,{variant:"outline",onClick:()=>{sT?.onCancel&&sT.onCancel(),sz(!1),s$(null)},className:"flex-1",children:sT.cancelText||"取消"}),a.jsx(d.Z,{onClick:()=>{sT?.onConfirm&&sT.onConfirm(),sz(!1),s$(null)},className:`flex-1 ${"danger"===sT.type?"bg-red-600 hover:bg-red-700 text-white":"warning"===sT.type?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:sT.confirmText||"确认"})]})]})}),sr&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:()=>sl(!1),children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"调整用户信用分"}),a.jsx("button",{onClick:()=>{sl(!1),sc({score:0,reason:""})},className:"text-gray-400 hover:text-gray-600",children:a.jsx(S.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"调整分数"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>sc(e=>({...e,score:-10})),className:"px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm",children:"-10"}),a.jsx("button",{onClick:()=>sc(e=>({...e,score:-5})),className:"px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm",children:"-5"}),a.jsx("input",{type:"number",value:si.score,onChange:e=>sc(s=>({...s,score:parseInt(e.target.value)||0})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"输入分数"}),a.jsx("button",{onClick:()=>sc(e=>({...e,score:5})),className:"px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm",children:"+5"}),a.jsx("button",{onClick:()=>sc(e=>({...e,score:10})),className:"px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm",children:"+10"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"调整原因"}),a.jsx("textarea",{value:si.reason,onChange:e=>sc(s=>({...s,reason:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",rows:3,placeholder:"请输入调整原因..."})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["当前信用分: ",a.jsx("span",{className:"font-medium",children:se?.credit_score||50})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["调整后信用分: ",a.jsx("span",{className:`font-medium ${(se?.credit_score||50)+si.score>=80?"text-green-600":(se?.credit_score||50)+si.score>=60?"text-yellow-600":"text-red-600"}`,children:Math.max(0,Math.min(200,(se?.credit_score||50)+si.score))})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"分数范围: 0-100分（普通用户），100分以上（超级用户）"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[a.jsx("button",{onClick:()=>{sl(!1),sc({score:0,reason:""})},className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"取消"}),a.jsx("button",{onClick:tr,disabled:!si.reason.trim()||0===si.score,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"确认调整"})]})]})}),sV&&e1&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:()=>sL(!1),children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"调整每日发帖限制"}),a.jsx("button",{onClick:()=>sL(!1),className:"text-gray-400 hover:text-gray-600",children:a.jsx(S.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户信息"}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e1.nickname}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",e1.user_id]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日发帖限制"}),a.jsx("input",{type:"number",min:"1",max:"100",value:sA,onChange:e=>sM(parseInt(e.target.value)||1),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"输入每日发帖限制"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["当前限制：",se?.daily_post_limit||5,"条/天"]})]}),a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[a.jsx(M.Z,{className:"w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[a.jsx("p",{className:"font-medium",children:"注意事项"}),a.jsx("p",{children:"个人设置的发帖限制优先级高于系统设置"})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[a.jsx("button",{onClick:()=>sL(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"取消"}),a.jsx("button",{onClick:tc,disabled:sx,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:sx?"调整中...":"确认调整"})]})]})}),so&&e1&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:()=>sh(!1),children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"设置VIP用户"}),a.jsx("button",{onClick:()=>sh(!1),className:"text-gray-400 hover:text-gray-600",children:a.jsx(S.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"VIP有效期（天）"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>su(7),className:`px-3 py-2 rounded-md text-sm ${7===sp?"bg-purple-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"7天"}),a.jsx("button",{onClick:()=>su(30),className:`px-3 py-2 rounded-md text-sm ${30===sp?"bg-purple-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"30天"}),a.jsx("button",{onClick:()=>su(90),className:`px-3 py-2 rounded-md text-sm ${90===sp?"bg-purple-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"90天"}),a.jsx("input",{type:"number",value:sp,onChange:e=>su(parseInt(e.target.value)||30),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"自定义天数",min:"1",max:"3650"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"VIP权益设置"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-xs text-gray-600 mb-1",children:"每日发帖限制"}),a.jsx("input",{type:"number",value:sg.daily_post_limit,onChange:e=>sj(s=>({...s,daily_post_limit:parseInt(e.target.value)||50})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",min:"1",max:"1000"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-xs text-gray-600 mb-1",children:"信用分上限"}),a.jsx("input",{type:"number",value:sg.credit_score_limit,onChange:e=>sj(s=>({...s,credit_score_limit:parseInt(e.target.value)||200})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",min:"100",max:"1000"})]})]})]}),a.jsx("div",{className:"bg-purple-50 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"text-sm text-purple-800",children:[(0,a.jsxs)("div",{children:["用户: ",a.jsx("span",{className:"font-medium",children:e1.nickname})]}),(0,a.jsxs)("div",{children:["VIP有效期: ",(0,a.jsxs)("span",{className:"font-medium",children:[sp,"天"]})]}),(0,a.jsxs)("div",{children:["到期时间: ",a.jsx("span",{className:"font-medium",children:new Date(Date.now()+864e5*sp).toLocaleDateString("zh-CN")})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[a.jsx("button",{onClick:()=>sh(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"取消"}),a.jsx("button",{onClick:ti,disabled:sx,className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:sx?"设置中...":"确认设置"})]})]})}),sC&&sk&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[a.jsx(u,{className:"w-5 h-5 text-purple-600 mr-2"}),"VIP用户管理 - ",sk.user_info?.nickname||"未知用户"]}),a.jsx("button",{onClick:()=>sI(!1),className:"text-gray-400 hover:text-gray-600",children:a.jsx(S.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"用户信息"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[a.jsx("img",{src:sk.user_info?.avatar||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:sk.user_info?.nickname||"未知用户",className:"w-16 h-16 rounded-full"}),(0,a.jsxs)("div",{children:[a.jsx("h5",{className:"font-medium text-gray-900",children:sk.user_info?.nickname||"未知用户"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",sk.user_id]})]})]})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"VIP状态"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"当前状态"}),a.jsx("div",{className:"mt-1 flex items-center",children:sk.is_vip?(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800",children:[a.jsx(u,{className:"w-4 h-4 mr-1"}),"有效VIP"]}):a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800",children:"已过期"})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"剩余天数"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[sk.remaining_days," 天"]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"开始时间"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900",children:new Date(sk.vip_start_time).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"到期时间"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900",children:new Date(sk.vip_end_time).toLocaleDateString("zh-CN")})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"VIP权益"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"每日发帖限制"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[sk.vip_benefits?.daily_post_limit||5," 条"]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"信用分上限"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[sk.vip_benefits?.credit_score_limit||100," 分"]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"专属VIP标识"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900",children:sk.vip_benefits?.special_badge?"✓ 已开启":"✗ 未开启"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"优先客服支持"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900",children:sk.vip_benefits?.priority_support?"✓ 已开启":"✗ 未开启"})]})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"管理操作"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[a.jsx("button",{onClick:()=>tx(sk.user_id,7),disabled:sx,className:"px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors",children:"延长7天"}),a.jsx("button",{onClick:()=>tx(sk.user_id,30),disabled:sx,className:"px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors",children:"延长30天"}),a.jsx("button",{onClick:()=>tx(sk.user_id,90),disabled:sx,className:"px-3 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors",children:"延长90天"}),a.jsx("button",{onClick:async()=>{try{sm(!0);let e=await i.petAPI.removeVipUser({user_id:sk.user_id});e.success?(c.C.success("移除VIP成功"),await td(),sI(!1)):c.C.error(e.message||"移除VIP失败")}catch(e){console.error("移除VIP失败:",e),c.C.error("移除VIP失败")}finally{sm(!1)}},disabled:sx,className:"px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 transition-colors",children:"移除VIP"})]})]})]}),a.jsx("div",{className:"flex justify-end space-x-3 mt-6",children:a.jsx("button",{onClick:()=>sI(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"关闭"})})]})})]})}},5264:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(10326);t(17577);var r=t(20603);function l({children:e}){return(0,a.jsxs)("div",{className:"admin-layout",children:[a.jsx(r.ToastProvider,{}),e]})}t(23824)},99837:(e,s,t)=>{"use strict";t.d(s,{Z:()=>d});var a=t(10326),r=t(17577),l=t.n(r),i=t(28295);let c=l().forwardRef(({className:e,variant:s="primary",size:t="md",loading:r=!1,icon:l,children:c,disabled:d,...n},x)=>(0,a.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[t],e),ref:x,disabled:d||r,...n,children:[r&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!r&&l&&a.jsx("span",{className:"mr-2",children:l}),c]}));c.displayName="Button";let d=c},13624:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\dashboard\page.tsx#default`)},9457:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\admin\layout.tsx#default`)},23824:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,946,240],()=>t(32868));module.exports=a})();