"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[734],{9356:function(e,t,r){r.d(t,{C:function(){return g},ToastProvider:function(){return f}});var a=r(57437);r(2265);var o=r(69064),s=r(65302),n=r(45131),l=r(22252),c=r(33245),i=r(68661);let u={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},d=e=>{let{message:t,type:r,onDismiss:o}=e,u={success:(0,a.jsx)(s.Z,{className:"h-5 w-5 text-green-500"}),error:(0,a.jsx)(n.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,a.jsx)(l.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,a.jsx)(c.Z,{className:"h-5 w-5 text-blue-500"})};return(0,a.jsxs)("div",{className:(0,i.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[r]),children:[u[r],(0,a.jsx)("span",{className:"flex-1 text-sm font-medium",children:t}),(0,a.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})},g={success:e=>{o.Am.custom(t=>(0,a.jsx)(d,{message:e,type:"success",onDismiss:()=>o.Am.dismiss(t.id)}),u)},error:e=>{o.Am.custom(t=>(0,a.jsx)(d,{message:e,type:"error",onDismiss:()=>o.Am.dismiss(t.id)}),{...u,duration:6e3})},warning:e=>{o.Am.custom(t=>(0,a.jsx)(d,{message:e,type:"warning",onDismiss:()=>o.Am.dismiss(t.id)}),u)},info:e=>{o.Am.custom(t=>(0,a.jsx)(d,{message:e,type:"info",onDismiss:()=>o.Am.dismiss(t.id)}),u)},loading:e=>o.Am.loading(e,{style:u.style,position:u.position}),dismiss:e=>{o.Am.dismiss(e)},promise:(e,t)=>o.Am.promise(e,t,{style:u.style,position:u.position})},f=()=>(0,a.jsx)(o.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},98734:function(e,t,r){r.d(t,{a:function(){return l}});var a=r(2265),o=r(98011),s=r(9356),n=r(83565);let l=()=>{let[e,t]=(0,a.useState)(null),[r,l]=(0,a.useState)(!0),[c,i]=(0,a.useState)(!1),u=(0,a.useCallback)(e=>{try{e?(localStorage.setItem("pet_platform_user",JSON.stringify(e)),localStorage.setItem("pet_platform_logged_in","true")):(localStorage.removeItem("pet_platform_user"),localStorage.removeItem("pet_platform_logged_in"))}catch(e){console.error("保存用户状态失败:",e)}},[]),d=(0,a.useCallback)(()=>{try{let e=localStorage.getItem("pet_platform_user"),r=localStorage.getItem("pet_platform_logged_in");if(e&&"true"===r){let r=JSON.parse(e);return t(r),i(!0),r}}catch(e){console.error("恢复用户状态失败:",e)}return null},[]),g=(0,a.useCallback)(async()=>{try{l(!0);let e=d();if(e)try{if(e.email){let r=await o.authAPI.getCurrentUser(e.email);r.success&&r.data?(t(r.data),i(!0),u(r.data)):(t(null),i(!1),u(null))}else console.log("使用本地保存的用户信息（匿名用户）"),t(e),i(!0)}catch(r){console.error("验证用户状态失败:",r),e?(console.log("验证失败，使用本地用户信息"),t(e),i(!0)):(t(null),i(!1),u(null))}else{console.log("没有保存的用户信息，尝试匿名登录");try{let e=await o.authAPI.getCurrentUser();e.success&&e.data?(t(e.data),i(!0),u(e.data)):(t(null),i(!1))}catch(e){console.error("匿名登录失败:",e),t(null),i(!1)}}}catch(e){console.error("检查登录状态失败:",e),t(null),i(!1)}finally{l(!1)}},[d,u]),f=(0,a.useCallback)(async(e,r)=>{try{if(l(!0),!e||!r)return s.C.error("请输入邮箱和密码"),!1;let a=await o.authAPI.loginWithEmail(e,r);if(!a.success)return s.C.error(a.message||"登录失败"),!1;t(a.data),i(!0),u(a.data);try{await (0,n.tD)(a.data._id)}catch(e){console.error("同步草稿失败:",e)}return a.data.isNewUser?s.C.success("欢迎加入宠物交易平台！"):s.C.success("登录成功！"),!0}catch(e){return console.error("登录失败:",e),s.C.error(e.message||"登录失败，请重试"),!1}finally{l(!1)}},[u]),m=(0,a.useCallback)(async()=>{try{return l(!0),await o.authAPI.logout(),t(null),i(!1),u(null),s.C.success("已退出登录"),!0}catch(e){return console.error("退出登录失败:",e),s.C.error("退出登录失败"),!1}finally{l(!1)}},[u]),p=(0,a.useCallback)(async r=>{if(!e)return!1;try{l(!0),console.log("更新用户资料:",r);let a={...e,...r};return t(a),u(a),s.C.success("资料更新成功"),!0}catch(e){return console.error("更新资料失败:",e),s.C.error("更新失败"),!1}finally{l(!1)}},[e,u]),h=(0,a.useCallback)(async()=>{if(c)try{let e=await o.authAPI.getCurrentUser();e.success&&e.data&&(t(e.data),u(e.data))}catch(e){console.error("刷新用户信息失败:",e)}},[c,u]),y=(0,a.useCallback)(async()=>{try{let e=d(),r=await o.authAPI.getCurrentUser(null==e?void 0:e.email);r.success&&r.data?(t(r.data),i(!0),u(r.data)):(t(null),i(!1),u(null))}catch(e){console.error("刷新登录状态失败:",e),t(null),i(!1),u(null)}},[u,d]);return(0,a.useEffect)(()=>{(async()=>{let e=d();if(e){t(e),i(!0),l(!1);try{console.log("后台验证用户状态...");let r=await o.authAPI.getCurrentUser(e.email);r.success&&r.data?(t(r.data),u(r.data),console.log("用户状态验证成功")):console.warn("云端验证失败，但保持本地状态")}catch(e){console.error("后台验证失败:",e)}}else await g()})()},[g,d,u]),(0,a.useEffect)(()=>{},[]),{user:e,isLoading:r,isLoggedIn:c,login:f,logout:m,updateProfile:p,refreshUser:h,refreshLoginState:y}}},83565:function(e,t,r){r.d(t,{DO:function(){return l},Oe:function(){return s},XD:function(){return c},deleteDraft:function(){return i},eJ:function(){return u},tD:function(){return d}});var a=r(98011);let o="petDrafts",s=()=>{try{let e=localStorage.getItem(o);return e?JSON.parse(e):[]}catch(e){return console.error("获取草稿失败:",e),[]}},n=async e=>{let t=[];for(let r of e)if(r instanceof File)try{let e=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(r)});t.push(e)}catch(e){console.error("转换图片失败:",e),t.push("https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")}else"string"==typeof r&&t.push(r);return t},l=async e=>{try{let t=await n(e.images),r=s(),a={id:Date.now().toString(),...e,images:t,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},l=[a,...r];return localStorage.setItem(o,JSON.stringify(l)),a.id}catch(e){throw console.error("保存草稿失败:",e),Error("保存草稿失败")}},c=async(e,t)=>{try{let r=s(),a=r.findIndex(t=>t.id===e);if(-1!==a){let e=await n(t.images);r[a]={...r[a],...t,images:e,updated_at:new Date().toISOString()},localStorage.setItem(o,JSON.stringify(r))}}catch(e){throw console.error("更新草稿失败:",e),Error("更新草稿失败")}},i=e=>{try{let t=s().filter(t=>t.id!==e);localStorage.setItem(o,JSON.stringify(t))}catch(e){throw console.error("删除草稿失败:",e),Error("删除草稿失败")}},u=e=>{try{let t=s().find(t=>t.id===e)||null;return console.log("获取草稿 ".concat(e,":"),t),t}catch(e){return console.error("获取草稿失败:",e),null}},d=async e=>{try{let t=(await a.petAPI.getUserDrafts({user_id:e})).data||[];if(0===t.length)return;let r=s(),n=t.map(e=>({id:e._id,title:e.title,content:e.content,images:e.images||[],category:e.category,type:e.type,location:e.location,breed:e.breed,age:e.age,gender:e.gender,price:e.price,contact_info:e.contact_info,tags:e.tags||[],created_at:e.created_at,updated_at:new Date().toISOString(),isArchived:"auto_archived"===e.draft_type,originalPostId:e.original_post_id})),l=new Set(r.map(e=>e.id)),c=n.filter(e=>!l.has(e.id));if(c.length>0){let e=[...r,...c];for(let t of(localStorage.setItem(o,JSON.stringify(e)),c))try{await a.petAPI.deleteUserDraft({draft_id:t.id})}catch(e){console.error("删除云端草稿失败:",e)}}}catch(e){console.error("同步云端草稿失败:",e)}}},68661:function(e,t,r){r.d(t,{cn:function(){return s},uf:function(){return n},vV:function(){return l}});var a=r(61994),o=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.m6)((0,a.W)(t))}function n(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function l(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}}}]);