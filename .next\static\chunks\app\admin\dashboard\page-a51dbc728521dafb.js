(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[427],{30804:function(e,s,t){Promise.resolve().then(t.bind(t,66095))},42488:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},63639:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},32660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},91723:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50091:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},42208:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},46211:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},94630:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},99397:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},73247:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},98728:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88906:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},18930:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},37806:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},92369:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},32489:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},99376:function(e,s,t){"use strict";var a=t(35475);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},66095:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return M}});var a=t(57437),r=t(2265),l=t(99376),i=t(98011),c=t(9356),n=t(56334),d=t(32660),x=t(88906),o=t(39763);let m=(0,o.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),h=(0,o.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),u=(0,o.Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),p=(0,o.Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]);var g=t(37806);let y=(0,o.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);var j=t(98728),b=t(99397),N=t(46211),v=t(42488);let f=(0,o.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var w=t(18930),_=t(65302),k=t(45131),C=t(73247),I=t(92369);let P=(0,o.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var Z=t(32489),S=t(42208),D=t(50091),V=t(94630),A=t(91723);let L=(0,o.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var T=t(63639);let U=(0,o.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function M(){var e,s,t,o,M,z,R,E;let F=(0,l.useRouter)(),[B,O]=(0,r.useState)("dashboard"),[H,q]=(0,r.useState)(null),[W,G]=(0,r.useState)(null),[J,K]=(0,r.useState)([]),[X,$]=(0,r.useState)([]),[Q,Y]=(0,r.useState)(!0),[ee,es]=(0,r.useState)(null),[et,ea]=(0,r.useState)(null),[er,el]=(0,r.useState)(""),[ei,ec]=(0,r.useState)(!1),[en,ed]=(0,r.useState)("approved"),[ex,eo]=(0,r.useState)(!1),[em,eh]=(0,r.useState)({username:"",password:"",role:"admin",level:1,permissions:[]}),[eu,ep]=(0,r.useState)([]),[eg,ey]=(0,r.useState)(!1),[ej,eb]=(0,r.useState)(!1),[eN,ev]=(0,r.useState)(null),[ef,ew]=(0,r.useState)(""),[e_,ek]=(0,r.useState)(""),[eC,eI]=(0,r.useState)(""),[eP,eZ]=(0,r.useState)([]),[eS,eD]=(0,r.useState)([]),[eV,eA]=(0,r.useState)([]),[eL,eT]=(0,r.useState)(!1),[eU,eM]=(0,r.useState)("ads"),[ez,eR]=(0,r.useState)(!1),[eE,eF]=(0,r.useState)([]),[eB,eO]=(0,r.useState)(!1),[eH,eq]=(0,r.useState)(null),[eW,eG]=(0,r.useState)(!1),[eJ,eK]=(0,r.useState)(!1),[eX,e$]=(0,r.useState)("all"),[eQ,eY]=(0,r.useState)("all"),[e0,e2]=(0,r.useState)({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10,normalUserPostLimit:100,superUserPostLimit:500,autoArchiveEnabled:!0,normalUserDailyPostLimit:5,vipUserDailyPostLimit:20}),[e1,e4]=(0,r.useState)(!1),[e6,e3]=(0,r.useState)({post_report_threshold:5,user_report_threshold:10,auto_hide_posts:!0,auto_warn_users:!0,notification_enabled:!0}),[e5,e7]=(0,r.useState)(!1),[e9,e8]=(0,r.useState)(""),[se,ss]=(0,r.useState)(null),[st,sa]=(0,r.useState)(null),[sr,sl]=(0,r.useState)(!1),[si,sc]=(0,r.useState)(!1),[sn,sd]=(0,r.useState)(null),[sx,so]=(0,r.useState)(!1),[sm,sh]=(0,r.useState)(!1),[su,sp]=(0,r.useState)({score:0,reason:""}),[sg,sy]=(0,r.useState)(null),[sj,sb]=(0,r.useState)(!1),[sN,sv]=(0,r.useState)(!1),[sf,sw]=(0,r.useState)(30),[s_,sk]=(0,r.useState)({daily_post_limit:50,credit_score_limit:200,special_badge:!0,priority_support:!0}),[sC,sI]=(0,r.useState)([]),[sP,sZ]=(0,r.useState)(!1),[sS,sD]=(0,r.useState)("all"),[sV,sA]=(0,r.useState)(null),[sL,sT]=(0,r.useState)(!1),[sU,sM]=(0,r.useState)(!1),[sz,sR]=(0,r.useState)(null),[sE,sF]=(0,r.useState)(!1),[sB,sO]=(0,r.useState)(5),[sH,sq]=(0,r.useState)(!1),[sW,sG]=(0,r.useState)(null),[sJ,sK]=(0,r.useState)(!1),sX=e=>{sG(e),sq(!0)};(0,r.useEffect)(()=>{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");if(!e||!s){F.push("/admin");return}try{let e=JSON.parse(s);q(e),s$()}catch(e){console.error("解析管理员信息失败:",e),F.push("/admin")}},[F]);let s$=async()=>{try{Y(!0);try{var e,s;let t=await (0,i.initCloudBase)();if(!t)throw Error("CloudBase未初始化");let a=await t.callFunction({name:"pet-api",data:{action:"getDashboardStats",data:{}}});(null===(e=a.result)||void 0===e?void 0:e.success)?G(a.result.data):(console.error("获取统计数据失败:",null===(s=a.result)||void 0===s?void 0:s.message),G({totalUsers:0,totalPosts:0,totalReports:0,totalAppeals:0,yesterdayNewUsers:0,yesterdayNewPosts:0,activeUsers:0,onlineUsers:0}))}catch(e){console.error("获取统计数据异常:",e),G({totalUsers:0,totalPosts:0,totalReports:0,totalAppeals:0,yesterdayNewUsers:0,yesterdayNewPosts:0,activeUsers:0,onlineUsers:0})}try{await sQ()}catch(e){console.log("无权限加载管理员列表或不是超级管理员")}await s2(),await s0(),await tt(),await ti(),await tc()}catch(e){console.error("加载仪表板数据失败:",e),c.C.error("加载数据失败")}finally{Y(!1)}},sQ=async()=>{try{let e=await i.petAPI.getAdmins({limit:50,offset:0});e.success&&K(e.data||[])}catch(e){console.error("加载管理员列表失败:",e)}},sY=async(e,s)=>{ey(!0);try{let t={limit:50};e&&(t.userId=e),s&&(t.breed=s);let a=await i.petAPI.getPostsForAdmin(t);a.success&&ep(a.data)}catch(e){console.error("加载帖子列表失败:",e),c.C.error("加载帖子列表失败")}finally{ey(!1)}},s0=async()=>{try{let e=await i.petAPI.getCategories();e.success&&eZ(e.data)}catch(e){console.error("加载分类列表失败:",e)}},s2=async()=>{try{let e=await i.petAPI.getAppeals({status:"all",limit:50,offset:0});e.success&&$(e.data||[])}catch(e){console.error("加载申诉列表失败:",e)}},s1=async()=>{if(!em.username||!em.password){c.C.error("请填写用户名和密码");return}try{let e=await i.petAPI.createAdmin({...em,permissions:["*"]});e.success?(c.C.success("管理员创建成功"),eo(!1),eh({username:"",password:"",role:"admin",level:1,permissions:[]}),sQ()):c.C.error(e.message||"创建失败")}catch(e){console.error("创建管理员失败:",e),c.C.error(e.message||"创建管理员失败")}},s4=async e=>{sX({title:"删除管理员",message:"确定要删除这个管理员吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:async()=>{try{let s=await i.petAPI.deleteAdmin({adminId:e});s.success?(c.C.success("管理员删除成功"),sQ()):c.C.error(s.message||"删除失败")}catch(e){console.error("删除管理员失败:",e),c.C.error(e.message||"删除管理员失败")}}})},s6=async()=>{if(et)try{es(et._id);let e=await i.petAPI.handleAppeal({appealId:et._id,action:en,adminReason:er});e.success?(c.C.success("申诉处理成功"),ec(!1),ea(null),el(""),s2()):c.C.error(e.message||"处理失败")}catch(e){console.error("处理申诉失败:",e),c.C.error(e.message||"处理申诉失败")}finally{es(null)}},s3=e=>new Date(e).toLocaleString("zh-CN"),s5=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},s7=e=>{switch(e){case"pending":return"待处理";case"approved":return"已通过";case"rejected":return"已驳回";default:return"未知"}},s9=async()=>{if(!eN||!ef.trim()){c.C.error("请填写删除原因");return}try{let e=await i.petAPI.adminDeletePost({postId:eN._id,reason:ef});e.success?(c.C.success("帖子删除成功"),eb(!1),ev(null),ew(""),e_.trim()&&sY(e_.trim(),eC)):c.C.error(e.message||"删除失败")}catch(e){console.error("删除帖子失败:",e),c.C.error(e.message||"删除帖子失败")}},s8=async(e,s)=>{try{await i.petAPI.togglePostPin({post_id:e,is_pinned:s}),ep(t=>t.map(t=>t._id===e?{...t,is_pinned:s}:t)),c.C.success(s?"帖子已置顶":"已取消置顶")}catch(e){console.error("置顶操作失败:",e),c.C.error("操作失败")}},te=async(e,s)=>{try{let t=await i.petAPI.adjustPostExposure({post_id:e,adjustment:s});t.success?(ep(t=>t.map(t=>t._id===e?{...t,exposure_score:(t.exposure_score||50)+s}:t)),c.C.success("曝光度".concat(s>0?"增加":"减少").concat(Math.abs(s),"分"))):c.C.error(t.message||"调整失败")}catch(e){console.error("调整曝光度失败:",e),c.C.error("调整失败")}},ts=async e=>{try{let s=await i.petAPI.takeDownPost({post_id:e});s.success?(ep(s=>s.map(s=>s._id===e?{...s,status:"taken_down",exposure_score:0}:s)),c.C.success("帖子已下架")):c.C.error(s.message||"下架失败")}catch(e){console.error("下架帖子失败:",e),c.C.error("下架失败")}},tt=async()=>{eT(!0);try{eD([{_id:"ad_001",title:"优质宠物用品推荐",advertiser_id:"advertiser_001",advertiser_name:"宠物之家商城",position_id:"home_banner",position_name:"首页横幅广告",ad_type:"banner",content:"为您的爱宠提供最好的生活用品，健康快乐每一天！全场8折优惠中。",target_url:"https://example.com/pet-products",start_date:"2025-01-01T00:00:00.000Z",end_date:"2025-03-31T23:59:59.000Z",status:"active",priority:1,budget:5e3,spent:1250.5,impressions:15420,clicks:342,ctr:.0222,created_at:"2025-01-01T00:00:00.000Z"},{_id:"ad_002",title:"专业宠物医院",advertiser_id:"advertiser_002",advertiser_name:"爱宠医疗中心",position_id:"home_feed",position_name:"首页信息流广告",ad_type:"feed",content:"24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。",target_url:"https://example.com/pet-hospital",start_date:"2025-01-10T00:00:00.000Z",end_date:"2025-02-28T23:59:59.000Z",status:"active",priority:2,budget:3e3,spent:890.25,impressions:8750,clicks:156,ctr:.0178,created_at:"2025-01-10T00:00:00.000Z"},{_id:"ad_003",title:"宠物美容服务",advertiser_id:"advertiser_003",advertiser_name:"美宠工坊",position_id:"home_feed",position_name:"首页信息流广告",ad_type:"feed",content:"专业宠物美容，让您的爱宠更加美丽动人。新客户首次服务7折。",target_url:"https://example.com/pet-grooming",start_date:"2025-01-15T00:00:00.000Z",end_date:"2025-04-15T23:59:59.000Z",status:"paused",priority:3,budget:2e3,spent:450.75,impressions:4200,clicks:89,ctr:.0212,created_at:"2025-01-15T00:00:00.000Z"}]),eA([{position_id:"home_banner",name:"首页横幅广告",page:"home",location:"top",width:728,height:90,ad_type:"banner",max_ads:3,rotation_interval:5e3,status:"active"},{position_id:"home_feed",name:"首页信息流广告",page:"home",location:"feed",width:300,height:200,ad_type:"feed",max_ads:5,rotation_interval:0,status:"active"},{position_id:"sidebar_banner",name:"侧边栏广告",page:"all",location:"sidebar",width:300,height:250,ad_type:"banner",max_ads:2,rotation_interval:8e3,status:"inactive"}])}catch(e){console.error("加载广告数据失败:",e),c.C.error("加载广告数据失败")}finally{eT(!1)}},ta=e=>{let s={active:{label:"投放中",color:"bg-green-100 text-green-800"},paused:{label:"已暂停",color:"bg-yellow-100 text-yellow-800"},expired:{label:"已过期",color:"bg-red-100 text-red-800"},pending:{label:"待审核",color:"bg-blue-100 text-blue-800"},inactive:{label:"未启用",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.pending;return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.color),children:t.label})},tr=e=>"\xa5".concat(e.toFixed(2)),tl=e=>"".concat((100*e).toFixed(2),"%"),ti=async()=>{eO(!0);try{eF([{_id:"activity_001",title:"最萌宠物评选大赛",description:"展示你的宠物，参与最萌宠物评选活动，赢取丰厚奖品！",type:"CONTEST",status:"ACTIVE",start_time:"2025-01-01T00:00:00.000Z",end_time:"2025-01-31T23:59:59.000Z",result_display_end_time:"2025-02-03T23:59:59.000Z",duration_days:31,result_display_days:3,config:{comments_enabled:!0},statistics_summary:{total_votes:1250,total_comments:340},created_at:"2024-12-25T00:00:00.000Z"},{_id:"activity_002",title:"猫咪 VS 狗狗投票",description:"你更喜欢猫咪还是狗狗？快来投票表达你的观点！",type:"VOTING",status:"ACTIVE",start_time:"2025-01-15T00:00:00.000Z",end_time:"2025-02-15T23:59:59.000Z",result_display_end_time:"2025-02-18T23:59:59.000Z",duration_days:31,result_display_days:3,config:{comments_enabled:!0},statistics_summary:{total_votes:890,total_comments:156},created_at:"2025-01-10T00:00:00.000Z"},{_id:"activity_003",title:"宠物护理经验分享",description:"分享你的宠物护理经验，帮助更多宠物主人",type:"DISCUSSION",status:"ENDED",start_time:"2024-12-01T00:00:00.000Z",end_time:"2024-12-31T23:59:59.000Z",result_display_end_time:"2025-01-03T23:59:59.000Z",duration_days:31,result_display_days:3,config:{comments_enabled:!0},statistics_summary:{total_votes:0,total_comments:245},created_at:"2024-11-25T00:00:00.000Z"}]),eq({enabled:!0,comments_enabled:!0,rate_limit_interval:10,max_comment_length:100,default_result_display_days:3})}catch(e){console.error("加载活动数据失败:",e),c.C.error("加载活动数据失败")}finally{eO(!1)}},tc=async()=>{e4(!0),e7(!0);try{let e=await i.petAPI.getSystemSettings();e.success&&e2(e.data);let s=await i.petAPI.getReportThresholds();s.success&&e3(s.data)}catch(e){console.error("加载设置失败:",e),c.C.error("加载设置失败")}finally{e4(!1),e7(!1)}},tn=e=>{switch(e){case"CONTEST":default:return"\uD83C\uDFC6";case"VOTING":return"\uD83D\uDDF3️";case"DISCUSSION":return"\uD83D\uDCAC"}},td=e=>{switch(e){case"CONTEST":return"评选竞赛";case"VOTING":return"投票话题";case"DISCUSSION":return"讨论活动";default:return"未知类型"}},tx=e=>{let s={DRAFT:{label:"草稿",color:"bg-yellow-100 text-yellow-800"},ACTIVE:{label:"进行中",color:"bg-green-100 text-green-800"},ENDED:{label:"已结束",color:"bg-blue-100 text-blue-800"},ARCHIVED:{label:"已归档",color:"bg-gray-100 text-gray-800"}},t=s[e]||s.DRAFT;return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.color),children:t.label})},to=e=>new Date(e).toLocaleDateString("zh-CN"),tm=async()=>{try{if(sK(!0),e7(!0),e0.maxImageSize<=0||e0.maxImageSize>100){c.C.error("图片大小限制必须在1-100MB之间");return}if(e0.maxImagesPerPost<=0||e0.maxImagesPerPost>20){c.C.error("每帖图片数量必须在1-20张之间");return}if(e0.normalUserPostLimit<=0||e0.normalUserPostLimit>1e3){c.C.error("普通用户帖子上限必须在1-1000之间");return}if(e0.superUserPostLimit<=0||e0.superUserPostLimit>5e3){c.C.error("超级用户帖子上限必须在1-5000之间");return}if(e0.normalUserDailyPostLimit<=0||e0.normalUserDailyPostLimit>50){c.C.error("普通用户每日发帖上限必须在1-50之间");return}if(e0.vipUserDailyPostLimit<=0||e0.vipUserDailyPostLimit>100){c.C.error("VIP用户每日发帖上限必须在1-100之间");return}if(e6.post_report_threshold<=0||e6.post_report_threshold>100){c.C.error("帖子举报阈值必须在1-100之间");return}if(e6.user_report_threshold<=0||e6.user_report_threshold>100){c.C.error("用户举报阈值必须在1-100之间");return}await i.petAPI.updateSystemSettings(e0);let e=await i.petAPI.updateReportThresholds(e6);if(!e.success)throw Error(e.message||"保存举报阈值失败");c.C.success("所有设置保存成功")}catch(e){console.error("保存设置失败:",e),c.C.error("保存设置失败")}finally{sK(!1),e7(!1)}},th=async()=>{if(!e9.trim()){c.C.error("请输入用户ID");return}try{sl(!0),ss(null),sa(null),sR(null),console.log("开始搜索用户:",e9.trim());let e=await i.petAPI.getUserInfo({userId:e9.trim()});if(console.log("用户搜索结果:",e),!e.success){let e="该用户不存在，请检查用户ID是否正确";console.log("用户不存在，显示错误信息"),c.C.error(e),sR(e),ss(null),sa(null);return}if(!e.data||!e.data.userInfo||!e.data.userInfo._id){let e="该用户不存在，请检查用户ID是否正确";console.log("用户数据无效，显示错误信息"),c.C.error(e),sR(e),ss(null),sa(null);return}console.log("用户存在，设置用户数据");let s={id:e.data.userInfo._id,user_id:e.data.userInfo.user_id,nickname:e.data.userInfo.nickname,email:e.data.userInfo.email,avatar:e.data.userInfo.avatar,created_at:e.data.userInfo.created_at,creditInfo:e.data.creditInfo,vipInfo:e.data.vipInfo};ss(s),sR(null);let t=[tu(e9.trim()).catch(e=>{console.error("加载用户权限失败:",e),c.C.warning("权限加载失败")}),tg(e9.trim()).catch(e=>{console.error("加载用户信用分失败:",e),c.C.warning("信用分加载失败")}),tj(e9.trim()).catch(e=>{console.error("加载VIP用户信息失败:",e),c.C.warning("VIP信息加载失败")})];await Promise.allSettled(t),console.log("所有用户数据加载完成"),c.C.success("用户信息加载成功")}catch(s){console.error("搜索用户失败:",s);let e="搜索失败，请检查网络连接或稍后重试";c.C.error(e),sR(e),ss(null),sa(null)}finally{sl(!1)}},tu=async e=>{try{sc(!0),console.log("开始加载用户权限:",e);let s=await i.petAPI.getTargetUserPermissions({targetUserId:e});(console.log("权限API调用结果:",s),s.success)?(console.log("权限加载成功:",s.data),sa(s.data)):(console.log("权限API调用失败，使用默认权限"),sa({canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!0,canPublishPost:!0,bannedUntil:null,banReason:null}),c.C.warning("无法获取用户权限，已设置为默认权限"))}catch(e){console.error("加载用户权限失败:",e),sa({canLike:!0,canDislike:!0,canReportPost:!0,canReportUser:!0,canContact:!0,canPublishPost:!0,bannedUntil:null,banReason:null}),c.C.warning("加载权限失败，已设置为默认权限")}finally{sc(!1)}},tp=async e=>{if(!se){c.C.error("请先搜索用户");return}try{sM(!0);let s=await i.petAPI.updateUserPermissions({targetUserId:se.id,permissions:e});s.success?(sa(e),c.C.success("用户权限更新成功")):c.C.error(s.message||"更新权限失败")}catch(e){console.error("更新用户权限失败:",e),c.C.error("更新权限失败")}finally{sM(!1)}},tg=async e=>{try{console.log("开始加载用户信用分:",e),so(!0);let s=await i.petAPI.getUserCreditScore({userId:e});if(console.log("信用分API调用结果:",s),s.success)console.log("信用分加载成功:",s.data),sd(s.data),sO(s.data.daily_post_limit||5);else{console.log("用户没有信用分记录，创建默认记录");let s={user_id:e,credit_score:50,is_super_user:!1,daily_post_limit:5,last_updated:new Date().toISOString()};sd(s),sO(5)}}catch(s){console.error("加载用户信用分失败:",s),sd({user_id:e,credit_score:50,is_super_user:!1,daily_post_limit:5,last_updated:new Date().toISOString()}),sO(5)}finally{so(!1)}},ty=async()=>{if(!se){c.C.error("请先搜索用户");return}if(0===su.score){c.C.error("请输入调整分数");return}try{let e=await i.petAPI.updateUserCreditScore({user_id:se._id,score_change:su.score,reason:su.reason||"管理员调整"});e.success?(await tg(se._id),sh(!1),sp({score:0,reason:""}),c.C.success("信用分调整成功")):c.C.error(e.message||"调整失败")}catch(e){console.error("调整信用分失败:",e),c.C.error("调整失败")}},tj=async e=>{try{console.log("开始加载VIP用户信息:",e),sb(!0);let s=await i.petAPI.getVipUserInfo({user_id:e});console.log("VIP用户信息API调用结果:",s),s.success?(console.log("VIP用户信息加载成功:",s.data),sy(s.data)):(console.log("用户不是VIP用户"),sy({user_id:e,is_vip:!1,vip_start_time:null,vip_end_time:null,vip_benefits:{daily_post_limit:5,credit_score_limit:100}}))}catch(e){console.error("加载VIP用户信息失败:",e),sy(null)}finally{sb(!1)}},tb=async()=>{if(se)try{sb(!0);let e=await i.petAPI.setVipUser({user_id:se._id,duration_days:sf,benefits:s_});e.success?(c.C.success("设置VIP用户成功"),sv(!1),await tj(se._id),await tg(se._id)):c.C.error(e.message||"设置VIP用户失败")}catch(e){console.error("设置VIP用户失败:",e),c.C.error("设置VIP用户失败")}finally{sb(!1)}},tN=async()=>{if(se)try{sb(!0);let e=await i.petAPI.updateUserDailyPostLimit({user_id:se._id,daily_post_limit:sB});e.success?(c.C.success("每日发帖限制调整成功"),sF(!1),await tg(se._id)):c.C.error(e.message||"调整每日发帖限制失败")}catch(e){console.error("调整每日发帖限制失败:",e),c.C.error("调整每日发帖限制失败")}finally{sb(!1)}},tv=async()=>{try{sZ(!0);let e=await i.petAPI.getVipUserList({limit:50,offset:0,status:sS});e.success?sI(e.data):(c.C.error(e.message||"加载VIP用户列表失败"),sI([]))}catch(e){console.error("加载VIP用户列表失败:",e),c.C.error("加载VIP用户列表失败"),sI([])}finally{sZ(!1)}},tf=e=>{sA(e),sT(!0)},tw=async(e,s)=>{try{sb(!0);let t=await i.petAPI.updateVipUser({user_id:e,duration_days:s});t.success?(c.C.success("延长VIP时间成功"),await tv(),sT(!1)):c.C.error(t.message||"延长VIP时间失败")}catch(e){console.error("延长VIP时间失败:",e),c.C.error("延长VIP时间失败")}finally{sb(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>F.push("/admin"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(d.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.Z,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"超级管理员控制台"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["欢迎，",(null==H?void 0:H.username)||"管理员"]})]})]})]}),(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),F.push("/admin")},children:"退出登录"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1 overflow-x-auto",children:[(0,a.jsxs)("button",{onClick:()=>O("dashboard"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("dashboard"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(m,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"数据概览"})]}),(0,a.jsxs)("button",{onClick:()=>O("admins"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("admins"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(h,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"管理员管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("appeals"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("appeals"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(u,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"申诉管理"})]}),(0,a.jsxs)("button",{onClick:()=>{O("vip"),0===sC.length&&tv()},className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("vip"===B?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(p,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"VIP管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("users"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("users"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(g.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"用户管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("ads"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("ads"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),(0,a.jsx)("span",{children:"广告管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("posts"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("posts"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"})}),(0,a.jsx)("span",{children:"帖子管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("activities"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("activities"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,a.jsx)("span",{children:"活动管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("content"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("content"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(y,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"内容管理"})]}),(0,a.jsxs)("button",{onClick:()=>O("settings"),className:"flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap ".concat("settings"===B?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(j.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"系统设置"})]})]}),"dashboard"===B&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(h,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总用户数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.totalUsers)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(y,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总宝贝数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.totalPosts)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,a.jsx)(g.Z,{className:"w-6 h-6 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增用户"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.yesterdayNewUsers)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-teal-100 rounded-lg",children:(0,a.jsx)(b.Z,{className:"w-6 h-6 text-teal-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"昨日新增宝贝"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.yesterdayNewPosts)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(N.Z,{className:"w-6 h-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待处理举报"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.totalReports)||0})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(u,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待处理申诉"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.totalAppeals)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-indigo-100 rounded-lg",children:(0,a.jsx)(v.Z,{className:"w-6 h-6 text-indigo-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"活跃用户"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.activeUsers)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-pink-100 rounded-lg",children:(0,a.jsx)(f,{className:"w-6 h-6 text-pink-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"在线用户"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==W?void 0:W.onlineUsers)||0})]})]})})]})]}),"admins"===B&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"管理员列表"}),(null==H?void 0:H.role)==="super_admin"&&(0,a.jsxs)(n.Z,{onClick:()=>eo(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"创建管理员"})]})]}),Q?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===J.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无管理员"}),(0,a.jsx)("p",{className:"text-gray-500",children:"点击上方按钮创建第一个管理员"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"管理员信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色权限"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:J.filter(e=>"superadminTT"!==e.username).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.username}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"管理员账号"})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:"super_admin"===e.role?"超级管理员":"普通管理员"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"super_admin"===e.role?"拥有所有权限":"拥有所有业务权限"})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("active"===e.status?"bg-green-100 text-green-800":"suspended"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"active"===e.status?"正常":"suspended"===e.status?"暂停":"禁用"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("zh-CN")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(null==H?void 0:H.role)==="super_admin"&&!e.is_system_account&&(0,a.jsx)("button",{onClick:()=>s4(e._id),className:"text-red-600 hover:text-red-900",title:"删除管理员",children:(0,a.jsx)(w.Z,{className:"w-4 h-4"})}),!((null==H?void 0:H.role)==="super_admin"&&!e.is_system_account)&&(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"无操作权限"})]})})]},e._id))})]})})]}),"appeals"===B&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"申诉列表"})}),Q?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-4",children:"加载中..."})]}):0===X.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无申诉"}),(0,a.jsx)("p",{className:"text-gray-500",children:"目前没有需要处理的申诉"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:X.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(s5(e.status)),children:s7(e.status)}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"post"===e.type?"帖子申诉":"用户申诉"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:s3(e.created_at)})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-900 mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"申诉理由："}),e.reason]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["举报ID: ",e.report_id," | 申诉人ID: ",e.appellant_id]}),e.admin_reason&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-900",children:[(0,a.jsx)("span",{className:"font-medium",children:"管理员回复："}),e.admin_reason]})})]}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,a.jsxs)(n.Z,{size:"sm",variant:"outline",onClick:()=>{ea(e),ed("approved"),ec(!0)},disabled:ee===e._id,children:[(0,a.jsx)(_.Z,{className:"w-4 h-4 mr-1"}),"通过"]}),(0,a.jsxs)(n.Z,{size:"sm",variant:"outline",onClick:()=>{ea(e),ed("rejected"),ec(!0)},disabled:ee===e._id,children:[(0,a.jsx)(k.Z,{className:"w-4 h-4 mr-1"}),"驳回"]})]})]})},e._id))})]}),"users"===B&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(C.Z,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"用户搜索"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"输入用户ID搜索用户信息和权限设置"})]}),(0,a.jsx)("div",{className:"px-6 py-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户ID"}),(0,a.jsx)("input",{type:"text",placeholder:"请输入用户ID...",value:e9,onChange:e=>{e8(e.target.value),sz&&sR(null)},onKeyPress:e=>"Enter"===e.key&&th(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsx)("div",{className:"pt-6",children:(0,a.jsxs)(n.Z,{onClick:th,loading:sr,disabled:sr||!e9.trim(),className:"flex items-center space-x-2",children:[(0,a.jsx)(C.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:sr?"搜索中...":"搜索用户"})]})})]})})]}),se&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(I.Z,{className:"w-6 h-6 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"用户信息"})]})}),(0,a.jsxs)("div",{className:"px-6 py-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("img",{src:se.avatar_url||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:se.nickname,className:"w-16 h-16 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:se.nickname}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",se.id]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"注册时间"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:new Date(se.created_at).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"最后登录"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:se.last_login?new Date(se.last_login).toLocaleDateString("zh-CN"):"未知"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700",children:"统计信息"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:se.posts_count||0}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"发布帖子"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:se.likes_count||0}),(0,a.jsx)("div",{className:"text-sm text-green-800",children:"获得点赞"})]})]})]})]}),sn&&(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700",children:"信用分管理"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:"rounded-lg p-4 ".concat(sn.credit_score>=80?"bg-green-50":sn.credit_score>=60?"bg-yellow-50":"bg-red-50"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold ".concat(sn.credit_score>=80?"text-green-600":sn.credit_score>=60?"text-yellow-600":"text-red-600"),children:sn.credit_score}),(0,a.jsx)("div",{className:"text-sm ".concat(sn.credit_score>=80?"text-green-800":sn.credit_score>=60?"text-yellow-800":"text-red-800"),children:"当前信用分"})]}),(0,a.jsx)("button",{onClick:()=>sh(!0),className:"px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"调整"})]})}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:sn.daily_post_limit||5}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"每日发帖限制"})]}),(0,a.jsx)("button",{onClick:()=>sF(!0),className:"px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"调整"})]})}),(0,a.jsx)("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-purple-600",children:(null==sg?void 0:sg.is_vip)?"VIP用户":"普通用户"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户状态"})]}),!(null==sg?void 0:sg.is_vip)&&(0,a.jsx)("button",{onClick:()=>sv(!0),disabled:sj,className:"px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors disabled:opacity-50",children:"升级VIP"})]})})]})]})]})]}),se&&st&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.Z,{className:"w-6 h-6 text-purple-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"权限管理"})]}),(0,a.jsxs)(n.Z,{onClick:()=>tp(st),loading:sU,disabled:sU,className:"flex items-center space-x-2",children:[(0,a.jsx)(P,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:sU?"保存中...":"保存权限"})]})]})}),si?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载权限中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700",children:"基础权限"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:st.canLike,onChange:e=>sa(s=>({...s,canLike:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"点赞权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:st.canDislike,onChange:e=>sa(s=>({...s,canDislike:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"不喜欢权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:st.canContact,onChange:e=>sa(s=>({...s,canContact:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"联系权限"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700",children:"高级权限"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:st.canPublishPost,onChange:e=>sa(s=>({...s,canPublishPost:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"发帖权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:st.canReportPost,onChange:e=>sa(s=>({...s,canReportPost:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"举报帖子权限"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:st.canReportUser,onChange:e=>sa(s=>({...s,canReportUser:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"举报用户权限"})]})]})]})]}),(0,a.jsx)("div",{className:"mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(x.Z,{className:"w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-gray-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"权限说明"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"点赞权限：控制用户是否可以给帖子点赞"}),(0,a.jsx)("li",{children:"不喜欢权限：控制用户是否可以给帖子点不喜欢"}),(0,a.jsx)("li",{children:"联系权限：控制用户是否可以查看和使用联系方式"}),(0,a.jsx)("li",{children:"发帖权限：控制用户是否可以发布新帖子"}),(0,a.jsx)("li",{children:"举报权限：控制用户是否可以举报帖子和其他用户"})]})]})]})})]})]}),!se&&!sr&&(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsx)("div",{className:"px-6 py-12 text-center",children:sz?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Z.Z,{className:"w-16 h-16 text-red-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-red-900 mb-2",children:"搜索失败"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:sz}),(0,a.jsx)(n.Z,{onClick:()=>{sR(null),e8("")},variant:"outline",className:"text-gray-600 border-gray-300 hover:bg-gray-50",children:"重新搜索"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(C.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"请搜索用户"}),(0,a.jsx)("p",{className:"text-gray-500",children:"输入用户ID来查看用户信息和管理权限"})]})})})]}),"posts"===B&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"帖子管理"})}),(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"按用户ID搜索"}),(0,a.jsx)("input",{type:"text",value:e_,onChange:e=>ek(e.target.value),placeholder:"输入用户ID",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"按宠物品种筛选"}),(0,a.jsxs)("select",{value:eC,onChange:e=>eI(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"全部品种"}),eP.map(e=>{var s;return(0,a.jsx)("optgroup",{label:e.name,children:null===(s=e.subcategories)||void 0===s?void 0:s.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e._id))},e._id)})]})]}),(0,a.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>{if(!e_.trim()){c.C.error("请输入用户ID进行搜索");return}sY(e_.trim(),eC)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),"搜索用户帖子"]}),(0,a.jsx)("button",{onClick:()=>{ek(""),eI(""),ep([])},className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:"重置"})]})]})}),eg?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):e_.trim()?0===eu.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)(y,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"该用户暂无帖子"}),(0,a.jsxs)("p",{className:"text-gray-500",children:["用户ID: ",e_]})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"帖子信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"作者"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"互动统计"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"举报统计"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"曝光度/评分"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"发布时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:eu.map(e=>{var s,t,r,l;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.images&&e.images[0]&&(0,a.jsx)("img",{src:e.images[0],alt:e.title,className:"h-12 w-12 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("a",{href:"/post/".concat(e._id),target:"_blank",className:"text-sm font-medium text-blue-600 hover:text-blue-800 max-w-xs truncate block",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.category," • \xa5",e.price]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("img",{src:(null===(s=e.author_info)||void 0===s?void 0:s.avatar_url)||"/default-avatar.png",alt:null===(t=e.author_info)||void 0===t?void 0:t.nickname,className:"h-8 w-8 rounded-full mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(null===(r=e.author_info)||void 0===r?void 0:r.nickname)||"未知用户"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",null===(l=e.user_id)||void 0===l?void 0:l.slice(0,8),"..."]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:["\uD83D\uDC4D ",e.likes_count||0]}),(0,a.jsxs)("div",{children:["\uD83D\uDC4E ",e.dislikes_count||0]}),(0,a.jsxs)("div",{children:["\uD83D\uDC96 ",e.wants_count||0]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-red-600",children:["理由1: ",e.report_reason1_count||0]}),(0,a.jsxs)("div",{className:"text-red-600",children:["理由2: ",e.report_reason2_count||0]}),(0,a.jsxs)("div",{className:"text-orange-600",children:["总计: ",e.reports_count||0]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"font-medium ".concat((e.exposure_score||50)>40?"text-green-600":"text-red-600"),children:["曝光度: ",e.exposure_score||50,"分"]}),(0,a.jsxs)("div",{className:"text-blue-600",children:["评分: ",(e.avg_rating||0).toFixed(1),"⭐"]}),(0,a.jsx)("div",{className:"text-xs",children:e.is_pinned?"\uD83D\uDCCC 已置顶":(e.exposure_score||50)>40?"\uD83D\uDC41️ 可见":"\uD83D\uDEAB 隐藏"})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.timeAgo}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>te(e._id,10),className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200",title:"增加曝光度 +10",children:"+10"}),(0,a.jsx)("button",{onClick:()=>te(e._id,50),className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200",title:"增加曝光度 +50",children:"+50"}),(0,a.jsx)("button",{onClick:()=>te(e._id,100),className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200",title:"增加曝光度 +100",children:"+100"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>te(e._id,-10),className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"减少曝光度 -10",children:"-10"}),(0,a.jsx)("button",{onClick:()=>te(e._id,-50),className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"减少曝光度 -50",children:"-50"}),(0,a.jsx)("button",{onClick:()=>te(e._id,-100),className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"减少曝光度 -100",children:"-100"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>s8(e._id,!e.is_pinned),className:"px-2 py-1 text-xs rounded ".concat(e.is_pinned?"bg-orange-100 text-orange-700 hover:bg-orange-200":"bg-blue-100 text-blue-700 hover:bg-blue-200"),title:e.is_pinned?"取消置顶":"置顶帖子",children:e.is_pinned?"取消置顶":"置顶"}),(0,a.jsx)("button",{onClick:()=>ts(e._id),className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200",title:"下架帖子",children:"下架"}),(0,a.jsx)("button",{onClick:()=>{ev(e),eb(!0)},className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200",title:"删除帖子",children:"删除"})]})]})})]},e._id)})})]})}):(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)(y,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"请搜索用户帖子"}),(0,a.jsx)("p",{className:"text-gray-500",children:"输入用户ID并点击搜索按钮来查看该用户的所有帖子"})]})]}),"content"===B&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"内容管理"})}),(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)(y,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"内容管理功能"}),(0,a.jsx)("p",{className:"text-gray-500",children:"此功能正在开发中，敬请期待..."})]})]}),"vip"===B&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(p,{className:"w-6 h-6 text-purple-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"VIP用户管理"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:sS,onChange:e=>{sD(e.target.value),tv()},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",children:[(0,a.jsx)("option",{value:"all",children:"全部VIP用户"}),(0,a.jsx)("option",{value:"active",children:"有效VIP用户"}),(0,a.jsx)("option",{value:"expired",children:"过期VIP用户"})]}),(0,a.jsx)("button",{onClick:tv,disabled:sP,className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors",children:sP?"加载中...":"刷新"})]})]})}),(0,a.jsx)("div",{className:"p-6",children:sP?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"加载VIP用户列表..."})]}):0===sC.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(p,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"暂无VIP用户"})]}):(0,a.jsx)("div",{className:"space-y-4",children:sC.map(e=>{var s,t,r,l,i,c,n;return(0,a.jsxs)("div",{className:"border rounded-lg p-4 ".concat(e.is_vip?"border-purple-200 bg-purple-50":"border-gray-200 bg-gray-50"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("img",{src:(null===(s=e.user_info)||void 0===s?void 0:s.avatar)||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:(null===(t=e.user_info)||void 0===t?void 0:t.nickname)||"未知用户",className:"w-12 h-12 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:(null===(r=e.user_info)||void 0===r?void 0:r.nickname)||"未知用户"}),e.is_vip&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:[(0,a.jsx)(p,{className:"w-3 h-3 mr-1"}),"VIP"]}),e.is_expired&&(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"已过期"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",e.user_id]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.is_vip?"剩余 ".concat(e.remaining_days," 天"):"已过期"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["到期时间: ",new Date(e.vip_end_time).toLocaleDateString("zh-CN")]})]}),(0,a.jsx)("button",{onClick:()=>tf(e),className:"px-3 py-1 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"管理"})]})]}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"每日发帖:"}),(0,a.jsx)("span",{className:"ml-1 font-medium",children:(null===(l=e.vip_benefits)||void 0===l?void 0:l.daily_post_limit)||5})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"信用分上限:"}),(0,a.jsx)("span",{className:"ml-1 font-medium",children:(null===(i=e.vip_benefits)||void 0===i?void 0:i.credit_score_limit)||100})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"专属标识:"}),(0,a.jsx)("span",{className:"ml-1 font-medium",children:(null===(c=e.vip_benefits)||void 0===c?void 0:c.special_badge)?"✓":"✗"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"优先支持:"}),(0,a.jsx)("span",{className:"ml-1 font-medium",children:(null===(n=e.vip_benefits)||void 0===n?void 0:n.priority_support)?"✓":"✗"})]})]})})]},e._id)})})})]})}),"ads"===B&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(S.Z,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总展示量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:eS.reduce((e,s)=>e+s.impressions,0).toLocaleString()})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(m,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总点击量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:eS.reduce((e,s)=>e+s.clicks,0).toLocaleString()})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(D.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总收益"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:tr(eS.reduce((e,s)=>e+s.spent,0))})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(v.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"活跃广告"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:eS.filter(e=>"active"===e.status).length})]})]})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"ads",label:"广告列表",icon:S.Z},{key:"positions",label:"广告位管理",icon:m},{key:"statistics",label:"数据统计",icon:D.Z}].map(e=>(0,a.jsxs)("button",{onClick:()=>eM(e.key),className:"".concat(eU===e.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2"),children:[(0,a.jsx)(e.icon,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:e.label})]},e.key))})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"ads"===eU?"广告列表":"positions"===eU?"广告位管理":"数据统计"})}),"ads"===eU&&(0,a.jsxs)(n.Z,{onClick:()=>eR(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"创建广告"})]})]}),"ads"===eU&&(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:eL?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"数据"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"收益"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===eS.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无广告数据"})}):eS.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.image_url&&(0,a.jsx)("img",{src:e.image_url,alt:e.title,className:"h-10 w-10 rounded object-cover mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.advertiser_name})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.position_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.ad_type})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:ta(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["展示: ",e.impressions.toLocaleString()]}),(0,a.jsxs)("div",{children:["点击: ",e.clicks.toLocaleString()]}),(0,a.jsxs)("div",{children:["CTR: ",tl(e.ctr)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["预算: ",tr(e.budget)]}),(0,a.jsxs)("div",{children:["已花费: ",tr(e.spent)]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(S.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(V.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?(0,a.jsx)(A.Z,{className:"w-4 h-4"}):(0,a.jsx)(v.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(w.Z,{className:"w-4 h-4"})})]})})]},e._id))})]})})}),"positions"===eU&&(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"广告位信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"尺寸"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:eV.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.page}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.width," \xd7 ",e.height]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.ad_type}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:ta(e.status)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(V.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900",children:"active"===e.status?(0,a.jsx)(A.Z,{className:"w-4 h-4"}):(0,a.jsx)(v.Z,{className:"w-4 h-4"})})]})})]},e.position_id))})]})})}),"statistics"===eU&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"用户体验策略"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"广告频率控制"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日最大展示次数"}),(0,a.jsx)("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:10,min:1,max:50})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最小展示间隔（分钟）"}),(0,a.jsx)("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:30,min:5,max:120})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"respectUserChoice",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"respectUserChoice",className:"ml-2 block text-sm text-gray-900",children:"尊重用户隐藏选择"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"adaptiveFrequency",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"adaptiveFrequency",className:"ml-2 block text-sm text-gray-900",children:"自适应展示频率"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"广告位策略"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"首页横幅"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"信息流广告"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：中"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"详情页底部"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户友好度：高"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"启用"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"启动弹窗"}),(0,a.jsx)("div",{className:"text-sm text-red-600",children:"用户友好度：低"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"禁用"}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"用户体验建议"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"基于用户行为数据的智能推荐"})]}),(0,a.jsx)(n.Z,{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"保存设置"})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"推荐"})]}),(0,a.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"原生信息流广告，用户接受度高"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-yellow-800",children:"谨慎"})]}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"横幅广告需要控制频率"})]}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800",children:"避免"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"弹窗广告容易引起用户反感"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"广告效果分析"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"92%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"用户满意度"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"3.2%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"平均点击率"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:"15s"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"平均停留时间"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"8%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"广告隐藏率"})]})]})]})]})]}),"activities"===B&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"活动管理"}),(0,a.jsx)("p",{className:"text-gray-600",children:"管理社区活动和系统配置"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(n.Z,{onClick:()=>eG(!0),variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"系统设置"})]}),(0,a.jsxs)(n.Z,{onClick:()=>eK(!0),className:"flex items-center space-x-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"创建活动"})]})]})]}),(0,a.jsx)("div",{className:"p-4 rounded-lg ".concat((null==eH?void 0:eH.enabled)?"bg-green-50 border border-green-200":"bg-yellow-50 border border-yellow-200"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-3 w-3 rounded-full mr-3 ".concat((null==eH?void 0:eH.enabled)?"bg-green-500":"bg-yellow-500")}),(0,a.jsxs)("span",{className:"font-medium ".concat((null==eH?void 0:eH.enabled)?"text-green-800":"text-yellow-800"),children:["活动系统状态：",(null==eH?void 0:eH.enabled)?"已启用":"已禁用"]}),!(null==eH?void 0:eH.enabled)&&(0,a.jsx)("span",{className:"ml-2 text-yellow-700",children:"（用户端不显示活动入口）"})]})}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow flex space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态筛选"}),(0,a.jsxs)("select",{value:eX,onChange:e=>e$(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"全部状态"}),(0,a.jsx)("option",{value:"DRAFT",children:"草稿"}),(0,a.jsx)("option",{value:"ACTIVE",children:"进行中"}),(0,a.jsx)("option",{value:"ENDED",children:"已结束"}),(0,a.jsx)("option",{value:"ARCHIVED",children:"已归档"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型筛选"}),(0,a.jsxs)("select",{value:eQ,onChange:e=>eY(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"全部类型"}),(0,a.jsx)("option",{value:"CONTEST",children:"评选竞赛"}),(0,a.jsx)("option",{value:"VOTING",children:"投票话题"}),(0,a.jsx)("option",{value:"DISCUSSION",children:"讨论活动"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"活动列表"})}),eB?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"活动信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"参与数据"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===eE.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center text-gray-500",children:"暂无活动数据"})}):eE.map(e=>{var s,t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 max-w-xs truncate",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-lg mr-2",children:tn(e.type)}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:td(e.type)})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:tx(e.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["开始：",to(e.start_time)]}),(0,a.jsxs)("div",{children:["结束：",to(e.end_time)]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["持续 ",e.duration_days," 天"]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["投票：",(null===(s=e.statistics_summary)||void 0===s?void 0:s.total_votes)||0]}),(0,a.jsxs)("div",{children:["评论：",(null===(t=e.statistics_summary)||void 0===t?void 0:t.total_comments)||0]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>window.open("/activities/".concat(e._id),"_blank"),className:"text-blue-600 hover:text-blue-900",title:"查看活动",children:(0,a.jsx)(S.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>c.C.info("编辑功能开发中"),className:"text-green-600 hover:text-green-900",title:"编辑活动",children:(0,a.jsx)(V.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{sX({title:"删除活动",message:"确定要删除这个活动吗？此操作不可恢复。",confirmText:"删除",cancelText:"取消",type:"danger",onConfirm:()=>{c.C.info("删除功能开发中")}})},className:"text-red-600 hover:text-red-900",title:"删除活动",children:(0,a.jsx)(w.Z,{className:"h-4 w-4"})})]})})]},e._id)})})]})})]})]}),"settings"===B&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"系统设置"}),(0,a.jsx)("p",{className:"text-gray-600",children:"配置系统参数和规则"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(L,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"图片上传设置"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户上传图片的限制和规则"})]}),e1?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"单张图片大小限制 (MB)"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"100",step:"1",value:e0.maxImageSize,onChange:e=>e2(s=>({...s,maxImageSize:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",e0.maxImageSize,"MB"]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"建议设置在5-30MB之间，过大会影响上传速度"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每帖最大图片数量"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"20",step:"1",value:e0.maxImagesPerPost,onChange:e=>e2(s=>({...s,maxImagesPerPost:parseInt(e.target.value)||1})),className:"block w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["当前限制：",e0.maxImagesPerPost,"张"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"支持的图片格式"}),(0,a.jsx)("div",{className:"space-y-2",children:["image/jpeg","image/png","image/webp","image/gif"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e0.allowedImageTypes.includes(e),onChange:s=>{s.target.checked?e2(s=>({...s,allowedImageTypes:[...s.allowedImageTypes,e]})):e2(s=>({...s,allowedImageTypes:s.allowedImageTypes.filter(s=>s!==e)}))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:e.replace("image/","").toUpperCase()})]},e))})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(T.Z,{className:"w-6 h-6 text-red-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"举报阈值设置"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"配置自动处理举报的阈值和规则"})]}),e5?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"帖子举报阈值"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:e6.post_report_threshold,onChange:e=>e3(s=>({...s,post_report_threshold:parseInt(e.target.value)||5})),className:"w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"人举报后自动隐藏帖子"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"当帖子被举报达到此数量时，系统将自动隐藏该帖子"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户举报阈值"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:e6.user_report_threshold,onChange:e=>e3(s=>({...s,user_report_threshold:parseInt(e.target.value)||10})),className:"w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"人举报后发送警告通知"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"当用户被举报达到此数量时，系统将发送警告通知"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"自动处理选项"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e6.auto_hide_posts,onChange:e=>e3(s=>({...s,auto_hide_posts:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"自动隐藏被举报的帖子"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e6.auto_warn_users,onChange:e=>e3(s=>({...s,auto_warn_users:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"自动警告被举报的用户"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e6.notification_enabled,onChange:e=>e3(s=>({...s,notification_enabled:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"启用系统通知"})]})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(T.Z,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"注意事项"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"阈值设置过低可能导致误处理，建议帖子阈值3-10，用户阈值5-15"}),(0,a.jsx)("li",{children:"被处理的用户可以通过申诉系统申请恢复"}),(0,a.jsx)("li",{children:"管理员可以在申诉管理中审核和处理申诉"}),(0,a.jsx)("li",{children:"系统通知将发送给被处理的用户"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(D.Z,{className:"w-6 h-6 text-purple-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"帖子数量限制"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"配置用户发布帖子的数量限制和自动下架规则"})]}),e1?(0,a.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载中..."})]}):(0,a.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"普通用户帖子上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"10",max:"1000",step:"10",value:e0.normalUserPostLimit,onChange:e=>e2(s=>({...s,normalUserPostLimit:parseInt(e.target.value)||100})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",e0.normalUserPostLimit,"条"]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"普通用户可发布的公开帖子数量上限"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"超级用户帖子上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"100",max:"5000",step:"50",value:e0.superUserPostLimit,onChange:e=>e2(s=>({...s,superUserPostLimit:parseInt(e.target.value)||500})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",e0.superUserPostLimit,"条"]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"超级用户可发布的公开帖子数量上限"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"普通用户每日发帖上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"1",max:"50",step:"1",value:e0.normalUserDailyPostLimit,onChange:e=>e2(s=>({...s,normalUserDailyPostLimit:parseInt(e.target.value)||5})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",e0.normalUserDailyPostLimit,"条/天"]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"普通用户每天可发布的帖子数量上限"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"VIP用户每日发帖上限"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("input",{type:"number",min:"5",max:"100",step:"5",value:e0.vipUserDailyPostLimit,onChange:e=>e2(s=>({...s,vipUserDailyPostLimit:parseInt(e.target.value)||20})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["当前限制：",e0.vipUserDailyPostLimit,"条/天"]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"VIP用户每天可发布的帖子数量上限"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"自动下架设置"}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e0.autoArchiveEnabled,onChange:e=>e2(s=>({...s,autoArchiveEnabled:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"启用自动下架机制"})]})}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"当用户发布新帖子超过上限时，自动下架最早的帖子并转为草稿"})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(D.Z,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"帖子数量限制说明"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"普通用户默认100条帖子上限，超级用户默认500条"}),(0,a.jsx)("li",{children:"普通用户默认每日5条发帖限制，VIP用户默认每日20条"}),(0,a.jsx)("li",{children:"个人设置的发帖限制优先级高于系统设置"}),(0,a.jsx)("li",{children:"发布新帖子时，如果超过上限，最早的帖子会被自动下架"}),(0,a.jsx)("li",{children:"下架的帖子会转为草稿，用户登录时可以看到并重新发布"}),(0,a.jsx)("li",{children:"草稿在云端保存30天后自动清理"}),(0,a.jsx)("li",{children:"用户在发布页面会看到接近上限的提醒"})]})]})]})})]}),(0,a.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between",children:[(0,a.jsxs)(n.Z,{variant:"outline",onClick:()=>{sX({title:"重置设置",message:"确定要重置为默认设置吗？当前的自定义设置将会丢失。",confirmText:"重置",cancelText:"取消",type:"warning",onConfirm:()=>{e2({maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"],autoReportThreshold:10,normalUserPostLimit:100,superUserPostLimit:500,autoArchiveEnabled:!0}),e3({post_report_threshold:5,user_report_threshold:10,auto_hide_posts:!0,auto_warn_users:!0,notification_enabled:!0}),c.C.success("已重置为默认设置")}})},className:"flex items-center space-x-2",children:[(0,a.jsx)(U,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"重置默认"})]}),(0,a.jsxs)(n.Z,{onClick:tm,loading:sJ||e5,disabled:sJ||e5,className:"flex items-center space-x-2",children:[(0,a.jsx)(P,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:sJ||e5?"保存中...":"保存所有设置"})]})]})]})]})]}),ex&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"创建管理员"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["用户名 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",value:em.username,onChange:e=>eh(s=>({...s,username:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入用户名"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["密码 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"password",value:em.password,onChange:e=>eh(s=>({...s,password:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入密码"})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"角色权限"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:"普通管理员 - 拥有所有业务权限（除删除其他管理员外）"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{eo(!1),eh({username:"",password:"",role:"admin",level:1,permissions:[]})},className:"flex-1",children:"取消"}),(0,a.jsx)(n.Z,{onClick:s1,className:"flex-1",children:"创建管理员"})]})]})}),ei&&et&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"approved"===en?"通过申诉":"驳回申诉"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"申诉内容："}),(0,a.jsx)("p",{className:"text-sm bg-gray-100 p-3 rounded-lg",children:et.reason})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"处理说明"}),(0,a.jsx)("textarea",{value:er,onChange:e=>el(e.target.value),placeholder:"请说明".concat("approved"===en?"通过":"驳回","的理由..."),className:"w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{ec(!1),ea(null),el("")},className:"flex-1",children:"取消"}),(0,a.jsx)(n.Z,{onClick:s6,disabled:ee===et._id,className:"flex-1",children:ee===et._id?"处理中...":"确认"})]})]})}),ej&&eN&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"删除帖子"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[eN.images&&eN.images[0]&&(0,a.jsx)("img",{src:eN.images[0],alt:eN.title,className:"h-16 w-16 rounded-lg object-cover mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:eN.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:eN.description})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["删除原因 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("textarea",{value:ef,onChange:e=>ew(e.target.value),placeholder:"请输入删除原因...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none",rows:3})]}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"警告"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-red-700",children:[(0,a.jsx)("p",{children:"删除帖子将同时删除："}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-1",children:[(0,a.jsx)("li",{children:"帖子的所有图片文件"}),(0,a.jsx)("li",{children:"所有点赞、收藏、评分记录"}),(0,a.jsx)("li",{children:"所有相关的举报和联系记录"})]}),(0,a.jsx)("p",{className:"mt-2 font-medium",children:"此操作不可撤销！"})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{eb(!1),ev(null),ew("")},className:"flex-1",children:"取消"}),(0,a.jsx)(n.Z,{onClick:s9,className:"flex-1 bg-red-600 hover:bg-red-700 text-white",disabled:!ef.trim(),children:"确认删除"})]})]})}),sH&&sW&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsxs)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3 ".concat("danger"===sW.type?"bg-red-100":"warning"===sW.type?"bg-yellow-100":"bg-blue-100"),children:["danger"===sW.type&&(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),"warning"===sW.type&&(0,a.jsx)("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(!sW.type||"info"===sW.type)&&(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:sW.title})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-gray-600",children:sW.message})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.Z,{variant:"outline",onClick:()=>{(null==sW?void 0:sW.onCancel)&&sW.onCancel(),sq(!1),sG(null)},className:"flex-1",children:sW.cancelText||"取消"}),(0,a.jsx)(n.Z,{onClick:()=>{(null==sW?void 0:sW.onConfirm)&&sW.onConfirm(),sq(!1),sG(null)},className:"flex-1 ".concat("danger"===sW.type?"bg-red-600 hover:bg-red-700 text-white":"warning"===sW.type?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"),children:sW.confirmText||"确认"})]})]})}),sm&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:()=>sh(!1),children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"调整用户信用分"}),(0,a.jsx)("button",{onClick:()=>{sh(!1),sp({score:0,reason:""})},className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(Z.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"调整分数"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>sp(e=>({...e,score:-10})),className:"px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm",children:"-10"}),(0,a.jsx)("button",{onClick:()=>sp(e=>({...e,score:-5})),className:"px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm",children:"-5"}),(0,a.jsx)("input",{type:"number",value:su.score,onChange:e=>sp(s=>({...s,score:parseInt(e.target.value)||0})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"输入分数（可为负数）"}),(0,a.jsx)("button",{onClick:()=>sp(e=>({...e,score:5})),className:"px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm",children:"+5"}),(0,a.jsx)("button",{onClick:()=>sp(e=>({...e,score:10})),className:"px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm",children:"+10"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["当前信用分: ",(0,a.jsx)("span",{className:"font-medium",children:(null==sn?void 0:sn.credit_score)||50})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["调整后信用分: ",(0,a.jsx)("span",{className:"font-medium ".concat(((null==sn?void 0:sn.credit_score)||50)+su.score>=80?"text-green-600":((null==sn?void 0:sn.credit_score)||50)+su.score>=60?"text-yellow-600":"text-red-600"),children:Math.max(0,Math.min(200,((null==sn?void 0:sn.credit_score)||50)+su.score))})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"分数范围: 0-100分（普通用户），100分以上（超级用户）"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>{sh(!1),sp({score:0,reason:""})},className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:ty,disabled:0===su.score,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"确认调整"})]})]})}),sE&&se&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:()=>sF(!1),children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"调整每日发帖限制"}),(0,a.jsx)("button",{onClick:()=>sF(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(Z.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"用户信息"}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:se.nickname}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",se.user_id]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"每日发帖限制"}),(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:sB,onChange:e=>sO(parseInt(e.target.value)||1),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"输入每日发帖限制"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["当前限制：",(null==sn?void 0:sn.daily_post_limit)||5,"条/天"]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)(T.Z,{className:"w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("p",{className:"font-medium",children:"注意事项"}),(0,a.jsx)("p",{children:"个人设置的发帖限制优先级高于系统设置"})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>sF(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:tN,disabled:sj,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:sj?"调整中...":"确认调整"})]})]})}),sN&&se&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:()=>sv(!1),children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"设置VIP用户"}),(0,a.jsx)("button",{onClick:()=>sv(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(Z.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"VIP有效期（天）"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>sw(7),className:"px-3 py-2 rounded-md text-sm ".concat(7===sf?"bg-purple-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"7天"}),(0,a.jsx)("button",{onClick:()=>sw(30),className:"px-3 py-2 rounded-md text-sm ".concat(30===sf?"bg-purple-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"30天"}),(0,a.jsx)("button",{onClick:()=>sw(90),className:"px-3 py-2 rounded-md text-sm ".concat(90===sf?"bg-purple-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"90天"}),(0,a.jsx)("input",{type:"number",value:sf,onChange:e=>sw(parseInt(e.target.value)||30),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"自定义天数",min:"1",max:"3650"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"VIP权益设置"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"每日发帖限制"}),(0,a.jsx)("input",{type:"number",value:s_.daily_post_limit,onChange:e=>sk(s=>({...s,daily_post_limit:parseInt(e.target.value)||50})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",min:"1",max:"1000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"信用分上限"}),(0,a.jsx)("input",{type:"number",value:s_.credit_score_limit,onChange:e=>sk(s=>({...s,credit_score_limit:parseInt(e.target.value)||200})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500",min:"100",max:"1000"})]})]})]}),(0,a.jsx)("div",{className:"bg-purple-50 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"text-sm text-purple-800",children:[(0,a.jsxs)("div",{children:["用户: ",(0,a.jsx)("span",{className:"font-medium",children:se.nickname})]}),(0,a.jsxs)("div",{children:["VIP有效期: ",(0,a.jsxs)("span",{className:"font-medium",children:[sf,"天"]})]}),(0,a.jsxs)("div",{children:["到期时间: ",(0,a.jsx)("span",{className:"font-medium",children:new Date(Date.now()+864e5*sf).toLocaleDateString("zh-CN")})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>sv(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:tb,disabled:sj,className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:sj?"设置中...":"确认设置"})]})]})}),sL&&sV&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[(0,a.jsx)(p,{className:"w-5 h-5 text-purple-600 mr-2"}),"VIP用户管理 - ",(null===(e=sV.user_info)||void 0===e?void 0:e.nickname)||"未知用户"]}),(0,a.jsx)("button",{onClick:()=>sT(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(Z.Z,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"用户信息"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,a.jsx)("img",{src:(null===(s=sV.user_info)||void 0===s?void 0:s.avatar)||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:(null===(t=sV.user_info)||void 0===t?void 0:t.nickname)||"未知用户",className:"w-16 h-16 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-900",children:(null===(o=sV.user_info)||void 0===o?void 0:o.nickname)||"未知用户"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",sV.user_id]})]})]})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"VIP状态"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"当前状态"}),(0,a.jsx)("div",{className:"mt-1 flex items-center",children:sV.is_vip?(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800",children:[(0,a.jsx)(p,{className:"w-4 h-4 mr-1"}),"有效VIP"]}):(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800",children:"已过期"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"剩余天数"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[sV.remaining_days," 天"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"开始时间"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:new Date(sV.vip_start_time).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"到期时间"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:new Date(sV.vip_end_time).toLocaleDateString("zh-CN")})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"VIP权益"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"每日发帖限制"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[(null===(M=sV.vip_benefits)||void 0===M?void 0:M.daily_post_limit)||5," 条"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"信用分上限"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[(null===(z=sV.vip_benefits)||void 0===z?void 0:z.credit_score_limit)||100," 分"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"专属VIP标识"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null===(R=sV.vip_benefits)||void 0===R?void 0:R.special_badge)?"✓ 已开启":"✗ 未开启"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"优先客服支持"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null===(E=sV.vip_benefits)||void 0===E?void 0:E.priority_support)?"✓ 已开启":"✗ 未开启"})]})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"管理操作"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[(0,a.jsx)("button",{onClick:()=>tw(sV.user_id,7),disabled:sj,className:"px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors",children:"延长7天"}),(0,a.jsx)("button",{onClick:()=>tw(sV.user_id,30),disabled:sj,className:"px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors",children:"延长30天"}),(0,a.jsx)("button",{onClick:()=>tw(sV.user_id,90),disabled:sj,className:"px-3 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors",children:"延长90天"}),(0,a.jsx)("button",{onClick:async()=>{try{sb(!0);let e=await i.petAPI.removeVipUser({user_id:sV.user_id});e.success?(c.C.success("移除VIP成功"),await tv(),sT(!1)):c.C.error(e.message||"移除VIP失败")}catch(e){console.error("移除VIP失败:",e),c.C.error("移除VIP失败")}finally{sb(!1)}},disabled:sj,className:"px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 transition-colors",children:"移除VIP"})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end space-x-3 mt-6",children:(0,a.jsx)("button",{onClick:()=>sT(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"关闭"})})]})})]})}},56334:function(e,s,t){"use strict";var a=t(57437),r=t(2265),l=t(68661);let i=r.forwardRef((e,s)=>{let{className:t,variant:r="primary",size:i="md",loading:c=!1,icon:n,children:d,disabled:x,...o}=e;return(0,a.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],t),ref:s,disabled:x||c,...o,children:[c&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!c&&n&&(0,a.jsx)("span",{className:"mr-2",children:n}),d]})});i.displayName="Button",s.Z=i},9356:function(e,s,t){"use strict";t.d(s,{C:function(){return m},ToastProvider:function(){return h}});var a=t(57437);t(2265);var r=t(69064),l=t(65302),i=t(45131),c=t(22252),n=t(33245),d=t(68661);let x={duration:4e3,position:"top-center",style:{background:"#fff",color:"#374151",border:"1px solid #e5e7eb",borderRadius:"8px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",padding:"12px 16px",fontSize:"14px",maxWidth:"400px"}},o=e=>{let{message:s,type:t,onDismiss:r}=e,x={success:(0,a.jsx)(l.Z,{className:"h-5 w-5 text-green-500"}),error:(0,a.jsx)(i.Z,{className:"h-5 w-5 text-red-500"}),warning:(0,a.jsx)(c.Z,{className:"h-5 w-5 text-yellow-500"}),info:(0,a.jsx)(n.Z,{className:"h-5 w-5 text-blue-500"})};return(0,a.jsxs)("div",{className:(0,d.cn)("flex items-center space-x-3 p-3 rounded-lg border shadow-lg",{success:"bg-green-50 border-green-200",error:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",info:"bg-blue-50 border-blue-200"}[t]),children:[x[t],(0,a.jsx)("span",{className:"flex-1 text-sm font-medium",children:s}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})},m={success:e=>{r.Am.custom(s=>(0,a.jsx)(o,{message:e,type:"success",onDismiss:()=>r.Am.dismiss(s.id)}),x)},error:e=>{r.Am.custom(s=>(0,a.jsx)(o,{message:e,type:"error",onDismiss:()=>r.Am.dismiss(s.id)}),{...x,duration:6e3})},warning:e=>{r.Am.custom(s=>(0,a.jsx)(o,{message:e,type:"warning",onDismiss:()=>r.Am.dismiss(s.id)}),x)},info:e=>{r.Am.custom(s=>(0,a.jsx)(o,{message:e,type:"info",onDismiss:()=>r.Am.dismiss(s.id)}),x)},loading:e=>r.Am.loading(e,{style:x.style,position:x.position}),dismiss:e=>{r.Am.dismiss(e)},promise:(e,s)=>r.Am.promise(e,s,{style:x.style,position:x.position})},h=()=>(0,a.jsx)(r.x7,{position:"top-center",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"transparent",boxShadow:"none",padding:0}}})},68661:function(e,s,t){"use strict";t.d(s,{cn:function(){return l},uf:function(){return i},vV:function(){return c}});var a=t(61994),r=t(53335);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}function i(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):e<1e5?"".concat((e/1e4).toFixed(1),"w"):"".concat(Math.floor(e/1e4),"w")}function c(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}}},function(e){e.O(0,[649,19,347,554,222,11,971,117,744],function(){return e(e.s=30804)}),_N_E=e.O()}]);