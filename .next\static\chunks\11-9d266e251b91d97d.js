"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[11],{98011:function(e,t,a){a.r(t),a.d(t,{activityAPI:function(){return d},app:function(){return s},auth:function(){return i},authAPI:function(){return y},db:function(){return n},getImageUrl:function(){return h},initCloudBase:function(){return l},petAPI:function(){return u},uploadFile:function(){return U},uploadFileToStatic:function(){return v}});var o=a(83393);let s=null,i=null,n=null,r=null,l=async()=>s?(console.log("CloudBase已初始化，直接返回"),s):r?(console.log("CloudBase正在初始化，等待完成..."),r):r=(async()=>{try{console.log("开始CloudBase NPM包初始化..."),console.log("CloudBase SDK版本:",o.Z),s=o.Z.init({env:"yichongyuzhou-3g9112qwf5f3487b",region:"ap-shanghai"}),console.log("CloudBase应用初始化完成，初始化服务..."),i=s.auth(),n=s.database(),console.log("检查登录状态...");let e=await i.getLoginState();if(console.log("当前登录状态:",e),e&&e.isLoggedIn)console.log("已登录，跳过匿名登录");else{console.log("未登录，开始匿名登录...");try{await i.signInAnonymously(),console.log("匿名登录成功！");let e=await i.getLoginState();console.log("新登录状态:",e)}catch(e){throw console.error("匿名登录失败:",e),Error("匿名登录失败: ".concat((null==e?void 0:e.message)||"未知错误"))}}return console.log("CloudBase NPM包初始化成功！"),console.log("应用实例:",s),console.log("认证服务:",i),console.log("数据库服务:",n),s}catch(e){throw console.error("CloudBase NPM包初始化失败:",e),console.error("错误详情:",null==e?void 0:e.message),r=null,Error("CloudBase初始化失败: ".concat((null==e?void 0:e.message)||"未知错误"))}})(),c=()=>{try{let e=localStorage.getItem("pet_platform_user"),t="true"===localStorage.getItem("pet_platform_logged_in");return e&&t}catch(e){return!1}},p=["toggleLike","toggleDislike","toggleBookmark","ratePet","exchangeContact","toggleFollow","reportPost","reportUser","wantPet","submitAppeal","getUserBookmarks","getUserPosts","getUserFollowing","getUserPermissions"],g=async(e,t,a)=>{try{if(console.log("\uD83D\uDE80 开始调用云函数: ".concat(e,".").concat(t),a),p.includes(t)){if(!c())throw console.log("❌ 操作需要登录，但用户未登录"),Error("请先登录后再进行此操作");console.log("✅ 用户已登录，可以执行操作")}console.log("\uD83D\uDD0D 检查CloudBase初始化状态...");let o=await l();if(!o)throw console.error("❌ CloudBase未初始化"),Error("CloudBase未初始化");console.log("\uD83D\uDD10 检查用户登录状态..."),i||(i=o.auth()),console.log("✅ CloudBase已初始化，准备调用云函数");let s=null,n=null;try{let e=localStorage.getItem("pet_platform_user");if(e){let t=JSON.parse(e);s=t._id,n=t._id,console.log("✅ 使用注册用户ID:",s)}else console.log("❌ 未找到注册用户信息")}catch(e){console.error("获取注册用户ID失败:",e)}let r=!["getUserInfo","getUserPosts","getUserBookmarks","getUserFollowing"].includes(t),g={name:e,data:{action:t,data:r?{...a,userId:s,openId:n}:{...a,currentUserId:s,openId:n}}};console.log("\uD83D\uDCCB 云函数调用参数:",g),console.log("\uD83D\uDCE1 正在调用云函数...");let u=await o.callFunction(g);if(console.log("✅ ".concat(e,".").concat(t," 调用成功:"),u),u.result)return console.log("\uD83D\uDCE6 返回result字段:",u.result),u.result;return console.log("\uD83D\uDCE6 返回完整结果:",u),u}catch(a){var o;console.error("❌ ".concat(e,".").concat(t," 调用失败:"),a),console.error("❌ 错误类型:",typeof a),console.error("❌ 错误构造函数:",null==a?void 0:null===(o=a.constructor)||void 0===o?void 0:o.name),(null==a?void 0:a.message)&&console.error("❌ 错误消息: ".concat(a.message)),(null==a?void 0:a.code)&&console.error("❌ 错误代码: ".concat(a.code)),(null==a?void 0:a.name)&&console.error("❌ 错误名称: ".concat(a.name)),(null==a?void 0:a.stack)&&console.error("❌ 错误堆栈:",a.stack);try{console.error("❌ 错误对象JSON:",JSON.stringify(a,null,2))}catch(e){console.error("❌ 无法序列化错误对象:",e),console.error("❌ 错误对象属性:",Object.keys(a)),console.error("❌ 错误对象值:",Object.values(a))}throw(null==a?void 0:a.response)&&console.error("❌ 响应错误:",a.response),(null==a?void 0:a.request)&&console.error("❌ 请求错误:",a.request),a}},u={async getPosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getPosts",e)},async getOptimizedPosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("optimizedPostQuery","query",e)},getPostDetail:async e=>g("pet-api","getPostDetail",e),createPost:async e=>g("pet-api","createPost",e),likePost:async e=>g("pet-api","likePost",e),toggleLike:async e=>g("pet-api","toggleLike",e),addLike:async e=>g("pet-api","addLike",e),toggleDislike:async e=>g("pet-api","toggleDislike",e),addDislike:async e=>g("pet-api","addDislike",e),toggleBookmark:async e=>g("pet-api","toggleBookmark",e),bookmarkPost:async e=>g("pet-api","bookmarkPost",e),exchangeContact:async e=>g("pet-api","exchangeContact",e),ratePost:async e=>g("pet-api","ratePost",e),toggleFollow:async e=>g("pet-api","toggleFollow",e),getCategories:async()=>g("pet-api","getCategories"),ratePet:async e=>g("pet-api","ratePet",e),reportPost:async e=>g("pet-api","reportPost",e),async getUserBookmarks(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserBookmarks",e)},async getUserPosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserPosts",e)},async getUserFollowing(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserFollowing",e)},async getUserFollowers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserFollowers",e)},async getUserNotifications(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getUserNotifications",e)},markNotificationRead:async e=>g("pet-api","markNotificationRead",e),deleteNotification:async e=>g("pet-api","deleteNotification",e),bulkDeleteNotifications:async e=>g("pet-api","bulkDeleteNotifications",e),updateProfile:async e=>g("pet-api","updateProfile",e),sendContactNotification:async e=>g("pet-api","sendContactNotification",e),async getReports(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getReports",e)},handleReport:async e=>g("pet-api","handleReport",e),banUser:async e=>g("pet-api","banUser",e),getUserInfo:async e=>g("user-management-api","getUserInfo",e),uploadToStatic:async e=>g("pet-api","uploadToStatic",e),updateAvatar:async e=>g("pet-api","updateAvatar",e),getImage:async e=>g("pet-api","getImage",e),blockUser:async e=>g("pet-api","blockUser",e),unblockUser:async e=>g("pet-api","unblockUser",e),async getBlockedUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getBlockedUsers",e)},reportUser:async e=>g("pet-api","reportUser",e),submitAppeal:async e=>g("pet-api","submitAppeal",e),getUserPermissions:async e=>g("pet-api","getUserPermissions",e),getTargetUserPermissions:async e=>g("pet-api","getTargetUserPermissions",e),updateUserPermissions:async e=>g("pet-api","updateUserPermissions",e),async getAppeals(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getAppeals",e)},handleAppeal:async e=>g("pet-api","handleAppeal",e),adminLogin:async e=>g("pet-api","adminLogin",e),async getAdmins(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getAdmins",e)},createAdmin:async e=>g("pet-api","createAdmin",e),updateAdmin:async e=>g("pet-api","updateAdmin",e),deleteAdmin:async e=>g("pet-api","deleteAdmin",e),getAds:async e=>g("pet-api","getAds",e||{}),getAdPositions:async e=>g("pet-api","getAdPositions",e||{}),createAd:async e=>g("pet-api","createAd",e),updateAd:async e=>g("pet-api","updateAd",e),deleteAd:async e=>g("pet-api","deleteAd",e),getAdStatistics:async e=>g("pet-api","getAdStatistics",e||{}),updatePostPriority:async e=>g("pet-api","updatePostPriority",e),getPostQualityScore:async e=>g("pet-api","getPostQualityScore",e),batchUpdatePostPriority:async e=>g("pet-api","batchUpdatePostPriority",e),getPostsByPriority:async e=>g("pet-api","getPostsByPriority",e||{}),batchUpdateAllPostsQuality:async()=>g("pet-api","batchUpdateAllPostsQuality",{}),createActivity:async e=>g("pet-api","createActivity",e),getActivities:async e=>g("pet-api","getActivities",e||{}),getActivityDetail:async e=>g("pet-api","getActivityDetail",e),updateActivity:async e=>g("pet-api","updateActivity",e),deleteActivity:async e=>g("pet-api","deleteActivity",e),voteInActivity:async e=>g("pet-api","voteInActivity",e),addActivityComment:async e=>g("pet-api","addActivityComment",e),getActivityComments:async e=>g("pet-api","getActivityComments",e),getSystemConfig:async()=>g("pet-api","getSystemConfig",{}),updateSystemConfig:async e=>g("pet-api","updateSystemConfig",e),getReportThresholds:async()=>g("pet-api","getReportThresholds",{}),updateReportThresholds:async e=>g("pet-api","updateReportThresholds",e),getPermissions:async()=>g("pet-api","getPermissions",{}),getUserRatedPosts:async e=>g("pet-api","getUserRatedPosts",e),updatePostStatus:async e=>g("pet-api","updatePostStatus",e),deletePost:async e=>g("pet-api","deleteMyPost",e),adminDeletePost:async e=>g("pet-api","adminDeletePost",e),deleteCloudFile:async e=>g("pet-api","deleteCloudFile",e),async getPostsForAdmin(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getPostsForAdmin",e)},getUserCreditScore:async e=>g("user-management-api","getUserCreditScore",e),updateUserCreditScore:async e=>g("user-management-api","updateUserCreditScore",e),setUserSuperStatus:async e=>g("user-management-api","setUserSuperStatus",e),getPostExposureScore:async e=>g("pet-api","getPostExposureScore",e),updatePostExposureScore:async e=>g("pet-api","updatePostExposureScore",e),getCreditScoreHistory:async e=>g("pet-api","getCreditScoreHistory",e),getVipUserInfo:async e=>g("vip-api","getVipUserInfo",e),setVipUser:async e=>g("vip-api","setVipUser",e),removeVipUser:async e=>g("vip-api","removeVipUser",e),updateVipUser:async e=>g("vip-api","updateVipUser",e),checkVipExpiry:async e=>g("vip-api","checkVipExpiry",e),getVipUserList:async e=>g("vip-api","getVipUserList",e),getExposureRanking:async e=>g("pet-api","getExposureRanking",e),setSuperUser:async e=>g("pet-api","setSuperUser",e),processReportPenalty:async e=>g("pet-api","processReportPenalty",e),handleAppeal:async e=>g("pet-api","handleAppeal",e),togglePostPin:async e=>g("pet-api","togglePostPin",e),getSystemSettings:async()=>g("pet-api","getSystemSettings",{}),updateSystemSettings:async e=>g("pet-api","updateSystemSettings",e),checkPostLimit:async e=>g("pet-api","checkPostLimit",e),archiveOldPosts:async e=>g("pet-api","archiveOldPosts",e),getUserDrafts:async e=>g("pet-api","getUserDrafts",e),deleteUserDraft:async e=>g("pet-api","deleteUserDraft",e)},d={getActiveActivities:async()=>g("pet-api","getActiveActivities"),async getActivities(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return g("pet-api","getActivities",e)},getSystemConfig:async()=>g("pet-api","getSystemConfig"),getDashboardStats:async()=>g("pet-api","getDashboardStats")},y={async sendVerificationCode(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"register";return g("email-auth","sendVerificationCode",{email:e,type:t})},async verifyCode(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"register";return g("email-auth","verifyCode",{email:e,code:t,type:a})},registerWithEmail:async(e,t,a,o)=>g("email-auth","registerWithEmail",{email:e,password:t,nickname:a,verificationCode:o}),loginWithEmail:async(e,t)=>g("email-auth","loginWithEmail",{email:e,password:t}),resetPassword:async(e,t,a)=>g("email-auth","resetPassword",{email:e,verificationCode:t,newPassword:a}),changePassword:async(e,t,a,o)=>g("email-auth","changePassword",{email:e,verificationCode:t,oldPassword:a,newPassword:o}),getCurrentUser:async e=>g("user-auth","getCurrentUser",e?{email:e}:{}),logout:async()=>g("user-auth","logout")},m=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:800,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.8;return new Promise(o=>{let s=document.createElement("canvas"),i=s.getContext("2d"),n=new Image;n.onload=()=>{let{width:r,height:l}=n,c=r,p=l,g=a;r>2e3||l>2e3?(p=l*(c=Math.min(r,t))/r,g=.8):r>t?(p=l*t/r,c=t,g=a):g=Math.max(.9,a),s.width=c,s.height=p,null==i||i.drawImage(n,0,0,c,p),s.toBlob(t=>{if(t){let a=new File([t],e.name,{type:"image/jpeg",lastModified:Date.now()}),s=Math.round((1-a.size/e.size)*100);console.log("图片压缩完成: ".concat(e.size," -> ").concat(a.size," (").concat(s,"% 减少)")),console.log("压缩参数: ".concat(r,"x").concat(l," -> ").concat(c,"x").concat(p,", 质量: ").concat(Math.round(100*g),"%")),o(a)}else o(e)},"image/jpeg",g)},n.src=URL.createObjectURL(e)})},h=async e=>{try{var t,a,o,s;let i=await l();if(!i)throw Error("CloudBase未初始化");let n=await i.callFunction({name:"pet-api",data:{action:"getImage",data:{fileId:e}}});if((null===(t=n.result)||void 0===t?void 0:t.success)&&(null===(o=n.result)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.url))return n.result.data.url;return console.error("获取图片URL失败:",null===(s=n.result)||void 0===s?void 0:s.message),"/placeholder-image.png"}catch(e){return console.error("获取图片URL失败:",e),"/placeholder-image.png"}},v=async e=>{try{var t,a,o,s;console.log("开始上传文件到静态托管:",e.name,"原始大小:",e.size);let i=e;e.type.startsWith("image/")&&(i=await m(e));let n=Date.now(),r=Math.random().toString(36).substring(2),c=e.name.split(".").pop(),p="".concat(n,"_").concat(r,".").concat(c);console.log("压缩后大小:",i.size);let g=await P(i),u=await l();if(!u)throw Error("CloudBase未初始化");let d=await u.callFunction({name:"pet-api",data:{action:"uploadToStatic",data:{fileName:p,fileData:g,contentType:i.type}}});if((null===(t=d.result)||void 0===t?void 0:t.success)&&(null===(o=d.result)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.url))return console.log("文件上传成功，URL:",d.result.data.url),d.result.data.url;throw Error((null===(s=d.result)||void 0===s?void 0:s.message)||"上传失败")}catch(e){throw console.error("文件上传失败:",e),e}},P=e=>new Promise((t,a)=>{let o=new FileReader;o.readAsDataURL(e),o.onload=()=>{t(o.result.split(",")[1])},o.onerror=e=>a(e)}),U=async e=>await v(e);t.default={petAPI:u,authAPI:y,activityAPI:d,uploadFile:U}}}]);