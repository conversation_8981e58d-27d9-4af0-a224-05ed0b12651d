"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[136],{95578:function(e,t,s){s.d(t,{Z:function(){return x}});var r=s(57437),a=s(2265),l=s(92369),i=s(66337),n=s(83774),o=s(82718),c=s(32489),d=s(87769),u=s(42208),m=s(56334),h=s(9356);function x(e){let{isOpen:t,onClose:s,currentUser:x,onUpdate:g,forceContactTab:y=!1}=e,[p,f]=(0,a.useState)("nickname"),[b,v]=(0,a.useState)(""),[j,w]=(0,a.useState)(""),[N,C]=(0,a.useState)(""),[k,S]=(0,a.useState)(""),[I,Z]=(0,a.useState)(!1),[H,_]=(0,a.useState)(!1),[D,K]=(0,a.useState)(!1),[z,J]=(0,a.useState)({type:"wechat",value:""}),[M,O]=(0,a.useState)(""),[R,T]=(0,a.useState)(""),[B,A]=(0,a.useState)(!1),[P,E]=(0,a.useState)(""),[V,$]=(0,a.useState)(!1),[F]=(0,a.useState)(new Date(Date.now()-216e7)),W=(e,t)=>{if(!t.trim())return"联系方式不能为空";if("wechat"===e){if(!/^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/.test(t))return"微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号"}else if("phone"===e&&!/^1[3-9]\d{9}$/.test(t))return"请输入正确的11位手机号";return""},X=()=>(null==x?void 0:x.loginType)==="wechat"&&(null==x?void 0:x.wechat_id)?{type:"wechat",value:x.wechat_id}:(null==x?void 0:x.loginType)==="phone"&&(null==x?void 0:x.phone)?{type:"phone",value:x.phone}:{type:"wechat",value:""};(0,a.useEffect)(()=>{if(t&&x){y?f("contact"):f("nickname"),v(x.nickname||"");let e=localStorage.getItem("contact_".concat(x._id));e?J(JSON.parse(e)):J(X());let t=localStorage.getItem("address_".concat(x._id));t?O(t):O("")}},[t,x,y]);let q=()=>!F||Math.floor((Date.now()-F.getTime())/864e5)>=30,G=()=>F?Math.max(0,30-Math.floor((Date.now()-F.getTime())/864e5)):0,L=async e=>Math.floor(3*Math.random()),Q=async()=>{if(!b.trim()){h.C.error("昵称不能为空");return}if(!q()){h.C.error("昵称30天只能修改一次，还需等待".concat(G(),"天"));return}$(!0);try{let e=await L(b);0===e?E(b):E("".concat(b,"#").concat(e+1)),A(!0)}catch(e){h.C.error("检查昵称失败")}finally{$(!1)}},U=async()=>{if(!b.trim()){h.C.error("昵称不能为空");return}if(!q()){h.C.error("昵称30天只能修改一次，还需等待".concat(G(),"天"));return}$(!0);try{await g({nickname:b}),h.C.success("昵称更新成功")}catch(e){h.C.error("昵称更新失败")}finally{$(!1)}},Y=async()=>{if(!j){h.C.error("请输入旧密码");return}if(!N){h.C.error("请输入新密码");return}if(N!==k){h.C.error("两次输入的密码不一致");return}if(N.length<6){h.C.error("密码长度至少6位");return}$(!0);try{await g({oldPassword:j,password:N}),h.C.success("密码修改成功"),w(""),C(""),S("")}catch(e){h.C.error("密码修改失败")}finally{$(!1)}},ee=async()=>{$(!0);try{localStorage.setItem("address_".concat(x._id),M),await g({address:M}),h.C.success("地址更新成功")}catch(e){h.C.error("地址更新失败")}finally{$(!1)}},et=async()=>{let e=W(z.type,z.value);if(e){T(e),h.C.error(e);return}$(!0);try{localStorage.setItem("contact_".concat(x._id),JSON.stringify(z)),await g({contactInfo:z}),T(""),h.C.success("联系方式更新成功"),y&&s()}catch(e){h.C.error("联系方式更新失败")}finally{$(!1)}};if(!t)return null;let es=[{id:"nickname",label:"昵称设置",icon:l.Z},{id:"password",label:"密码修改",icon:i.Z},{id:"address",label:"地址信息",icon:n.Z},{id:"contact",label:"联系方式",icon:o.Z}];return(0,r.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&s()},children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"个人设置"}),(0,r.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(c.Z,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"border-b border-gray-200 flex-shrink-0",children:(0,r.jsx)("div",{className:"flex",children:es.map(e=>{let t=e.icon;return(0,r.jsxs)("button",{onClick:()=>f(e.id),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat(p===e.id?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(t,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id)})})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:function(){switch(p){case"nickname":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"昵称"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"text",value:b,onChange:e=>v(e.target.value),placeholder:(null==x?void 0:x.loginType)==="wechat"?"微信用户":"邮箱用户",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!q()}),(0,r.jsx)(m.Z,{onClick:Q,loading:V,size:"sm",disabled:!q(),children:"检查"})]}),!q()&&(0,r.jsxs)("p",{className:"text-sm text-orange-600 mt-1",children:["昵称30天只能修改一次，还需等待",G(),"天"]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(m.Z,{onClick:U,loading:V,disabled:!q()||!b.trim(),children:"保存昵称"})})]});case"password":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"旧密码"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:I?"text":"password",value:j,onChange:e=>w(e.target.value),placeholder:"请输入当前密码",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("button",{type:"button",onClick:()=>Z(!I),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:I?(0,r.jsx)(d.Z,{className:"w-4 h-4"}):(0,r.jsx)(u.Z,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"新密码"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:H?"text":"password",value:N,onChange:e=>C(e.target.value),placeholder:"至少6位字符",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("button",{type:"button",onClick:()=>_(!H),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:H?(0,r.jsx)(d.Z,{className:"w-4 h-4"}):(0,r.jsx)(u.Z,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"确认密码"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:D?"text":"password",value:k,onChange:e=>S(e.target.value),placeholder:"再次输入新密码",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("button",{type:"button",onClick:()=>K(!D),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:D?(0,r.jsx)(d.Z,{className:"w-4 h-4"}):(0,r.jsx)(u.Z,{className:"w-4 h-4"})})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(m.Z,{onClick:Y,loading:V,disabled:!j||!N||!k,children:"修改密码"})})]});case"address":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"常用地址"}),(0,r.jsx)("input",{type:"text",value:M,onChange:e=>O(e.target.value),placeholder:"请输入您的常用地址",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["• 建议填写到县/区一级，如：北京市朝阳区、上海市浦东新区",(0,r.jsx)("br",{}),"• 此地址将作为发布宠物时的默认位置，提升交易效率"]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(m.Z,{onClick:ee,loading:V,children:"保存地址"})})]});case"contact":return(0,r.jsxs)("div",{className:"space-y-4",children:[y&&(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-sm text-blue-800",children:"\uD83D\uDCA1 检测到您还没有设置联系方式，请先完善联系信息才能使用联系功能"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"联系方式类型"}),(0,r.jsx)("div",{className:"flex space-x-3 mb-3",children:(0,r.jsxs)("select",{value:z.type,onChange:e=>{let t=e.target.value;J({...z,type:t}),T("")},className:"min-w-[120px] px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white",style:{backgroundImage:"url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\")",backgroundPosition:"right 0.5rem center",backgroundRepeat:"no-repeat",backgroundSize:"1.5em 1.5em"},children:[(0,r.jsx)("option",{value:"wechat",children:"微信号"}),(0,r.jsx)("option",{value:"phone",children:"手机号"})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"wechat"===z.type?"微信号":"手机号"}),(0,r.jsx)("input",{type:"text",value:z.value,onChange:e=>{J({...z,value:e.target.value}),T("")},placeholder:"wechat"===z.type?"请输入微信号":"请输入手机号",className:"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(R?"border-red-300":"border-gray-300")}),R&&(0,r.jsx)("p",{className:"text-sm text-red-600 mt-1",children:R}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-2",children:"wechat"===z.type?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"• 微信号格式：6-20位字符"}),(0,r.jsx)("p",{children:"• 必须以字母开头"}),(0,r.jsx)("p",{children:"• 可包含字母、数字、下划线、减号"}),(0,r.jsx)("p",{children:"• 示例：wechat123、user_name、my-id"})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"• 请输入11位中国大陆手机号"}),(0,r.jsx)("p",{children:"• 示例：13812345678"})]})})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(m.Z,{onClick:et,loading:V,disabled:!z.value.trim(),children:"保存联系方式"})})]});default:return null}}()})]}),B&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm w-full mx-4",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"确认昵称"}),(0,r.jsxs)("p",{className:"text-gray-600 mb-6",children:["您的昵称将显示为：",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:P})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(m.Z,{variant:"outline",onClick:()=>A(!1),className:"flex-1",children:"再想想"}),(0,r.jsx)(m.Z,{onClick:()=>{v(P),A(!1)},className:"flex-1",children:"确认"})]})]})})]})}},56334:function(e,t,s){var r=s(57437),a=s(2265),l=s(68661);let i=a.forwardRef((e,t)=>{let{className:s,variant:a="primary",size:i="md",loading:n=!1,icon:o,children:c,disabled:d,...u}=e;return(0,r.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[a],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],s),ref:t,disabled:d||n,...u,children:[n&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!n&&o&&(0,r.jsx)("span",{className:"mr-2",children:o}),c]})});i.displayName="Button",t.Z=i},59604:function(e,t,s){s.d(t,{Fd:function(){return l},KX:function(){return i}});class r{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e)return JSON.parse(e).sort((e,t)=>t.timestamp-e.timestamp)}catch(e){console.error("获取历史记录失败:",e)}return[]}addItem(e,t){if(!e.trim())return;let s={value:e.trim(),label:t||e.trim(),timestamp:Date.now()},r=this.getHistory();(r=r.filter(e=>e.value!==s.value)).unshift(s),r.length>this.maxItems&&(r=r.slice(0,this.maxItems)),this.saveHistory(r)}removeItem(e){let t=this.getHistory().filter(t=>t.value!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getHistoryValues(){return this.getHistory().map(e=>e.value)}hasItem(e){return this.getHistoryValues().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存历史记录失败:",e)}}constructor(e,t=10){this.storageKey=e,this.maxItems=t}}class a{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e){let t=JSON.parse(e),s=Date.now()-864e5*this.maxDays,r=t.filter(e=>e.timestamp>s);return r.length!==t.length&&this.saveHistory(r),r.sort((e,t)=>t.timestamp-e.timestamp)}}catch(e){console.error("获取浏览历史记录失败:",e)}return[]}addBrowseRecord(e,t,s,r,a){if(!e||!t)return;let l={postId:e,title:t.trim(),author:s.trim(),authorId:r,image:a,timestamp:Date.now()},i=this.getHistory();(i=i.filter(e=>e.postId!==l.postId)).unshift(l),i.length>this.maxItems&&(i=i.slice(0,this.maxItems)),this.saveHistory(i)}removeRecord(e){let t=this.getHistory().filter(t=>t.postId!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getRecentPostIds(){return this.getHistory().map(e=>e.postId)}hasBrowsed(e){return this.getRecentPostIds().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存浏览历史记录失败:",e)}}constructor(){this.storageKey="browse_history",this.maxDays=3,this.maxItems=50}}new r("location_history",10);let l=new r("category_history",8),i=new a}}]);