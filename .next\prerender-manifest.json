{"version": 4, "routes": {"/admin/ads": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/ads", "dataRoute": "/admin/ads.rsc"}, "/admin/activities": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/activities", "dataRoute": "/admin/activities.rsc"}, "/admin/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/dashboard", "dataRoute": "/admin/dashboard.rsc"}, "/admin/posts": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/posts", "dataRoute": "/admin/posts.rsc"}, "/admin": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin", "dataRoute": "/admin.rsc"}, "/debug": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/debug", "dataRoute": "/debug.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/post/detail": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/post/detail", "dataRoute": "/post/detail.rsc"}, "/favorites": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favorites", "dataRoute": "/favorites.rsc"}, "/profile": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile", "dataRoute": "/profile.rsc"}, "/test-email": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/test-email", "dataRoute": "/test-email.rsc"}, "/activities": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/activities", "dataRoute": "/activities.rsc"}, "/upload": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/upload", "dataRoute": "/upload.rsc"}, "/admin/performance": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/admin/performance", "dataRoute": "/admin/performance.rsc"}, "/profile/2ed3518f6875a7f905582dfd0fc94e98": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/2ed3518f6875a7f905582dfd0fc94e98.rsc"}, "/profile/c611b94668776114057326f5172f0bc5": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/c611b94668776114057326f5172f0bc5.rsc"}, "/profile/d77d384f6877626a0574989b7124319b": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/d77d384f6877626a0574989b7124319b.rsc"}, "/profile/eea1754d6873ce81053b899e0f3aa469": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/profile/[id]", "dataRoute": "/profile/eea1754d6873ce81053b899e0f3aa469.rsc"}}, "dynamicRoutes": {"/profile/[id]": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/profile/([^/]+?)(?:/)?$", "dataRoute": "/profile/[id].rsc", "fallback": null, "dataRouteRegex": "^/profile/([^/]+?)\\.rsc$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "ec622569d7a0931bbacfa5a90b3d776e", "previewModeSigningKey": "c525821d61dd5b4f1baa27717f30687cde3b98f895130944b8ffa4a3b7537e7f", "previewModeEncryptionKey": "aac9ada6ad9bc7d075c9101510ef66143e204b2ce9e298f97230fc4a14c308e4"}}