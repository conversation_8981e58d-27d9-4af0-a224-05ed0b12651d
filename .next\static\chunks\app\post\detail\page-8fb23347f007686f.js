(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[601],{8775:function(e,s,t){Promise.resolve().then(t.bind(t,12058))},12058:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return J}});var a=t(57437),r=t(2265),i=t(99376),n=t(32660),l=t(87769),c=t(42208),o=t(88997),d=t(37157),u=t(86595),x=t(82718),h=t(56266),m=t(68919),g=t(46211),f=t(92369),v=t(91723),p=t(83774),j=t(19764),y=t(10476),w=t(68661),b=t(9356),N=t(98011),_=t(98702),C=t(56334);let k={1:"普通",2:"极品",3:"神品",4:"仙品",5:"圣品"},S={1:"违法违规图片（血腥暴力色情等）",2:"图片模糊看不清"};var R=e=>{var s,t,i,n,l,c,o,d,x,h,m,g,f,v,p,j;let{isOpen:y,onClose:S,post:R,onSuccess:I}=e,[Z,P]=(0,r.useState)(0),[O,z]=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e,s;(null===(e=R.user_interactions)||void 0===e?void 0:e.hasRated)&&(null===(s=R.user_interactions)||void 0===s?void 0:s.userRating)&&P(R.user_interactions.userRating)},[R.user_interactions]);let E=()=>{var e,s;(null===(e=R.user_interactions)||void 0===e?void 0:e.hasRated)&&(null===(s=R.user_interactions)||void 0===s?void 0:s.userRating)?P(R.user_interactions.userRating):P(0),z(!1)},J=()=>{E(),S()},A=async()=>{var e;if(0===Z){b.C.warning("请选择评分");return}if(null===(e=R.user_interactions)||void 0===e?void 0:e.hasRated){b.C.warning("您已经评分过了");return}try{z(!0);let e=await N.petAPI.ratePet({postId:R._id,rating:Z});if(e.success){b.C.success("评分成功！");try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(R._id)||(e.unshift(R._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("保存评分记录到本地存储失败:",e)}I()}else b.C.error(e.message||"评分失败")}catch(e){b.C.error(e.message||"评分失败")}finally{z(!1)}};return(null===(s=R.user_interactions)||void 0===s?void 0:s.hasRated)?(0,a.jsx)(_.u_,{isOpen:y,onClose:J,title:"评分统计",size:"sm",children:(0,a.jsx)(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:R.avg_rating.toFixed(1)}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-1 mb-2",children:[1,2,3,4,5].map(e=>(0,a.jsx)(u.Z,{className:(0,w.cn)("h-6 w-6",e<=Math.round(R.avg_rating)?"text-yellow-500 fill-current":"text-gray-300")},e))}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["基于 ",R.ratings_count," 个评分"]})]}),(0,a.jsx)("div",{className:"space-y-2",children:[5,4,3,2,1].map(e=>{var s;let t=(null===(s=R.rating_stats)||void 0===s?void 0:s[e])||0,r=R.ratings_count>0?t/R.ratings_count*100:0;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1 w-16",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e}),(0,a.jsx)(u.Z,{className:"h-3 w-3 text-yellow-500 fill-current"})]}),(0,a.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(r,"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-500 w-8",children:t})]},e)})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"您的评分"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[1,2,3,4,5].map(e=>{var s;return(0,a.jsx)(u.Z,{className:(0,w.cn)("h-5 w-5",e<=((null===(s=R.user_interactions)||void 0===s?void 0:s.userRating)||0)?"text-yellow-500 fill-current":"text-gray-300")},e)})}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:k[null===(j=R.user_interactions)||void 0===j?void 0:j.userRating]})]})]})]})})}):(0,a.jsx)(_.u_,{isOpen:y,onClose:J,title:"为这只宠物评分",size:"sm",children:(0,a.jsx)(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:R.title})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"点击星星为这只宠物评分"}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[1,2,3,4,5].map(e=>{var s,t;return(0,a.jsx)("button",{onClick:()=>{var s;(null===(s=R.user_interactions)||void 0===s?void 0:s.hasRated)||P(e)},className:(0,w.cn)("transition-transform duration-200 focus:outline-none",(null===(s=R.user_interactions)||void 0===s?void 0:s.hasRated)?"cursor-not-allowed":"hover:scale-105 active:scale-95 cursor-pointer"),disabled:O||(null===(t=R.user_interactions)||void 0===t?void 0:t.hasRated),children:(0,a.jsx)(u.Z,{className:(0,w.cn)("h-8 w-8 transition-colors duration-200",e<=Z?"text-yellow-500 fill-current":"text-gray-300")})},e)})}),Z>0&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900",children:k[Z]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(null===(t=R.user_interactions)||void 0===t?void 0:t.hasRated)?"您的评价：":"",Z," 星评分"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:(null===(i=R.user_interactions)||void 0===i?void 0:i.hasRated)?"评分统计":"评分标准"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐ 普通"}),(null===(n=R.user_interactions)||void 0===n?void 0:n.hasRated)&&(0,a.jsxs)("span",{children:[(null===(l=R.rating_stats)||void 0===l?void 0:l[1])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐ 极品"}),(null===(c=R.user_interactions)||void 0===c?void 0:c.hasRated)&&(0,a.jsxs)("span",{children:[(null===(o=R.rating_stats)||void 0===o?void 0:o[2])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐ 神品"}),(null===(d=R.user_interactions)||void 0===d?void 0:d.hasRated)&&(0,a.jsxs)("span",{children:[(null===(x=R.rating_stats)||void 0===x?void 0:x[3])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐⭐ 仙品"}),(null===(h=R.user_interactions)||void 0===h?void 0:h.hasRated)&&(0,a.jsxs)("span",{children:[(null===(m=R.rating_stats)||void 0===m?void 0:m[4])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐⭐⭐ 圣品"}),(null===(g=R.user_interactions)||void 0===g?void 0:g.hasRated)&&(0,a.jsxs)("span",{children:[(null===(f=R.rating_stats)||void 0===f?void 0:f[5])||0,"人"]})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(C.Z,{variant:"outline",onClick:J,className:"flex-1",disabled:O,children:(null===(v=R.user_interactions)||void 0===v?void 0:v.hasRated)?"关闭":"取消"}),!(null===(p=R.user_interactions)||void 0===p?void 0:p.hasRated)&&(0,a.jsx)(C.Z,{onClick:A,loading:O,className:"flex-1",disabled:0===Z,children:"提交评分"})]})]})})})},I=e=>{let{isOpen:s,onClose:t,postId:i,onSuccess:n}=e,[l,c]=(0,r.useState)(0),[o,d]=(0,r.useState)(!1),u=()=>{c(0),d(!1)},x=()=>{u(),t()},h=async()=>{if(0===l){b.C.warning("请选择举报原因");return}try{d(!0);let e=await N.petAPI.reportPost({postId:i,reason:S[l]});e.success?n():b.C.error(e.message||"举报失败")}catch(e){b.C.error(e.message||"举报失败")}finally{d(!1)}},m=Object.entries(S).map(e=>{let[s,t]=e;return{id:parseInt(s),label:t}});return(0,a.jsx)(_.u_,{isOpen:s,onClose:x,title:"举报帖子",size:"sm",children:(0,a.jsx)(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"请选择举报原因，我们会认真处理每一个举报。恶意举报可能会影响您的账户信誉。"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"请选择举报原因："}),(0,a.jsx)("div",{className:"space-y-2",children:m.map(e=>(0,a.jsxs)("label",{className:(0,w.cn)("flex items-center p-3 rounded-lg border cursor-pointer transition-colors",l===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"),children:[(0,a.jsx)("input",{type:"radio",name:"reportReason",value:e.id,checked:l===e.id,onChange:()=>c(e.id),className:"sr-only"}),(0,a.jsx)("div",{className:(0,w.cn)("w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center",l===e.id?"border-red-500 bg-red-500":"border-gray-300"),children:l===e.id&&(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,a.jsx)("span",{className:(0,w.cn)("text-sm",l===e.id?"text-red-700 font-medium":"text-gray-700"),children:e.label})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(C.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:o,children:"取消"}),(0,a.jsx)(C.Z,{variant:"danger",onClick:h,loading:o,className:"flex-1",disabled:0===l,children:"提交举报"})]})]})})})},Z=t(95578),P=t(79994),O=t(59604),z=t(98734);t(7354),t(76889),t(86968),t(23469);let E=e=>{let s=new Date(e),t=s.getFullYear(),a=s.getMonth()+1,r=s.getDate();return"".concat(t,"/").concat(a,"/").concat(r)};function J(){var e,s,t,_,C;let k=(0,i.useRouter)(),S=(0,i.useSearchParams)().get("id"),{isLoggedIn:J,user:A}=(0,z.a)(),[D,L]=(0,r.useState)(null),[T,B]=(0,r.useState)(!0),[F,M]=(0,r.useState)(!1),[U,q]=(0,r.useState)(!1),[G,K]=(0,r.useState)(!1),[W,X]=(0,r.useState)(!1),[Y,H]=(0,r.useState)(0),[Q,V]=(0,r.useState)(!1),[$,ee]=(0,r.useState)(!1),[es,et]=(0,r.useState)(!1),[ea,er]=(0,r.useState)(!1),[ei,en]=(0,r.useState)(null),[el,ec]=(0,r.useState)(null),[eo,ed]=(0,r.useState)(new Set);(0,r.useEffect)(()=>{if(!S){k.push("/");return}eu();let e=localStorage.getItem("contactedPosts");if(e)try{let s=new Set(JSON.parse(e));ed(s)}catch(e){console.error("加载已联系帖子列表失败:",e)}},[S]);let eu=async()=>{try{B(!0);let n=await N.petAPI.getPostDetail({postId:S});if(n.success){var e,s,t,a,r,i;if(L(n.data),q((null===(e=n.data.user_interactions)||void 0===e?void 0:e.hasLiked)||!1),K((null===(s=n.data.user_interactions)||void 0===s?void 0:s.hasDisliked)||!1),H((null===(t=n.data.user_interactions)||void 0===t?void 0:t.userRating)||0),(null===(a=n.data.user_interactions)||void 0===a?void 0:a.hasRated)&&(null===(r=n.data.user_interactions)||void 0===r?void 0:r.userRating)>0)try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(n.data._id)||(e.unshift(n.data._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("同步评分记录到本地存储失败:",e)}n.data.author&&O.KX.addBrowseRecord(n.data._id,n.data.breed||"宠物发布",n.data.author.nickname||"匿名用户",n.data.author._id,null===(i=n.data.images)||void 0===i?void 0:i[0])}else b.C.error("加载失败："+n.message),k.push("/")}catch(e){console.error("加载帖子详情失败:",e),b.C.error("加载失败，请重试"),k.push("/")}finally{B(!1)}},ex=async()=>{try{let i=await N.petAPI.getPostDetail({postId:S});if(i.success){var e,s,t,a,r;if(L(i.data),q((null===(e=i.data.user_interactions)||void 0===e?void 0:e.hasLiked)||!1),K((null===(s=i.data.user_interactions)||void 0===s?void 0:s.hasDisliked)||!1),H((null===(t=i.data.user_interactions)||void 0===t?void 0:t.userRating)||0),(null===(a=i.data.user_interactions)||void 0===a?void 0:a.hasRated)&&(null===(r=i.data.user_interactions)||void 0===r?void 0:r.userRating)>0)try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(i.data._id)||(e.unshift(i.data._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("同步评分记录到本地存储失败:",e)}}}catch(e){console.error("刷新帖子数据失败:",e)}},eh=()=>{M(!F)},em=async()=>{if(D){if(!J||!A){b.C.error("请先登录");return}if(U||G){b.C.warning("您已经对此帖子做出过评价，无法更改");return}ec({title:"点赞确认",message:"喜欢跟不喜欢只能二选一且不能取消",onConfirm:()=>ef()})}},eg=async()=>{if(D){if(!J||!A){b.C.error("请先登录");return}if(U||G){b.C.warning("您已经对此帖子做出过评价，无法更改");return}ec({title:"不喜欢确认",message:"喜欢跟不喜欢只能二选一且不能取消",onConfirm:()=>ev()})}},ef=async()=>{if(D)try{let e=await N.petAPI.addLike({postId:D._id});e.success?(q(!0),L(e=>e?{...e,likes_count:(e.likes_count||0)+1}:null),b.C.success("点赞成功")):b.C.error(e.message||"操作失败")}catch(e){console.error("点赞操作失败:",e),b.C.error("操作失败，请重试")}finally{ec(null)}},ev=async()=>{if(D)try{let e=await N.petAPI.addDislike({postId:D._id});e.success?(K(!0),L(e=>e?{...e,dislikes_count:(e.dislikes_count||0)+1}:null),b.C.success("不喜欢")):b.C.error(e.message||"操作失败")}catch(e){console.error("不喜欢操作失败:",e),b.C.error("操作失败，请重试")}finally{ec(null)}},ep=()=>{let e=window.location.href,s=(null==D?void 0:D.breed)||"宠物交易平台",t="".concat(s," - 来看看这个可爱的宠物吧！");navigator.share?navigator.share({title:s,text:t,url:e}).then(()=>{b.C.success("分享成功")}).catch(s=>{"AbortError"!==s.name&&ej(e,t)}):ej(e,t)},ej=(e,s)=>{let t="".concat(s,"\n").concat(e);navigator.clipboard.writeText(t).then(()=>{b.C.success("分享内容已复制到剪贴板")}).catch(()=>{b.C.error("复制失败，请手动复制链接")})},ey=async e=>{try{let s=await N.petAPI.updateProfile(e);if(s.success)b.C.success("资料更新成功"),e.contactInfo&&A&&localStorage.setItem("contact_".concat(A._id),JSON.stringify(e.contactInfo)),void 0!==e.address&&A&&localStorage.setItem("address_".concat(A._id),e.address);else throw Error(s.message||"更新失败")}catch(e){throw b.C.error(e.message||"更新失败，请重试"),e}},ew=async()=>{if(!J||!A){b.C.error("请先登录");return}let e=null;try{let s=localStorage.getItem("contact_".concat(A._id));s&&(e=JSON.parse(s))}catch(e){console.log("获取联系方式失败")}if(!e||!e.value){b.C.warning("请先完善联系方式才能使用联系功能"),et(!0);return}D&&D.contact_info?ec({title:"联系确认",message:"点击确认后将与对方互换联系方式",showIcon:!1,onConfirm:async()=>{try{let s=await N.petAPI.exchangeContact({postId:D._id,contactInfo:e});if(s.success){b.C.success("联系方式已交换，请查看通知中心");let e=new Set(eo);e.add(D._id),ed(e),localStorage.setItem("contactedPosts",JSON.stringify(Array.from(e)))}else b.C.error(s.message||"交换失败")}catch(e){console.error("交换联系方式失败:",e),b.C.error("交换失败，请重试")}finally{ec(null)}}}):b.C.error("对方未提供联系方式")};return T?(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-500",children:"加载中..."})}):D?(0,a.jsxs)("div",{className:"min-h-screen bg-white relative",onClick:()=>X(!1),children:[(0,a.jsxs)("div",{className:(0,w.cn)("fixed top-4 left-4 z-30 flex space-x-2 transition-opacity duration-300",F?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsx)("button",{onClick:()=>k.back(),className:"w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:eh,className:"w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm",title:F?"退出清屏":"清屏模式",children:(0,a.jsx)(l.Z,{className:"h-5 w-5"})})]}),F&&(0,a.jsx)("div",{className:"fixed top-4 left-16 z-30",children:(0,a.jsx)("button",{onClick:eh,className:"w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm border border-white/20 text-white flex items-center justify-center hover:bg-black/70 transition-all duration-200 shadow-sm",title:"退出清屏",children:(0,a.jsx)(c.Z,{className:"h-5 w-5"})})}),(0,a.jsx)("div",{className:"relative h-screen",children:D.images&&D.images.length>0?(0,a.jsx)(j.tq,{modules:[y.tl,y.W_,y.LG],pagination:{clickable:!0},navigation:!0,zoom:{maxRatio:3,minRatio:1,toggle:!0},className:"h-full",children:D.images.map((e,s)=>(0,a.jsx)(j.o5,{children:(0,a.jsxs)("div",{className:"swiper-zoom-container relative h-full",children:[(0,a.jsx)("img",{src:e,alt:"".concat(D.breed||"宠物"," - ").concat(s+1),className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none"})]})},s))}):(0,a.jsx)("div",{className:"h-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-lg",children:"暂无图片"})})}),(0,a.jsxs)("div",{className:(0,w.cn)("fixed right-4 bottom-20 z-20 flex flex-col space-y-6 transition-opacity duration-300",F?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsxs)("button",{onClick:em,disabled:U||G,className:(0,w.cn)("flex flex-col items-center justify-center transition-all duration-200 group",U||G?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:[(0,a.jsx)(o.Z,{className:(0,w.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",U?"text-red-500 fill-current scale-110":U||G?"text-gray-400":"text-white hover:scale-110")}),(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:D.likes_count||0})]}),(0,a.jsxs)("button",{onClick:eg,disabled:U||G,className:(0,w.cn)("flex flex-col items-center justify-center transition-all duration-200 group",U||G?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:[(0,a.jsx)(d.Z,{className:(0,w.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",G?"text-gray-600 fill-current scale-110":U||G?"text-gray-400":"text-white hover:scale-110")}),(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:D.dislikes_count||0})]}),(0,a.jsxs)("button",{onClick:()=>{ex(),V(!0)},className:"flex flex-col items-center justify-center transition-all duration-200 group",children:[(0,a.jsx)(u.Z,{className:(0,w.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",Y>0?"text-orange-400 fill-current scale-110":"text-white hover:scale-110")}),Y>0&&(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:Y})]}),D&&("breeding"===D.type||"selling"===D.type||"lost"===D.type||"wanted"===D.type)&&(0,a.jsx)("button",{onClick:ew,disabled:eo.has(D._id),className:"flex flex-col items-center justify-center transition-all duration-200 group ".concat(eo.has(D._id)?"opacity-50 cursor-not-allowed":""),title:eo.has(D._id)?"已联系":"联系对方",children:(0,a.jsx)(x.Z,{className:"h-8 w-8 transition-all duration-200 drop-shadow-lg ".concat(eo.has(D._id)?"text-green-500 fill-current":"text-blue-400 hover:scale-110")})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),X(!W)},className:"flex flex-col items-center justify-center transition-all duration-200 group",children:(0,a.jsx)(h.Z,{className:"h-8 w-8 text-white transition-all duration-200 drop-shadow-lg hover:scale-110"})}),W&&(0,a.jsxs)("div",{className:"absolute right-14 bottom-0 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-30",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("button",{onClick:()=>{ep(),X(!1)},className:"w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"分享"})]}),(0,a.jsxs)("button",{onClick:()=>{X(!1),ee(!0)},className:"w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"举报"})]})]})]})]}),(0,a.jsxs)("div",{className:(0,w.cn)("absolute bottom-4 left-4 z-20 bg-black/50 backdrop-blur-md rounded-xl p-4 text-white max-w-sm transition-opacity duration-300 border border-white/10",F?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(null===(e=D.author)||void 0===e?void 0:e.avatar_url)?(0,a.jsx)("img",{src:D.author.avatar_url,alt:D.author.nickname,className:"w-12 h-12 rounded-full object-cover border-2 border-white/30 shadow-lg"}):(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-600/80 border-2 border-white/30 flex items-center justify-center shadow-lg",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(null===(s=D.author)||void 0===s?void 0:s._id)&&"anonymous"!==D.author._id?(0,a.jsx)("button",{onClick:()=>{let e=null;try{let s=localStorage.getItem("pet_platform_user");s&&(e=JSON.parse(s)._id)}catch(e){console.log("获取当前用户信息失败")}e&&D.author&&e===D.author._id?k.push("/profile"):D.author&&k.push("/profile/".concat(D.author._id))},className:"font-semibold text-white hover:text-white/80 transition-colors block truncate text-base",children:(null===(t=D.author)||void 0===t?void 0:t.nickname)||"匿名用户"}):(0,a.jsx)("span",{className:"font-semibold text-white block truncate text-base",children:(null===(_=D.author)||void 0===_?void 0:_.nickname)||"匿名用户"})})]}),(0,a.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{children:E(D.created_at)})]}),D.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:(C=D.location)&&"string"==typeof C?C.replace(/^#/,"").trim():""})]}),"breeding"===D.postType&&D.gender&&(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:(0,a.jsx)("span",{className:(0,w.cn)("px-2 py-1 rounded-full text-xs font-medium","male"===D.gender?"bg-blue-500/80 text-white":"bg-pink-500/80 text-white"),children:"male"===D.gender?"雄性":"雌性"})})]}),D.description&&(0,a.jsx)("div",{className:"border-t border-white/20 pt-3",children:(0,a.jsx)("div",{className:"text-sm text-white leading-relaxed",children:(0,a.jsx)("p",{className:"line-clamp-4",children:D.description})})})]}),D&&(0,a.jsx)(R,{isOpen:Q,onClose:()=>V(!1),post:D,onSuccess:()=>{ex()}}),D&&(0,a.jsx)(I,{isOpen:$,onClose:()=>ee(!1),postId:D._id,onSuccess:()=>{ee(!1),b.C.success("举报已提交，我们会尽快处理")}}),A&&(0,a.jsx)(Z.Z,{isOpen:es,onClose:()=>et(!1),currentUser:A,onUpdate:ey,forceContactTab:!0}),el&&(0,a.jsx)(P.Z,{isOpen:!0,onClose:()=>ec(null),onConfirm:el.onConfirm,title:el.title,message:el.message,type:"info",showIcon:el.showIcon})]}):(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-500",children:"宝贝不存在"})})}},79994:function(e,s,t){"use strict";var a=t(57437);t(2265);var r=t(98702),i=t(56334),n=t(63639),l=t(65302),c=t(33245);s.Z=e=>{let{isOpen:s,onClose:t,onConfirm:o,title:d,message:u,confirmText:x="确认",cancelText:h="取消",type:m="info",loading:g=!1,showIcon:f=!0}=e;return(0,a.jsx)(r.u_,{isOpen:s,onClose:t,title:"",size:"sm",showCloseButton:!1,children:(0,a.jsx)(r.fe,{children:(0,a.jsxs)("div",{className:"text-center",children:[f&&(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4",children:(()=>{switch(m){case"danger":return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-red-600"});case"warning":return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-yellow-600"});case"success":return(0,a.jsx)(l.Z,{className:"w-6 h-6 text-green-600"});default:return(0,a.jsx)(c.Z,{className:"w-6 h-6 text-blue-600"})}})()}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2 ".concat(f?"":"mt-2"),children:d}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:u}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-center",children:[(0,a.jsx)(i.Z,{variant:"outline",onClick:t,disabled:g,className:"min-w-[80px]",children:h}),(0,a.jsx)(i.Z,{variant:(()=>{switch(m){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:o,loading:g,disabled:g,className:"min-w-[80px]",children:x})]})]})})})}},98702:function(e,s,t){"use strict";t.d(s,{fe:function(){return d},u_:function(){return o}});var a=t(57437),r=t(2265),i=t(54887),n=t(32489),l=t(68661),c=t(56334);let o=e=>{let{isOpen:s,onClose:t,title:o,children:d,size:u="md",showCloseButton:x=!0,closeOnOverlayClick:h=!0,className:m}=e;if((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,t]),!s)return null;let g=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:h?t:void 0}),(0,a.jsxs)("div",{className:(0,l.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[u],m),onClick:e=>e.stopPropagation(),children:[(o||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[o&&(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:o}),x&&(0,a.jsx)(c.Z,{variant:"ghost",size:"sm",onClick:t,className:"p-1 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,i.createPortal)(g,document.body)},d=e=>{let{children:s,className:t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("p-4",t),children:s})}}},function(e){e.O(0,[35,649,19,347,554,222,61,11,734,136,971,117,744],function(){return e(e.s=8775)}),_N_E=e.O()}]);