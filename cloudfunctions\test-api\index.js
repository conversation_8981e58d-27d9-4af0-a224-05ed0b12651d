const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

/**
 * 测试云函数
 */
exports.main = async (event, context) => {
  try {
    console.log('测试云函数被调用，参数:', event);
    
    return {
      success: true,
      message: '测试云函数正常工作',
      data: {
        timestamp: new Date().toISOString(),
        event: event,
        context: context
      }
    };
  } catch (error) {
    console.error('测试云函数错误:', error);
    return {
      success: false,
      message: error.message || '测试云函数失败'
    };
  }
};
