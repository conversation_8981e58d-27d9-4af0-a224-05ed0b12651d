const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 生成唯一ID的工具函数
function generateId() {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 宠物交易平台 - 统一API云函数
 * 处理所有与宠物相关的业务逻辑
 */
exports.main = async (event, context) => {
  const { action, data, ...restParams } = event;
  const { OPENID } = cloud.getWXContext();

  // 如果没有data字段，说明参数直接在event中
  const actualData = data || restParams;

  // 获取OPENID，优先使用前端传递的openId，否则使用云开发的OPENID
  const finalOpenId = actualData?.openId || event?.openId || OPENID;
  console.log('云函数获取到的OPENID:', finalOpenId, '来源:', actualData?.openId ? '前端传递(data.openId)' : event?.openId ? '前端传递(event.openId)' : '云开发上下文');
  console.log('调试信息 - data.openId:', actualData?.openId, 'event.openId:', event?.openId, 'OPENID:', OPENID);

  // 获取用户ID，优先使用传入的userId，否则使用OPENID
  const userId = actualData?.userId || finalOpenId || 'anonymous';

  try {
    switch (action) {
      // 获取宠物列表
      case 'getPosts':
        return await getPosts(data, userId);

      // 获取宠物详情
      case 'getPostDetail':
        return await getPostDetail(data, userId);

      // 创建宠物帖子
      case 'createPost':
        return await createPost(data, userId);
      
      // 点赞/取消点赞
      case 'toggleLike':
        return await toggleLike(data, userId);

      // 永久点赞
      case 'addLike':
        return await addLike(data, userId);

      // 不喜欢/取消不喜欢
      case 'toggleDislike':
        return await toggleDislike(data, userId);

      // 永久不喜欢
      case 'addDislike':
        return await addDislike(data, userId);

      // 收藏/取消收藏
      case 'toggleBookmark':
        return await toggleBookmark(data, userId);

      // 想买宠物（一次性操作）
      case 'wantPet':
        return await wantPet(data, userId);

      // 交换联系方式
      case 'exchangeContact':
        return await exchangeContact(data, userId);

      // 评分宠物
      case 'ratePet':
        return await ratePet(data, finalOpenId);

      // 举报帖子
      case 'reportPost':
        return await reportPost(data, finalOpenId);

      // 举报用户
      case 'reportUser':
        return await reportUser(data, finalOpenId);

      // 提交申诉
      case 'submitAppeal':
        return await submitAppeal(data, finalOpenId);

      // 获取用户权限状态
      case 'getUserPermissions':
        return await getUserPermissions(data, finalOpenId);

      // 管理员功能 - 更新用户权限
      case 'updateUserPermissions':
        return await updateUserPermissions(data, finalOpenId);

      // 管理员功能 - 获取指定用户权限
      case 'getTargetUserPermissions':
        return await getTargetUserPermissions(data, finalOpenId);

      // 拉黑用户
      case 'blockUser':
        return await blockUser(data, finalOpenId);

      // 取消拉黑用户
      case 'unblockUser':
        return await unblockUser(data, finalOpenId);

      // 获取拉黑列表
      case 'getBlockedUsers':
        return await getBlockedUsers(data, finalOpenId);
      
      // 获取分类列表
      case 'getCategories':
        return await getCategories();
      
      // 关注/取消关注用户
      case 'toggleFollow':
        return await toggleFollow(data, finalOpenId);

      // 获取用户信息
      case 'getUserInfo':
        return await getUserInfo(actualData);

      // 获取用户收藏列表
      case 'getUserBookmarks':
        return await getUserBookmarks(data, finalOpenId);

      // 获取用户发布列表
      case 'getUserPosts':
        return await getUserPosts(data, finalOpenId);

      // 获取用户关注列表
      case 'getUserFollowing':
        return await getUserFollowing(data, finalOpenId);

      // 获取用户粉丝列表
      case 'getUserFollowers':
        return await getUserFollowers(data, finalOpenId);

      // 获取用户通知列表
      case 'getUserNotifications':
        return await getUserNotifications(data, finalOpenId);

      // 标记通知为已读
      case 'markNotificationRead':
        return await markNotificationRead(data, finalOpenId);

      // 删除通知
      case 'deleteNotification':
        return await deleteNotification(data, finalOpenId);

      // 批量删除通知
      case 'bulkDeleteNotifications':
        return await bulkDeleteNotifications(data, finalOpenId);

      // 发送联系通知
      case 'sendContactNotification':
        return await sendContactNotification(data, finalOpenId);

      // 搜索帖子
      case 'searchPosts':
        return await searchPosts(data);

      // 搜索用户
      case 'searchUsers':
        return await searchUsers(data);

      // 综合搜索（帖子+用户）
      case 'searchAll':
        return await searchAll(data);

      // 管理员功能 - 获取举报列表
      case 'getReports':
        return await getReports(data, finalOpenId);

      // 管理员功能 - 处理举报
      case 'handleReport':
        return await handleReport(data, finalOpenId);

      // 管理员功能 - 封禁用户
      case 'banUser':
        return await banUser(data, finalOpenId);

      // 申诉功能
      case 'submitAppeal':
        return await submitAppeal(data, finalOpenId);

      // 管理员功能 - 获取申诉列表
      case 'getAppeals':
        return await getAppeals(data, finalOpenId);

      // 管理员功能 - 处理申诉
      case 'handleAppeal':
        return await handleAppeal(data, finalOpenId);

      // 管理员认证
      case 'adminLogin':
        return await adminLogin(data, finalOpenId);

      // 管理员功能 - 获取管理员列表
      case 'getAdmins':
        return await getAdmins(data, finalOpenId);

      // 管理员功能 - 创建管理员
      case 'createAdmin':
        return await createAdmin(data, finalOpenId);

      // 管理员功能 - 更新管理员
      case 'updateAdmin':
        return await updateAdmin(data, finalOpenId);

      // 管理员功能 - 删除管理员
      case 'deleteAdmin':
        return await deleteAdmin(data, finalOpenId);

      // 管理员功能 - 获取权限列表
      case 'getPermissions':
        return await getPermissions(data, finalOpenId);

      // 用户删除自己的帖子
      case 'deleteMyPost':
        return await deleteMyPost(data, finalOpenId);

      // 管理员功能 - 删除帖子
      case 'deletePost':
        return await deletePost(data, finalOpenId);

      // 管理员功能 - 删除云存储文件
      case 'deleteCloudFile':
        return await deleteCloudFile(data, finalOpenId);

      // 管理员功能 - 获取帖子管理列表
      case 'getPostsForAdmin':
        return await getPostsForAdmin(data, finalOpenId);

      // 管理员功能 - 调整帖子曝光度
      case 'adjustPostExposure':
        return await adjustPostExposure(data, finalOpenId);

      // 管理员功能 - 下架帖子
      case 'takeDownPost':
        return await takeDownPost(data, finalOpenId);



      // 初始化示例数据
      case 'init_sample_data':
        return await initSampleData();

      // 图片代理访问
      case 'getImage':
        return await getImageProxy(data.fileId);

      // 上传文件到静态托管
      case 'uploadToStatic':
        return await uploadToStatic(data);

      // 更新用户头像
      case 'updateAvatar':
        return await updateUserAvatar(data, userId);

      // 更新用户资料
      case 'updateProfile':
        return await updateUserProfile(data, userId);

      // 广告管理相关API
      case 'getAds':
        return await getAds(data, finalOpenId);
      case 'getAdPositions':
        return await getAdPositions(data, finalOpenId);
      case 'createAd':
        return await createAd(data, finalOpenId);
      case 'updateAd':
        return await updateAd(data, finalOpenId);
      case 'deleteAd':
        return await deleteAd(data, finalOpenId);
      case 'getAdStatistics':
        return await getAdStatistics(data, finalOpenId);

      // 用户信用评分系统API
      case 'getUserCreditScore':
        return await getUserCreditScore(data, finalOpenId);
      case 'updateUserCreditScore':
        return await updateUserCreditScore(data, finalOpenId);
      case 'getPostExposureScore':
        return await getPostExposureScore(data, finalOpenId);
      case 'updatePostExposureScore':
        return await updatePostExposureScore(data, finalOpenId);
      case 'getCreditScoreHistory':
        return await getCreditScoreHistory(data, finalOpenId);
      case 'getExposureRanking':
        return await getExposureRanking(data, finalOpenId);
      case 'setSuperUser':
        return await setSuperUser(data, finalOpenId);
      case 'updateUserDailyPostLimit':
        return await updateUserDailyPostLimit(data, finalOpenId);
      case 'setVipUser':
        return await setVipUser(data, finalOpenId);
      case 'getVipUserInfo':
        return await getVipUserInfo(data, finalOpenId);
      case 'removeVipUser':
        return await removeVipUser(data, finalOpenId);
      case 'getVipUserList':
        return await getVipUserList(data, finalOpenId);
      case 'processReportPenalty':
        return await processReportPenalty(data, finalOpenId);
      case 'handleAppeal':
        return await handleAppeal(data, finalOpenId);
      case 'togglePostPin':
        return await togglePostPin(data, finalOpenId);
      case 'getSystemSettings':
        return await getSystemSettings();
      case 'updateSystemSettings':
        return await updateSystemSettings(data, finalOpenId);
      case 'checkPostLimit':
        return await checkPostLimit(data, finalOpenId);
      case 'archiveOldPosts':
        return await archiveOldPosts(data, finalOpenId);
      case 'getUserDrafts':
        return await getUserDrafts(data, finalOpenId);
      case 'deleteUserDraft':
        return await deleteUserDraft(data, finalOpenId);
      case 'cleanExpiredDrafts':
        return await cleanExpiredDrafts();

      // 活动系统API
      case 'createActivity':
        return await createActivity(data, finalOpenId);
      case 'getActivities':
        return await getActivities(data, finalOpenId);
      case 'getActivityDetail':
        return await getActivityDetail(data, finalOpenId);
      case 'updateActivity':
        return await updateActivity(data, finalOpenId);
      case 'deleteActivity':
        return await deleteActivity(data, finalOpenId);
      case 'participateInActivity':
        return await participateInActivity(data, finalOpenId);
      case 'voteInActivity':
        return await voteInActivity(data, finalOpenId);
      case 'getActivityResults':
        return await getActivityResults(data, finalOpenId);
      case 'addActivityComment':
        return await addActivityComment(data, finalOpenId);
      case 'getActivityComments':
        return await getActivityComments(data, finalOpenId);
      case 'getSystemConfig':
        return await getSystemConfig(data, finalOpenId);
      case 'updateSystemConfig':
        return await updateSystemConfig(data, finalOpenId);
      case 'processActivityAutomation':
        return await processActivityAutomation(data, finalOpenId);

      // 数据库优化 - 创建搜索索引
      case 'createSearchIndexes':
        return await createSearchIndexes();

      // 获取仪表板统计数据
      case 'getDashboardStats':
        return await getDashboardStats(data, finalOpenId);

      // 手动审核帖子
      case 'manualAudit':
        return await manualAuditPost(data.postId, finalOpenId);

      // 举报阈值管理API
      case 'getReportThresholds':
        return await getReportThresholdsAPI(data, finalOpenId);

      case 'updateReportThresholds':
        return await updateReportThresholds(data, finalOpenId);

      default:
        return {
          success: false,
          message: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: error.message || '服务器内部错误'
    };
  }
}

// 图片代理访问函数
async function getImageProxy(fileId) {
  try {
    console.log('图片代理访问，fileId:', fileId);

    if (!fileId) {
      return {
        success: false,
        message: '缺少文件ID'
      };
    }

    // 检查是否是完整URL
    if (fileId.startsWith('http')) {
      console.log('fileId是完整URL，无需生成临时URL');
      return {
        success: true,
        data: {
          url: fileId
        }
      };
    }

    // 生成临时访问链接（30天有效期）
    const tempUrl = await cloud.getTempFileURL({
      fileList: [fileId],
      maxAge: 30 * 24 * 60 * 60 // 30天有效期
    });

    console.log('生成临时URL结果:', tempUrl);

    if (tempUrl.fileList && tempUrl.fileList[0] && tempUrl.fileList[0].tempFileURL) {
      return {
        success: true,
        data: {
          url: tempUrl.fileList[0].tempFileURL
        }
      };
    } else {
      return {
        success: false,
        message: '无法生成图片访问链接'
      };
    }
  } catch (error) {
    console.error('图片代理访问失败:', error);
    return {
      success: false,
      message: '图片访问失败: ' + error.message
    };
  }
}

// 删除图片文件（支持云存储和静态托管）
async function deleteImageFile(imageUrl) {
  try {
    console.log('删除图片文件:', imageUrl);

    if (!imageUrl) {
      console.log('图片URL为空，跳过删除');
      return;
    }

    // 1. 处理云存储文件（旧格式）
    if (imageUrl.startsWith('cloud://') || (!imageUrl.startsWith('http') && imageUrl.includes('/'))) {
      console.log('删除云存储文件:', imageUrl);
      await cloud.deleteFile({
        fileList: [imageUrl]
      });
      return;
    }

    // 2. 处理静态托管文件（新格式）
    if (imageUrl.startsWith('https://') && imageUrl.includes('tcloudbaseapp.com')) {
      console.log('删除静态托管文件:', imageUrl);

      // 从URL中提取文件路径
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');

      // 路径格式: /images/filename.jpg
      if (pathParts.length >= 2 && pathParts[1] === 'images') {
        const fileName = pathParts.slice(2).join('/'); // 支持子目录
        const cloudPath = `images/${fileName}`;

        console.log('静态托管文件路径:', cloudPath);

        // 删除静态托管中的文件
        await cloud.deleteFile({
          fileList: [cloudPath]
        });

        console.log('静态托管文件删除成功:', cloudPath);
      } else {
        console.log('无法解析静态托管文件路径:', imageUrl);
      }
      return;
    }

    // 3. 外部图片URL（如Unsplash等），不需要删除
    if (imageUrl.startsWith('https://')) {
      console.log('外部图片URL，无需删除:', imageUrl);
      return;
    }

    console.log('未知图片URL格式，跳过删除:', imageUrl);

  } catch (error) {
    console.error('删除图片文件失败:', imageUrl, error);
    throw error;
  }
}

// 上传文件到静态托管（公开访问）
async function uploadToStatic({ fileName, fileData, contentType }) {
  try {
    console.log('上传文件到静态托管:', fileName, 'contentType:', contentType);

    if (!fileName || !fileData) {
      return {
        success: false,
        message: '缺少文件名或文件数据'
      };
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const fileExtension = fileName.split('.').pop();
    const uniqueFileName = `${fileName.split('.')[0]}_${timestamp}_${randomStr}.${fileExtension}`;

    // 将base64转换为Buffer
    const buffer = Buffer.from(fileData, 'base64');

    console.log('准备上传文件:', uniqueFileName, '大小:', buffer.length);

    // 使用云开发SDK上传到云存储，然后生成临时访问URL
    const cloudbase = require('@cloudbase/node-sdk');
    const app = cloudbase.init({
      env: cloudbase.SYMBOL_CURRENT_ENV
    });

    // 上传到云存储
    const uploadResult = await app.uploadFile({
      cloudPath: `images/${uniqueFileName}`,
      fileContent: buffer
    });

    console.log('云存储上传结果:', uploadResult);

    if (uploadResult && uploadResult.fileID) {
      // 生成临时访问URL（有效期1小时）
      const tempUrlResult = await app.getTempFileURL({
        fileList: [uploadResult.fileID],
        maxAge: 3600 // 1小时
      });

      console.log('临时URL生成结果:', tempUrlResult);

      if (tempUrlResult && tempUrlResult.fileList && tempUrlResult.fileList.length > 0) {
        const tempUrl = tempUrlResult.fileList[0].tempFileURL;

        // 触发免费图片审核（异步）
      try {
        await triggerFreeImageAudit(tempUrl, uploadResult.fileID, fileName, buffer.length);
      } catch (auditError) {
        console.warn('触发图片审核失败:', auditError);
        // 审核失败不影响上传，但会记录日志
      }

      return {
          success: true,
          data: {
            fileID: uploadResult.fileID,
            url: tempUrl // 返回临时访问URL
          }
        };
      } else {
        console.error('生成临时URL失败');
        return {
          success: false,
          message: '生成临时URL失败'
        };
      }
    } else {
      console.error('上传失败，没有返回fileID');
      return {
        success: false,
        message: '上传失败，没有返回文件ID'
      };
    }
  } catch (error) {
    console.error('上传失败:', error);
    return {
      success: false,
      message: '上传失败: ' + error.message
    };
  }
}

// 更新用户头像
async function updateUserAvatar({ avatarUrl }, userId) {
  try {
    console.log('更新用户头像:', userId, avatarUrl);

    if (!avatarUrl) {
      return {
        success: false,
        message: '缺少头像URL'
      };
    }

    // 检查用户是否存在
    const userResult = await db.collection('users').doc(userId).get();
    if (!userResult.data) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    // 检查头像更新限制（30天只能更新一次）
    const lastAvatarUpdate = userResult.data.last_avatar_update;
    if (lastAvatarUpdate) {
      const daysSinceLastUpdate = (new Date() - new Date(lastAvatarUpdate)) / (1000 * 60 * 60 * 24);
      if (daysSinceLastUpdate < 30) {
        const remainingDays = Math.ceil(30 - daysSinceLastUpdate);
        return {
          success: false,
          message: `头像更新太频繁，请等待${remainingDays}天后再试`
        };
      }
    }

    // 更新用户头像 - 先获取现有数据，然后合并更新
    const existingUser = await db.collection('users').doc(userId).get();
    if (!existingUser.data) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const updatedUserData = {
      ...existingUser.data,
      avatar_url: avatarUrl,
      last_avatar_update: new Date(),
      updated_at: new Date()
    };

    // 删除_id字段避免冲突
    delete updatedUserData._id;

    await db.collection('users').doc(userId).set({
      data: updatedUserData
    });

    return {
      success: true,
      message: '头像更新成功',
      data: {
        avatar_url: avatarUrl
      }
    };
  } catch (error) {
    console.error('更新用户头像失败:', error);
    return {
      success: false,
      message: '头像更新失败: ' + error.message
    };
  }
};

// 更新用户资料
async function updateUserProfile(data, userId) {
  try {
    console.log('更新用户资料:', userId, data);

    // 检查用户是否存在
    const userResult = await db.collection('users').doc(userId).get();
    if (!userResult.data) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    const updateData = {};

    // 处理昵称更新
    if (data.nickname !== undefined) {
      // 检查昵称更新限制（30天只能更新一次）
      const lastNicknameUpdate = userResult.data.last_nickname_update;
      if (lastNicknameUpdate) {
        const daysSinceLastUpdate = (new Date() - new Date(lastNicknameUpdate)) / (1000 * 60 * 60 * 24);
        if (daysSinceLastUpdate < 30) {
          const remainingDays = Math.ceil(30 - daysSinceLastUpdate);
          return {
            success: false,
            message: `昵称更新太频繁，请等待${remainingDays}天后再试`
          };
        }
      }
      updateData.nickname = data.nickname;
      updateData.last_nickname_update = new Date();

      // 标记需要同步昵称到帖子
      updateData._needSyncNickname = true;
    }

    // 处理密码更新
    if (data.password && data.oldPassword) {
      // 验证旧密码 - 检查是否为bcrypt格式
      const crypto = require('crypto');
      let isValidPassword = false;

      if (userResult.data.password.startsWith('$2')) {
        // bcrypt格式密码
        const bcrypt = require('bcryptjs');
        isValidPassword = await bcrypt.compare(data.oldPassword, userResult.data.password);
      } else {
        // SHA-256格式密码（旧格式）
        const hashedOldPassword = crypto.createHash('sha256').update(data.oldPassword).digest('hex');
        isValidPassword = hashedOldPassword === userResult.data.password;
      }

      if (!isValidPassword) {
        return {
          success: false,
          message: '旧密码错误'
        };
      }

      // 加密新密码 - 使用bcrypt
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(data.password, 10);
      updateData.password = hashedPassword;
    }

    // 处理联系方式更新
    if (data.contactInfo) {
      updateData.contact_info = data.contactInfo;
    }

    // 处理地址更新
    if (data.address !== undefined) {
      updateData.address = data.address;
    }

    // 检查是否有需要更新的数据
    if (Object.keys(updateData).length === 0) {
      return {
        success: false,
        message: '没有需要更新的数据'
      };
    }

    // 添加更新时间
    updateData.updated_at = new Date();

    // 过滤掉undefined值
    const cleanUpdateData = {};
    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined) {
        cleanUpdateData[key] = value;
      }
    }

    console.log('准备更新用户数据:', userId, cleanUpdateData);

    // 更新用户数据 - 使用正确的语法
    const updateResult = await db.collection('users').doc(userId).update({
      data: cleanUpdateData
    });

    console.log('用户数据更新结果:', updateResult);

    // 如果更新了昵称，同步更新该用户所有帖子的作者昵称
    if (cleanUpdateData._needSyncNickname && cleanUpdateData.nickname) {
      try {
        console.log('开始同步昵称到用户帖子:', userId, cleanUpdateData.nickname);

        // 查询该用户的所有帖子
        const userPostsResult = await db.collection('posts')
          .where({
            author_id: userId
          })
          .get();

        if (userPostsResult.data && userPostsResult.data.length > 0) {
          console.log(`找到${userPostsResult.data.length}个帖子需要同步昵称`);

          // 逐个更新帖子的作者昵称
          for (const post of userPostsResult.data) {
            try {
              await db.collection('posts').doc(post._id).update({
                'author.nickname': cleanUpdateData.nickname,
                author_name: cleanUpdateData.nickname,
                updated_at: new Date()
              });
            } catch (updateError) {
              console.error(`更新帖子 ${post._id} 昵称失败:`, updateError);
            }
          }

          console.log('昵称同步到帖子完成');
        }
      } catch (error) {
        console.error('同步昵称到帖子失败:', error);
        // 不影响主要的用户更新操作，只记录错误
      }

      // 移除临时标记
      delete cleanUpdateData._needSyncNickname;
    }

    return {
      success: true,
      message: '用户资料更新成功',
      data: cleanUpdateData
    };

  } catch (error) {
    console.error('更新用户资料失败:', error);
    return {
      success: false,
      message: '用户资料更新失败: ' + error.message
    };
  }
}

// 处理图片URL的通用函数
async function processImageUrls(images, postId) {
  console.log('processImageUrls 输入:', images, 'postId:', postId);
  if (!images || images.length === 0) return [];



  return await Promise.all(
    images.map(async (imageUrl, index) => {
      // 如果是fileID格式，生成临时访问链接
      if (imageUrl.startsWith('cloud://') || (!imageUrl.startsWith('http') && imageUrl.includes('/'))) {
        try {
          console.log('检测到fileID，生成临时URL:', imageUrl);

          // 生成临时访问链接
          const tempUrl = await cloud.getTempFileURL({
            fileList: [imageUrl]
          });

          console.log('getTempFileURL 结果:', tempUrl);

          if (tempUrl.fileList && tempUrl.fileList[0] && tempUrl.fileList[0].tempFileURL) {
            console.log('成功生成临时URL:', tempUrl.fileList[0].tempFileURL);
            return tempUrl.fileList[0].tempFileURL;
          }
        } catch (error) {
          console.log('生成临时URL失败:', error);
        }
      }

      // 如果是完整的URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl;
      }

      // 其他情况，尝试作为文件路径处理
      try {
        const tempUrl = await cloud.getTempFileURL({
          fileList: [imageUrl]
        });

        if (tempUrl.fileList && tempUrl.fileList[0] && tempUrl.fileList[0].tempFileURL) {
          return tempUrl.fileList[0].tempFileURL;
        }
      } catch (error) {
        console.log('作为文件路径处理失败:', error);
      }

      // 最后返回原URL
      return imageUrl;
    })
  );
}

// 获取宠物列表
async function getPosts({ category, page = 1, limit = 20, sortBy = 'exposure_score' }, userId) {
  const skip = (page - 1) * limit;

  // 只显示已发布的帖子
  const whereConditions = { status: 'published' };

  if (category) {
    whereConditions.category = category;
  }

  let result;
  // 根据排序方式处理
  if (sortBy === 'exposure_score') {
    // 智能排序：使用新的排序算法
    result = await getIntelligentSortedPosts(whereConditions, skip, limit);
  } else {
    // 其他排序方式
    let query = db.collection('posts').where(whereConditions);
    result = await query
      .orderBy('is_pinned', 'desc') // 置顶帖子优先
      .orderBy(sortBy, 'desc')
      .skip(skip)
      .limit(limit)
      .get();
  }

  // 如果用户已登录，过滤被拉黑用户的帖子
  let filteredPosts = result.data;
  if (userId && userId !== 'anonymous') {
    // 获取当前用户拉黑的用户列表
    const blacklistResult = await db.collection('blacklist')
      .where({ user_id: userId })
      .get();

    const blockedUserIds = blacklistResult.data.map(item => item.blocked_user_id);

    // 过滤掉被拉黑用户的帖子
    filteredPosts = result.data.filter(post => {
      const authorId = post.author_id || post.userId;
      return !blockedUserIds.includes(authorId);
    });
  }
  
  // 获取每个帖子的统计信息
  const postsWithStats = await Promise.all(
    filteredPosts.map(async (post) => {
      const [likes, wants, ratings, bookmarks, dislikes, reports, processedImages] = await Promise.all([
        db.collection('likes').where({ post_id: post._id }).count(),
        db.collection('wants').where({ post_id: post._id }).count(),
        db.collection('ratings').where({ post_id: post._id }).get(),
        db.collection('bookmarks').where({ post_id: post._id }).count(),
        db.collection('dislikes').where({ post_id: post._id }).count(),
        db.collection('post_reports').where({ post_id: post._id }).count(),
        processImageUrls(post.images || [], post._id)
      ]);

      // 计算平均评分
      const avgRating = ratings.data.length > 0
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length
        : 0;

      // 获取用户信用分
      let userCreditScore = 50; // 默认信用分
      try {
        const creditResult = await db.collection('user_credit_scores')
          .where({ user_id: post.author_id })
          .get();
        if (creditResult.data.length > 0) {
          userCreditScore = creditResult.data[0].credit_score;
        }
      } catch (error) {
        console.warn('获取用户信用分失败:', error);
      }

      // 计算用户评分分数
      const userRatingScore = ratings.data.length > 0
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0)
        : 0;

      // 计算帖子类型分数
      const typeScore = calculatePostTypeScore(post.type);

      // 计算曝光度分数
      const exposureScore = userCreditScore +
                           likes.total -
                           dislikes.total +
                           userRatingScore +
                           typeScore -
                           reports.total;

      return {
        ...post,
        images: processedImages, // 使用处理后的图片URL
        likes_count: likes.total,
        wants_count: wants.total,
        bookmarks_count: bookmarks.total,
        ratings_count: ratings.data.length,
        avg_rating: Math.round(avgRating * 10) / 10,
        dislikes_count: dislikes.total,
        reports_count: reports.total,
        exposure_score: exposureScore,
        is_visible: exposureScore > 40 || post.is_pinned // 曝光度>40或置顶才可见
      };
    })
  );

  // 过滤掉不可见的帖子（曝光度<=40且未置顶）
  const visiblePosts = postsWithStats.filter(post => post.is_visible);

  return {
    success: true,
    data: visiblePosts,
    pagination: {
      page,
      limit,
      hasMore: result.data.length === limit
    }
  };
}

// 获取宠物详情
async function getPostDetail(params, currentUserId) {
  console.log('getPostDetail params:', params);
  const { postId, userId } = params;
  // 使用传入的currentUserId作为当前用户ID
  const requestUserId = currentUserId || userId;
  const post = await db.collection('posts').doc(postId).get();
  
  if (!post.data) {
    throw new Error('宠物信息不存在');
  }
  
  // 获取统计信息
  const [likes, dislikes, wants, ratings, bookmarks] = await Promise.all([
    db.collection('likes').where({ post_id: postId }).count(),
    db.collection('dislikes').where({ post_id: postId }).count(),
    db.collection('wants').where({ post_id: postId }).count(),
    db.collection('ratings').where({ post_id: postId }).get(),
    db.collection('bookmarks').where({ post_id: postId }).count()
  ]);
  
  // 获取用户交互状态
  let userInteractions = {};
  if (requestUserId && requestUserId !== 'anonymous') {
    const [userLike, userDislike, userWant, userRating, userBookmark, userContact] = await Promise.all([
      db.collection('likes').where({ post_id: postId, user_id: requestUserId }).get(),
      db.collection('dislikes').where({ post_id: postId, user_id: requestUserId }).get(),
      db.collection('wants').where({ post_id: postId, user_id: requestUserId }).get(),
      db.collection('ratings').where({ post_id: postId, user_id: requestUserId }).get(),
      db.collection('bookmarks').where({ post_id: postId, user_id: requestUserId }).get(),
      db.collection('contacts').where({ post_id: postId, requester_id: requestUserId }).get()
    ]);

    userInteractions = {
      hasLiked: userLike.data.length > 0,
      hasDisliked: userDislike.data.length > 0,
      hasWanted: userWant.data.length > 0,
      hasRated: userRating.data.length > 0,
      hasBookmarked: userBookmark.data.length > 0,
      hasContacted: userContact.data.length > 0,
      userRating: userRating.data[0]?.rating || 0
    };
  }
  
  // 计算评分统计
  const ratingStats = {
    1: 0, 2: 0, 3: 0, 4: 0, 5: 0
  };
  ratings.data.forEach(r => {
    ratingStats[r.rating]++;
  });
  
  const avgRating = ratings.data.length > 0
    ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length
    : 0;

  // 处理图片URL
  const processedImages = await processImageUrls(post.data.images || [], postId);

  // 获取作者信息
  let authorInfo = {
    _id: 'anonymous',
    nickname: '匿名用户',
    avatar_url: '/default-avatar.png'
  };

  const authorId = post.data.author_id || post.data.userId;
  if (authorId) {
    try {
      const authorResult = await db.collection('users').doc(authorId).get();
      if (authorResult.data) {
        authorInfo = {
          _id: authorId,
          nickname: authorResult.data.nickname || '匿名用户',
          avatar_url: authorResult.data.avatar_url || '/default-avatar.png'
        };
      }
    } catch (error) {
      console.error('获取作者信息失败:', error);
    }
  }

  return {
    success: true,
    data: {
      ...post.data,
      images: processedImages, // 使用处理后的图片URL
      likes_count: likes.total,
      dislikes_count: dislikes.total,
      wants_count: wants.total,
      bookmarks_count: bookmarks.total,
      ratings_count: ratings.data.length,
      avg_rating: Math.round(avgRating * 10) / 10,
      rating_stats: ratingStats,
      user_interactions: userInteractions,
      // 添加格式化时间和作者信息
      timeAgo: getTimeAgo(post.data.created_at),
      author_name: authorInfo.nickname,
      author_avatar: authorInfo.avatar_url,
      // 构建author对象以便前端使用
      author: authorInfo
    }
  };
}

// 品种标签生成函数
function generateBreedTags(breed, category) {
  if (!breed) return [];

  const tags = [];
  const breedLower = breed.toLowerCase();

  // 添加原始品种名
  tags.push(breed);

  // 添加小写版本
  tags.push(breedLower);

  // 品种别名映射
  const breedAliases = {
    // 狗狗别名
    '拉布拉多犬': ['拉布拉多', '拉拉', 'labrador'],
    '金毛寻回犬': ['金毛', '金毛犬', 'golden'],
    '泰迪犬': ['泰迪', '贵宾犬', 'poodle'],
    '比熊犬': ['比熊', 'bichon'],
    '博美犬': ['博美', 'pomeranian'],
    '柯基犬': ['柯基', 'corgi'],
    '哈士奇': ['哈士奇', '二哈', 'husky'],
    '萨摩耶': ['萨摩', 'samoyed'],
    '边境牧羊犬': ['边牧', 'border collie'],
    '德国牧羊犬': ['德牧', 'german shepherd'],
    '法国斗牛犬': ['法斗', 'french bulldog'],
    '英国斗牛犬': ['英斗', 'english bulldog'],

    // 田园犬别名
    '中华田园犬': ['田园犬', '土狗', '本地狗', '土狗子'],
    '大黄狗': ['黄狗', '黄色狗'],
    '大白狗': ['白狗', '白色狗'],
    '大黑狗': ['黑狗', '黑色狗'],
    '花狗': ['花花狗', '花色狗'],
    '草狗': ['草狗子'],
    '唐狗': ['唐犬'],
    '柴狗': ['柴犬风格'],
    '大笨狗': ['笨狗'],
    '太行犬': ['太行狗'],

    // 猫咪别名
    '英国短毛猫': ['英短', 'british shorthair'],
    '美国短毛猫': ['美短', 'american shorthair'],
    '苏格兰折耳猫': ['折耳猫', 'scottish fold'],
    '布偶猫': ['布偶', 'ragdoll'],
    '缅因猫': ['缅因', 'maine coon'],
    '挪威森林猫': ['挪威森林', 'norwegian forest'],
    '俄罗斯蓝猫': ['俄蓝', 'russian blue'],
    '银渐层': ['银渐层猫', '银点'],
    '金渐层': ['金渐层猫', '金点'],
    '蓝猫': ['英短蓝猫', '蓝色猫'],
    '橘猫': ['橘色猫', '橙猫'],
    '狸花猫': ['狸花', '中华田园猫'],
    '奶牛猫': ['黑白猫', '奶牛色']
  };

  // 添加别名
  Object.keys(breedAliases).forEach(key => {
    if (breed.includes(key) || key.includes(breed)) {
      tags.push(...breedAliases[key]);
    }
  });

  // 添加关键词提取
  const keywords = breed.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g) || [];
  tags.push(...keywords);

  // 去重并过滤空值
  return [...new Set(tags.filter(tag => tag && tag.length > 0))];
}

// 创建宠物帖子
async function createPost(data, userId) {
  console.log('创建帖子，用户ID:', userId, '数据:', data);

  // 0. 检查用户是否有发帖权限
  const canPublishPost = await checkUserPermission(userId, 'canPublishPost');
  if (!canPublishPost) {
    return {
      success: false,
      message: '您已被禁用发帖权限，如有疑问请联系客服申诉'
    };
  }

  // 0.5. 检查每日发帖限制
  try {
    const dailyLimitCheck = await checkDailyPostLimit(userId);
    if (!dailyLimitCheck.canPost) {
      return {
        success: false,
        message: `您今日发帖已达上限（${dailyLimitCheck.limit}条），请明天再试`
      };
    }
  } catch (error) {
    console.error('检查每日发帖限制失败:', error);
    // 不阻止帖子发布，但记录错误
  }

  // 1. 检查用户帖子数量限制
  try {
    const limitCheck = await checkPostLimit({ user_id: userId }, userId);

    if (limitCheck.needArchive) {
      // 需要下架旧帖子
      console.log('用户帖子数量超限，开始自动下架旧帖子...');
      const archiveResult = await archiveOldPosts({
        user_id: userId,
        posts_to_archive: limitCheck.postsToArchive
      }, userId);

      console.log(`自动下架完成，下架了 ${archiveResult.archivedCount} 条帖子`);
    }
  } catch (error) {
    console.error('检查帖子数量限制失败:', error);
    // 不阻止帖子发布，但记录错误
  }

  // 2. 生成品种标签
  const breedTags = data.breed ? generateBreedTags(data.breed, data.category) : [];

  const postData = {
    ...data,
    breed_tags: breedTags, // 添加品种标签用于搜索
    author_id: userId,
    user_id: userId, // 添加user_id字段
    status: 'pending_audit', // 初始状态为待审核
    created_at: new Date(),
    updated_at: new Date()
  };

  console.log('准备保存的帖子数据:', postData);

  const result = await db.collection('posts').add({
    data: postData
  });

  console.log('帖子创建成功，ID:', result._id);

  // 启动自动审核流程
  try {
    console.log('启动帖子审核流程...');
    await startPostAudit(result._id, postData, userId);
  } catch (error) {
    console.error('启动审核流程失败:', error);
    // 审核失败不影响帖子创建，但记录错误
  }

  return {
    success: true,
    data: {
      id: result._id,
      ...postData
    }
  };
}

// 启动帖子审核流程
async function startPostAudit(postId, postData, userId) {
  console.log(`开始审核帖子: ${postId}`);

  try {
    // 记录审核开始时间
    const auditStartTime = new Date();

    // 更新帖子审核状态
    await db.collection('posts').doc(postId).update({
      data: {
        audit_start_time: auditStartTime,
        audit_status: 'auditing'
      }
    });

    // 模拟审核过程（8-12秒随机延迟）
    const auditDuration = Math.floor(Math.random() * 5000) + 8000; // 8-13秒
    console.log(`审核将持续 ${auditDuration}ms`);

    // 异步执行审核（不阻塞帖子创建响应）
    setTimeout(async () => {
      try {
        await performPostAudit(postId, postData, userId, auditDuration);
      } catch (error) {
        console.error('执行审核失败:', error);
        // 审核失败时设置为需要人工审核
        await db.collection('posts').doc(postId).update({
          data: {
            audit_status: 'manual_review',
            audit_end_time: new Date(),
            audit_duration: auditDuration,
            audit_error: error.message
          }
        });
      }
    }, auditDuration);

    console.log('审核流程已启动');
  } catch (error) {
    console.error('启动审核流程失败:', error);
    throw error;
  }
}

// 执行帖子审核
async function performPostAudit(postId, postData, userId, auditDuration) {
  console.log(`执行帖子审核: ${postId}`);

  try {
    // 获取用户信息用于审核评分
    const userResult = await db.collection('users').doc(userId).get();
    const userProfile = userResult.data || {};

    // 执行多维度审核
    const auditResult = await comprehensivePostAudit(postData, userProfile);

    const auditEndTime = new Date();

    // 更新帖子审核结果
    const updateData = {
      audit_status: auditResult.status,
      audit_end_time: auditEndTime,
      audit_duration: auditDuration,
      audit_confidence: auditResult.confidence,
      audit_reason: auditResult.reason,
      updated_at: auditEndTime
    };

    // 如果审核通过，更新状态为已发布
    if (auditResult.status === 'approved') {
      updateData.status = 'published';
    } else if (auditResult.status === 'rejected') {
      updateData.status = 'rejected';
    }

    await db.collection('posts').doc(postId).update({
      data: updateData
    });

    console.log(`帖子审核完成: ${postId}, 结果: ${auditResult.status}`);

    // 如果审核通过，清理相关缓存
    if (auditResult.status === 'approved') {
      // 这里可以添加缓存清理逻辑
      console.log(`帖子 ${postId} 审核通过，已发布`);
    }

  } catch (error) {
    console.error('执行帖子审核失败:', error);
    throw error;
  }
}

// 综合帖子审核
async function comprehensivePostAudit(postData, userProfile) {
  console.log('开始综合审核...');

  try {
    const auditChecks = [];

    // 1. 内容审核
    const contentScore = auditPostContent(postData);
    auditChecks.push({ name: 'content', score: contentScore, weight: 0.4 });

    // 2. 图片审核（简化版）
    const imageScore = auditPostImages(postData.images || []);
    auditChecks.push({ name: 'images', score: imageScore, weight: 0.3 });

    // 3. 用户信誉审核
    const userScore = auditUserReputation(userProfile);
    auditChecks.push({ name: 'user', score: userScore, weight: 0.2 });

    // 4. 发布频率审核
    const frequencyScore = await auditPostFrequency(userProfile._id || postData.author_id);
    auditChecks.push({ name: 'frequency', score: frequencyScore, weight: 0.1 });

    // 计算综合分数
    let totalScore = 0;
    let totalWeight = 0;

    auditChecks.forEach(check => {
      totalScore += check.score * check.weight;
      totalWeight += check.weight;
    });

    const finalScore = totalScore / totalWeight;
    const confidence = Math.min(finalScore, 1.0);

    // 决定审核结果
    let status, reason;
    if (finalScore >= 0.8) {
      status = 'approved';
      reason = '内容符合平台规范';
    } else if (finalScore >= 0.6) {
      status = 'approved';
      reason = '内容基本符合规范';
    } else if (finalScore >= 0.4) {
      status = 'manual_review';
      reason = '内容需要人工审核';
    } else {
      status = 'rejected';
      reason = '内容不符合平台规范';
    }

    console.log(`审核完成，分数: ${finalScore.toFixed(2)}, 结果: ${status}`);

    return {
      status,
      confidence,
      reason,
      details: {
        finalScore,
        checks: auditChecks
      }
    };

  } catch (error) {
    console.error('综合审核失败:', error);
    // 审核失败时采用保守策略
    return {
      status: 'manual_review',
      confidence: 0.5,
      reason: '审核系统异常，需要人工审核',
      details: { error: error.message }
    };
  }
}

// 内容审核
function auditPostContent(postData) {
  let score = 1.0;

  const title = postData.title || '';
  const description = postData.description || '';
  const breed = postData.breed || '';

  // 检查敏感词（简化版）
  const sensitiveWords = ['违法', '欺诈', '假货', '病死', '虐待'];
  const allText = `${title} ${description} ${breed}`.toLowerCase();

  sensitiveWords.forEach(word => {
    if (allText.includes(word)) {
      score -= 0.3;
    }
  });

  // 检查内容长度
  if (description.length < 5) {
    score -= 0.2; // 描述太短
  }

  if (title.length < 2) {
    score -= 0.1; // 标题太短
  }

  // 检查是否包含联系方式在描述中（应该在专门字段）
  const contactPattern = /\d{11}|qq|微信|wechat/i;
  if (contactPattern.test(description)) {
    score -= 0.1;
  }

  return Math.max(score, 0);
}

// 图片审核（简化版）
function auditPostImages(images) {
  if (!images || images.length === 0) {
    return 0.5; // 没有图片，中等分数
  }

  let score = 1.0;

  // 检查图片数量
  if (images.length > 9) {
    score -= 0.1; // 图片太多
  }

  // 检查图片URL格式
  images.forEach(imageUrl => {
    if (!imageUrl || !imageUrl.startsWith('http')) {
      score -= 0.2;
    }
  });

  return Math.max(score, 0);
}

// 用户信誉审核
function auditUserReputation(userProfile) {
  let score = 0.8; // 基础分数

  // 检查用户注册时间
  if (userProfile.created_at) {
    const daysSinceRegistration = (new Date() - new Date(userProfile.created_at)) / (1000 * 60 * 60 * 24);
    if (daysSinceRegistration > 30) {
      score += 0.1; // 老用户加分
    }
  }

  // 检查用户是否完善了资料
  if (userProfile.nickname && userProfile.nickname !== '用户') {
    score += 0.05;
  }

  if (userProfile.avatar_url) {
    score += 0.05;
  }

  return Math.min(score, 1.0);
}

// 发布频率审核
async function auditPostFrequency(userId) {
  try {
    // 检查最近24小时的发布数量
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const recentPosts = await db.collection('posts')
      .where({
        author_id: userId,
        created_at: db.command.gte(oneDayAgo)
      })
      .get();

    const postCount = recentPosts.data.length;

    if (postCount >= 10) {
      return 0.2; // 发布太频繁
    } else if (postCount >= 5) {
      return 0.6; // 发布较频繁
    } else {
      return 1.0; // 正常频率
    }

  } catch (error) {
    console.error('检查发布频率失败:', error);
    return 0.8; // 默认分数
  }
}

// 手动审核帖子
async function manualAuditPost(postId, userId) {
  console.log(`手动审核帖子: ${postId}`);

  try {
    // 获取帖子信息
    const postResult = await db.collection('posts').doc(postId).get();
    if (!postResult.data) {
      return { success: false, message: '帖子不存在' };
    }

    const postData = postResult.data;

    // 获取用户信息
    const userResult = await db.collection('users').doc(postData.author_id).get();
    const userProfile = userResult.data || {};

    // 执行审核
    await performPostAudit(postId, postData, postData.author_id, 1000); // 快速审核

    return { success: true, message: '审核已启动' };

  } catch (error) {
    console.error('手动审核失败:', error);
    return { success: false, message: '审核启动失败: ' + error.message };
  }
}

// 点赞/取消点赞
async function toggleLike({ postId }, userId) {
  const existing = await db.collection('likes')
    .where({ post_id: postId, user_id: userId })
    .get();
  
  if (existing.data.length > 0) {
    // 取消点赞
    await db.collection('likes').doc(existing.data[0]._id).remove();
    return {
      success: true,
      action: 'unliked',
      message: '取消点赞成功'
    };
  } else {
    // 添加点赞
    await db.collection('likes').add({
      data: {
        post_id: postId,
        user_id: userId,
        created_at: new Date()
      }
    });

    // 获取帖子信息以创建通知
    const post = await db.collection('posts').doc(postId).get();
    if (post.data && post.data.author_id !== userId) {
      // 创建点赞通知
      await createNotification({
        type: 'like',
        recipient_id: post.data.author_id,
        sender_id: userId,
        post_id: postId,
        message: '有人点赞了您的帖子',
        data: { post_title: post.data.title }
      });
    }

    return {
      success: true,
      action: 'liked',
      message: '点赞成功'
    };
  }
}

// 不喜欢/取消不喜欢
async function toggleDislike({ postId }, userId) {
  const existing = await db.collection('dislikes')
    .where({ post_id: postId, user_id: userId })
    .get();

  if (existing.data.length > 0) {
    // 取消不喜欢
    await db.collection('dislikes').doc(existing.data[0]._id).remove();
    return {
      success: true,
      action: 'undisliked',
      message: '取消不喜欢成功'
    };
  } else {
    // 添加不喜欢
    await db.collection('dislikes').add({
      data: {
        post_id: postId,
        user_id: userId,
        created_at: new Date()
      }
    });

    return {
      success: true,
      action: 'disliked',
      message: '不喜欢成功'
    };
  }
}

// 永久点赞（不可取消）
async function addLike({ postId }, userId) {
  try {
    // 检查用户是否有点赞权限
    const canLike = await checkUserPermission(userId, 'canLike');
    if (!canLike) {
      return {
        success: false,
        message: '您已被禁用点赞权限，如有疑问请联系客服申诉'
      };
    }

    // 检查是否已经不喜欢
    const existingDislike = await db.collection('dislikes')
      .where({ post_id: postId, user_id: userId })
      .get();

    if (existingDislike.data.length > 0) {
      return {
        success: false,
        message: '您已经对此帖子做出过评价，无法更改'
      };
    }

    // 直接尝试添加点赞，利用唯一索引防止重复
    await db.collection('likes').add({
      data: {
        post_id: postId,
        user_id: userId,
        created_at: new Date()
      }
    });

    // 获取帖子信息以创建通知
    const post = await db.collection('posts').doc(postId).get();
    if (post.data && post.data.author_id !== userId) {
      // 创建点赞通知
      await createNotification({
        type: 'like',
        recipient_id: post.data.author_id,
        sender_id: userId,
        post_id: postId,
        message: '有人点赞了您的帖子',
        data: { post_title: post.data.title }
      });
    }

    return {
      success: true,
      action: 'liked',
      message: '点赞成功'
    };
  } catch (error) {
    // 处理重复键错误
    if (error.code === -502001 && error.message.includes('E11000 duplicate key error')) {
      return {
        success: false,
        message: '您已经点赞过这个帖子了'
      };
    }

    // 其他错误
    console.error('点赞失败:', error);
    return {
      success: false,
      message: '点赞失败，请重试'
    };
  }
}

// 永久不喜欢（不可取消）
async function addDislike({ postId }, userId) {
  try {
    // 检查用户是否有不喜欢权限
    const canDislike = await checkUserPermission(userId, 'canDislike');
    if (!canDislike) {
      return {
        success: false,
        message: '您已被禁用不喜欢权限，如有疑问请联系客服申诉'
      };
    }

    // 检查是否已经点赞
    const existingLike = await db.collection('likes')
      .where({ post_id: postId, user_id: userId })
      .get();

    if (existingLike.data.length > 0) {
      return {
        success: false,
        message: '您已经对此帖子做出过评价，无法更改'
      };
    }

    // 直接尝试添加不喜欢，利用唯一索引防止重复
    await db.collection('dislikes').add({
      data: {
        post_id: postId,
        user_id: userId,
        created_at: new Date()
      }
    });

    return {
      success: true,
      action: 'disliked',
      message: '不喜欢成功'
    };
  } catch (error) {
    // 处理重复键错误
    if (error.code === -502001 && error.message.includes('E11000 duplicate key error')) {
      return {
        success: false,
        message: '您已经不喜欢过这个帖子了'
      };
    }

    // 其他错误
    console.error('不喜欢失败:', error);
    return {
      success: false,
      message: '操作失败，请重试'
    };
  }
}

// 收藏/取消收藏
async function toggleBookmark({ postId }, userId) {
  const existing = await db.collection('bookmarks')
    .where({ post_id: postId, user_id: userId })
    .get();

  if (existing.data.length > 0) {
    // 取消收藏
    await db.collection('bookmarks').doc(existing.data[0]._id).remove();
    return {
      success: true,
      action: 'unbookmarked',
      message: '取消收藏成功'
    };
  } else {
    // 添加收藏
    await db.collection('bookmarks').add({
      data: {
        post_id: postId,
        user_id: userId,
        created_at: new Date()
      }
    });
    return {
      success: true,
      action: 'bookmarked',
      message: '收藏成功'
    };
  }
}

// 想买宠物（一次性操作）
async function wantPet({ postId }, userId) {
  const existing = await db.collection('wants')
    .where({ post_id: postId, user_id: userId })
    .get();
  
  if (existing.data.length > 0) {
    return {
      success: false,
      message: '您已经表达过购买意向了'
    };
  }
  
  await db.collection('wants').add({
    data: {
      post_id: postId,
      user_id: userId,
      created_at: new Date()
    }
  });
  
  return {
    success: true,
    message: '购买意向表达成功'
  };
}

// 交换联系方式
async function exchangeContact({ postId, contactInfo }, userId) {
  console.log('exchangeContact params:', { postId, contactInfo, userId });

  // 检查用户是否有联系权限
  const canContact = await checkUserPermission(userId, 'canContact');
  if (!canContact) {
    return {
      success: false,
      message: '您已被禁用联系权限，如有疑问请联系客服申诉'
    };
  }

  // 获取帖子信息
  const post = await db.collection('posts').doc(postId).get();
  if (!post.data) {
    throw new Error('帖子不存在');
  }

  // 不能联系自己的帖子
  if (post.data.author_id === userId) {
    throw new Error('不能联系自己发布的帖子');
  }

  // 检查是否被对方拉黑
  const blacklistCheck = await db.collection('blacklist')
    .where({ user_id: post.data.author_id, blocked_user_id: userId })
    .get();

  if (blacklistCheck.data.length > 0) {
    return {
      success: false,
      message: '对方已经将您拉黑无法联系到对方'
    };
  }

  // 检查是否已经联系过
  const existing = await db.collection('contacts')
    .where({ post_id: postId, requester_id: userId })
    .get();

  if (existing.data.length > 0) {
    return {
      success: false,
      message: '您已经联系过对方了'
    };
  }

  // 获取用户信息
  const requester = await db.collection('users').doc(userId).get();
  const owner = await db.collection('users').doc(post.data.author_id).get();

  // 创建联系记录
  await db.collection('contacts').add({
    data: {
      post_id: postId,
      post_title: post.data.title,
      post_type: post.data.type, // 添加帖子类型
      requester_id: userId, // 请求联系的用户
      owner_id: post.data.author_id, // 帖子发布者
      requester_contact: contactInfo, // 请求者的联系方式
      owner_contact: post.data.contact_info, // 发布者的联系方式
      status: 'active', // 联系状态
      created_at: new Date()
    }
  });

  // 使用新的联系方式交换通知函数
  await createContactNotification({
    requester_id: userId,
    owner_id: post.data.author_id,
    post_id: postId,
    post_title: post.data.title,
    post_type: post.data.type,
    requester_contact: contactInfo,
    owner_contact: post.data.contact_info,
    requester_nickname: requester.data?.nickname || '用户',
    owner_nickname: owner.data?.nickname || '用户'
  });

  return {
    success: true,
    message: '联系方式已交换，请查看通知中心',
    data: {
      owner_contact: post.data.contact_info
    }
  };
}

// 评分宠物
async function ratePet({ postId, rating }, userId) {
  if (rating < 1 || rating > 5) {
    throw new Error('评分必须在1-5之间');
  }
  
  const existing = await db.collection('ratings')
    .where({ post_id: postId, user_id: userId })
    .get();
  
  if (existing.data.length > 0) {
    return {
      success: false,
      message: '您已经评分过了'
    };
  }
  
  await db.collection('ratings').add({
    data: {
      post_id: postId,
      user_id: userId,
      rating: rating,
      created_at: new Date()
    }
  });
  
  return {
    success: true,
    message: '评分成功'
  };
}

// 举报帖子
async function reportPost({ postId, reason }, userId) {
  // 检查用户是否有举报帖子权限
  const canReportPost = await checkUserPermission(userId, 'canReportPost');
  if (!canReportPost) {
    return {
      success: false,
      message: '您已被禁用举报帖子权限，如有疑问请联系客服申诉'
    };
  }

  const existing = await db.collection('post_reports')
    .where({ post_id: postId, reporter_id: userId })
    .get();

  if (existing.data.length > 0) {
    return {
      success: false,
      message: '您已经举报过这个帖子了'
    };
  }

  await db.collection('post_reports').add({
    data: {
      post_id: postId,
      reporter_id: userId,
      reason: reason,
      status: 'pending',
      created_at: new Date()
    }
  });

  // 检查是否需要自动隐藏帖子
  await checkAndHidePost(postId, reason);

  return {
    success: true,
    message: '举报提交成功'
  };
}

// 检查并自动隐藏帖子
async function checkAndHidePost(postId, reason) {
  try {
    // 获取举报阈值设置
    const thresholdConfig = await getReportThresholds();
    const threshold = thresholdConfig.post_report_threshold || 5;

    // 统计该帖子的举报次数
    const reports = await db.collection('post_reports')
      .where({
        post_id: postId,
        status: 'pending'
      })
      .get();

    console.log(`帖子 ${postId} 被举报次数: ${reports.data.length}, 阈值: ${threshold}`);

    if (reports.data.length >= threshold && thresholdConfig.auto_hide_posts) {
      // 获取帖子信息
      const post = await db.collection('posts').doc(postId).get();
      if (!post.data) return;

      // 隐藏帖子
      await db.collection('posts').doc(postId).update({
        data: {
          status: 'hidden',
          hidden_reason: reason,
          hidden_at: new Date(),
          hidden_by: 'system'
        }
      });

      // 发送通知给帖子作者
      if (thresholdConfig.notification_enabled) {
        await createReportNotification({
          user_id: post.data.author_id,
          report_type: 'post_hidden',
          reason: reason,
          post_title: post.data.title,
          post_id: postId
        });
      }

      console.log(`帖子 ${postId} 已自动隐藏`);
    }
  } catch (error) {
    console.error('检查帖子举报阈值失败:', error);
  }
}

// 举报用户
async function reportUser({ targetUserId, reason }, userId) {
  // 检查用户是否有举报用户权限
  const canReportUser = await checkUserPermission(userId, 'canReportUser');
  if (!canReportUser) {
    return {
      success: false,
      message: '您已被禁用举报用户权限，如有疑问请联系客服申诉'
    };
  }

  const existing = await db.collection('user_reports')
    .where({ reported_user_id: targetUserId, reporter_id: userId })
    .get();

  if (existing.data.length > 0) {
    return {
      success: false,
      message: '您已经举报过这个用户了'
    };
  }

  await db.collection('user_reports').add({
    data: {
      reported_user_id: targetUserId,
      reporter_id: userId,
      reason: reason,
      status: 'pending',
      created_at: new Date()
    }
  });

  // 检查是否需要自动警告用户
  await checkAndWarnUser(targetUserId, reason);

  return {
    success: true,
    message: '举报提交成功'
  };
}

// 检查并警告用户
async function checkAndWarnUser(targetUserId, reason) {
  try {
    // 获取举报阈值设置
    const thresholdConfig = await getReportThresholds();
    const threshold = thresholdConfig.user_report_threshold || 10;

    // 统计该用户被举报的次数（所有原因）
    const reports = await db.collection('user_reports')
      .where({
        reported_user_id: targetUserId,
        status: 'pending'
      })
      .get();

    console.log(`用户 ${targetUserId} 被举报次数: ${reports.data.length}, 阈值: ${threshold}`);

    if (reports.data.length >= threshold && thresholdConfig.auto_warn_users) {
      // 发送警告通知给用户
      if (thresholdConfig.notification_enabled) {
        await createReportNotification({
          user_id: targetUserId,
          report_type: 'user_warned',
          reason: reason
        });
      }

      console.log(`用户 ${targetUserId} 已收到警告通知`);
    }
  } catch (error) {
    console.error('检查用户举报阈值失败:', error);
  }
}

// 获取举报阈值配置
async function getReportThresholds() {
  try {
    const config = await db.collection('system_config')
      .where({ key: 'report_thresholds' })
      .get();

    if (config.data.length > 0) {
      return config.data[0].value;
    }

    // 返回默认配置
    return {
      post_report_threshold: 5,
      user_report_threshold: 10,
      auto_hide_posts: true,
      auto_warn_users: true,
      notification_enabled: true
    };
  } catch (error) {
    console.error('获取举报阈值配置失败:', error);
    return {
      post_report_threshold: 5,
      user_report_threshold: 10,
      auto_hide_posts: true,
      auto_warn_users: true,
      notification_enabled: true
    };
  }
}

// 应用自动处罚
async function applyAutoPunishment(userId, reportCount) {
  try {
    // 禁止发布宝贝权限和联系权限
    await db.collection('user_permissions').doc(userId).set({
      data: {
        user_id: userId,
        canPublishPost: false,
        canContact: false,
        canLike: true,
        canDislike: true,
        canReportPost: true,
        canReportUser: true,
        bannedUntil: null, // 永久禁止，直到申诉成功
        banReason: `被多次举报为疑似骗子（${reportCount}次），系统自动处罚`,
        punishedAt: new Date(),
        punishmentType: 'auto_fraud_report'
      }
    });

    // 发送系统通知
    await createNotification({
      type: 'system_punishment',
      recipient_id: userId,
      sender_id: 'system',
      message: '你被多次举报为骗子，如要申诉请点击右边申诉按钮',
      data: {
        reportCount: reportCount,
        canAppeal: true
      }
    });

    console.log(`已对用户 ${userId} 执行自动处罚，举报次数: ${reportCount}`);
  } catch (error) {
    console.error('应用自动处罚失败:', error);
  }
}





// 权限检查缓存（减少数据库查询）
const permissionCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

// 检查用户权限的通用函数
async function checkUserPermission(userId, permission) {
  try {
    // 检查缓存
    const cacheKey = `${userId}_${permission}`;
    const cached = permissionCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
      return cached.value;
    }

    // 从数据库获取权限
    const permissions = await db.collection('user_permissions').doc(userId).get();

    let userPermissions;
    if (permissions.data) {
      // 使用 .doc().get() 时，数据直接在 permissions.data 中，不是数组
      userPermissions = permissions.data;
    } else {
      // 默认权限
      userPermissions = {
        canLike: true,
        canDislike: true,
        canReportPost: true,
        canReportUser: true,
        canContact: true,
        canPublishPost: true,
        bannedUntil: null,
        banReason: null
      };
    }

    // 检查具体权限
    const hasPermission = userPermissions[permission] === true;

    // 缓存结果
    permissionCache.set(cacheKey, {
      value: hasPermission,
      timestamp: Date.now()
    });

    return hasPermission;
  } catch (error) {
    console.error('检查用户权限失败:', error);
    // 出错时默认允许（避免影响正常用户）
    return true;
  }
}

// 清除用户权限缓存
function clearUserPermissionCache(userId) {
  const keysToDelete = [];
  for (const key of permissionCache.keys()) {
    if (key.startsWith(userId + '_')) {
      keysToDelete.push(key);
    }
  }
  keysToDelete.forEach(key => permissionCache.delete(key));
}

// 获取用户权限状态
async function getUserPermissions({ }, userId) {
  try {
    // 默认权限
    const defaultPermissions = {
      canLike: true,
      canDislike: true,
      canReportPost: true,
      canReportUser: true,
      canContact: true,
      canPublishPost: true,
      bannedUntil: null,
      banReason: null
    };

    try {
      const permissions = await db.collection('user_permissions').doc(userId).get();

      if (permissions.data && permissions.data.length > 0) {
        return {
          success: true,
          data: permissions.data[0]
        };
      } else {
        // 权限文档不存在，返回默认权限
        return {
          success: true,
          data: defaultPermissions
        };
      }
    } catch (error) {
      // 权限文档不存在或查询失败，返回默认权限
      console.log('用户权限文档不存在，使用默认权限:', userId);
      return {
        success: true,
        data: defaultPermissions
      };
    }
  } catch (error) {
    console.error('获取用户权限失败:', error);
    return {
      success: false,
      message: '获取权限状态失败'
    };
  }
}

// 管理员功能 - 获取指定用户权限
async function getTargetUserPermissions({ targetUserId }, adminId) {
  try {
    // 检查管理员权限 - 支持超级管理员特殊标识
    let isAdmin = false;

    if (adminId) {
      isAdmin = await checkAdminPermission(adminId);
    }

    // 如果常规权限检查失败，尝试检查是否是超级管理员
    if (!isAdmin) {
      // 检查是否有活跃的超级管理员会话
      const recentSuperAdminSession = await db.collection('admin_sessions')
        .where({ status: 'active' })
        .orderBy('last_activity', 'desc')
        .limit(1)
        .get();

      if (recentSuperAdminSession.data.length > 0) {
        const session = recentSuperAdminSession.data[0];
        const admin = await getAdminById(session.admin_id);
        if (admin && admin.role === 'super_admin' && admin.status === 'active') {
          isAdmin = true;
          console.log('通过超级管理员会话验证权限成功');
        }
      }
    }

    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    // 默认权限
    const defaultPermissions = {
      canLike: true,
      canDislike: true,
      canReportPost: true,
      canReportUser: true,
      canContact: true,
      canPublishPost: true,
      bannedUntil: null,
      banReason: null
    };

    try {
      const permissions = await db.collection('user_permissions').doc(targetUserId).get();

      if (permissions.data && permissions.data.length > 0) {
        // 用户有权限记录
        return {
          success: true,
          data: permissions.data[0]
        };
      } else {
        // 用户没有权限记录，返回默认权限
        return {
          success: true,
          data: defaultPermissions
        };
      }
    } catch (error) {
      // 权限文档不存在，返回默认权限
      console.log('用户权限文档不存在，使用默认权限:', targetUserId);
      return {
        success: true,
        data: defaultPermissions
      };
    }
  } catch (error) {
    console.error('获取用户权限失败:', error);
    return {
      success: false,
      message: error.message || '获取权限失败'
    };
  }
}

// 管理员功能 - 更新用户权限
async function updateUserPermissions({ targetUserId, permissions }, adminId) {
  try {
    // 检查是否有活跃的超级管理员会话
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    let isAdmin = false;
    if (recentSuperAdminSession.data.length > 0) {
      const session = recentSuperAdminSession.data[0];
      const admin = await getAdminById(session.admin_id);

      if (admin && admin.role === 'super_admin' && admin.status === 'active') {
        isAdmin = true;
      }
    }

    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    // 验证权限数据
    const validPermissions = {
      canLike: Boolean(permissions.canLike),
      canDislike: Boolean(permissions.canDislike),
      canReportPost: Boolean(permissions.canReportPost),
      canReportUser: Boolean(permissions.canReportUser),
      canContact: Boolean(permissions.canContact),
      canPublishPost: Boolean(permissions.canPublishPost),
      bannedUntil: permissions.bannedUntil || null,
      banReason: permissions.banReason || null,
      updated_at: new Date(),
      updated_by: adminId
    };

    // 更新用户权限（如果不存在会自动创建）
    await db.collection('user_permissions').doc(targetUserId).set({
      data: validPermissions
    });

    // 清除该用户的权限缓存
    clearUserPermissionCache(targetUserId);

    return {
      success: true,
      message: '用户权限更新成功',
      data: validPermissions
    };
  } catch (error) {
    console.error('更新用户权限失败:', error);
    return {
      success: false,
      message: error.message || '更新权限失败'
    };
  }
}

// ==================== 免费图片审核系统 ====================

/**
 * 触发免费图片审核
 */
async function triggerFreeImageAudit(imageUrl, fileId, fileName, fileSize) {
  console.log(`开始免费图片审核: ${imageUrl}`);

  try {
    // 构建审核上下文
    const auditContext = {
      imageUrl,
      fileName,
      fileSize,
      userId: OPENID || 'anonymous'
    };

    // 执行免费审核
    const auditResult = await performFreeAudit(auditContext);

    // 记录审核结果
    await recordAuditResult(fileId, auditResult, auditContext);

    console.log(`图片审核完成: ${imageUrl}, 结果: ${auditResult.status}`);
    return auditResult;

  } catch (error) {
    console.error('免费图片审核失败:', error);

    // 审核失败时的保守处理
    const fallbackResult = {
      status: 'manual_review',
      confidence: 0.5,
      reason: 'audit_system_error',
      riskLevel: 'medium'
    };

    await recordAuditResult(fileId, fallbackResult, { imageUrl, fileName, fileSize });
    return fallbackResult;
  }
}

/**
 * 执行免费审核
 */
async function performFreeAudit(context) {
  const rules = [
    { name: 'file_format_check', weight: 0.1, check: checkFileFormat },
    { name: 'file_size_check', weight: 0.1, check: checkFileSize },
    { name: 'filename_analysis', weight: 0.3, check: analyzeFileName },
    { name: 'url_analysis', weight: 0.2, check: analyzeUrl },
    { name: 'user_trust_score', weight: 0.2, check: checkUserTrustScore },
    { name: 'upload_frequency', weight: 0.1, check: checkUploadFrequency }
  ];

  const ruleResults = [];

  // 执行所有规则
  for (const rule of rules) {
    try {
      const result = await rule.check(context);
      ruleResults.push({ rule, result });
    } catch (error) {
      console.warn(`规则 ${rule.name} 执行失败:`, error);
      ruleResults.push({
        rule,
        result: { passed: true, confidence: 0.5, reason: 'rule_execution_failed' }
      });
    }
  }

  // 计算最终分数
  return calculateFinalAuditScore(ruleResults);
}

/**
 * 检查文件格式
 */
async function checkFileFormat(context) {
  const { fileName } = context;
  const allowedFormats = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];

  const hasValidFormat = allowedFormats.some(format =>
    fileName.toLowerCase().endsWith(format)
  );

  return {
    passed: hasValidFormat,
    confidence: hasValidFormat ? 0.95 : 0.1,
    reason: hasValidFormat ? 'valid_image_format' : 'invalid_image_format'
  };
}

/**
 * 检查文件大小
 */
async function checkFileSize(context) {
  const { fileSize } = context;
  const maxSize = 10 * 1024 * 1024; // 10MB
  const minSize = 1024; // 1KB

  const validSize = fileSize >= minSize && fileSize <= maxSize;

  return {
    passed: validSize,
    confidence: validSize ? 0.9 : 0.2,
    reason: validSize ? 'valid_file_size' : 'invalid_file_size'
  };
}

/**
 * 分析文件名
 */
async function analyzeFileName(context) {
  const { fileName } = context;
  const lowerFileName = fileName.toLowerCase();

  // 风险关键词库
  const riskKeywords = new Map([
    // 高风险关键词
    ['blood', 0.9], ['violence', 0.9], ['weapon', 0.9], ['drug', 0.9],
    ['nude', 0.9], ['naked', 0.9], ['porn', 0.9], ['sex', 0.9],
    ['dead', 0.9], ['death', 0.9], ['kill', 0.9], ['murder', 0.9],

    // 中风险关键词
    ['fight', 0.6], ['angry', 0.6], ['aggressive', 0.6], ['bite', 0.6],
    ['sick', 0.6], ['disease', 0.6], ['injured', 0.6], ['bleeding', 0.6],
    ['illegal', 0.6], ['stolen', 0.6], ['fake', 0.6], ['scam', 0.6],

    // 宠物相关风险词
    ['abuse', 0.7], ['torture', 0.8], ['harm', 0.7], ['hurt', 0.6]
  ]);

  let riskScore = 0;
  const matchedKeywords = [];

  // 检查风险关键词
  for (const [keyword, weight] of riskKeywords) {
    if (lowerFileName.includes(keyword)) {
      riskScore = Math.max(riskScore, weight);
      matchedKeywords.push(keyword);
    }
  }

  const confidence = 1 - riskScore;
  const passed = riskScore < 0.7;

  return {
    passed,
    confidence,
    reason: matchedKeywords.length > 0 ?
      `suspicious_keywords: ${matchedKeywords.join(', ')}` :
      'filename_analysis_passed'
  };
}

/**
 * 分析URL
 */
async function analyzeUrl(context) {
  const { imageUrl } = context;
  const lowerUrl = imageUrl.toLowerCase();

  // 检查可疑域名和路径
  const suspiciousPatterns = [
    'temp', 'tmp', 'test', 'demo', 'localhost',
    'adult', 'xxx', 'porn', 'sex', 'violence', 'illegal'
  ];

  let riskScore = 0;
  const matchedPatterns = [];

  for (const pattern of suspiciousPatterns) {
    if (lowerUrl.includes(pattern)) {
      riskScore = Math.max(riskScore, 0.6);
      matchedPatterns.push(pattern);
    }
  }

  const confidence = 1 - riskScore;
  const passed = riskScore < 0.6;

  return {
    passed,
    confidence,
    reason: matchedPatterns.length > 0 ?
      `suspicious_url_patterns: ${matchedPatterns.join(', ')}` :
      'url_analysis_passed'
  };
}

/**
 * 检查用户信任分数
 */
async function checkUserTrustScore(context) {
  const { userId } = context;

  try {
    // 查询用户历史审核记录
    const userAudits = await db.collection('image_audits')
      .where({ userId })
      .orderBy('createdAt', 'desc')
      .limit(20)
      .get();

    if (userAudits.data.length === 0) {
      // 新用户给予中等信任度
      return {
        passed: true,
        confidence: 0.6,
        reason: 'new_user_default_trust'
      };
    }

    // 计算用户信任分数
    const totalAudits = userAudits.data.length;
    const rejectedCount = userAudits.data.filter(audit => audit.status === 'rejected').length;
    const recentViolations = userAudits.data.slice(0, 5).filter(audit => audit.status === 'rejected').length;

    let trustScore = 1 - (rejectedCount / totalAudits);

    // 如果最近有违规，降低信任度
    if (recentViolations > 0) {
      trustScore *= (1 - recentViolations * 0.3);
    }

    const passed = trustScore > 0.3;

    return {
      passed,
      confidence: trustScore,
      reason: passed ? 'user_trusted' : 'user_low_trust'
    };

  } catch (error) {
    console.warn('检查用户信任分数失败:', error);
    return {
      passed: true,
      confidence: 0.5,
      reason: 'trust_check_failed'
    };
  }
}

/**
 * 检查上传频率
 */
async function checkUploadFrequency(context) {
  const { userId } = context;

  try {
    // 检查最近1小时内的上传次数
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentUploads = await db.collection('image_audits')
      .where({ userId })
      .where('createdAt', '>=', oneHourAgo)
      .get();

    const uploadCount = recentUploads.data.length;
    const maxUploadsPerHour = 20; // 每小时最多20张

    const passed = uploadCount < maxUploadsPerHour;
    const confidence = passed ? 0.8 : Math.max(0.2, 1 - (uploadCount / maxUploadsPerHour));

    return {
      passed,
      confidence,
      reason: passed ? 'upload_frequency_normal' : 'upload_frequency_high'
    };

  } catch (error) {
    console.warn('检查上传频率失败:', error);
    return {
      passed: true,
      confidence: 0.8,
      reason: 'frequency_check_failed'
    };
  }
}

/**
 * 计算最终审核分数
 */
function calculateFinalAuditScore(ruleResults) {
  let totalWeight = 0;
  let weightedScore = 0;
  let hasRejection = false;
  let highestRisk = 0;

  const details = {};

  for (const { rule, result } of ruleResults) {
    totalWeight += rule.weight;
    weightedScore += result.confidence * rule.weight;

    details[rule.name] = result;

    // 如果有规则明确拒绝且置信度高，直接拒绝
    if (!result.passed && result.confidence > 0.8) {
      hasRejection = true;
    }

    // 记录最高风险分数
    highestRisk = Math.max(highestRisk, 1 - result.confidence);
  }

  const finalConfidence = weightedScore / totalWeight;

  // 决策逻辑
  let status, reason, riskLevel;

  if (hasRejection || finalConfidence < 0.3) {
    status = 'rejected';
    reason = 'high_risk_content_detected';
    riskLevel = 'high';
  } else if (finalConfidence < 0.6 || highestRisk > 0.5) {
    status = 'manual_review';
    reason = 'medium_risk_needs_review';
    riskLevel = 'medium';
  } else {
    // 10%随机抽查机制
    const randomCheck = Math.random() < 0.1;
    if (randomCheck) {
      status = 'manual_review';
      reason = 'random_manual_review';
      riskLevel = 'low';
    } else {
      status = 'approved';
      reason = 'content_appears_safe';
      riskLevel = 'low';
    }
  }

  return {
    status,
    confidence: finalConfidence,
    reason,
    details,
    riskLevel
  };
}

/**
 * 记录审核结果
 */
async function recordAuditResult(fileId, auditResult, context) {
  try {
    const auditRecord = {
      fileId,
      imageUrl: context.imageUrl,
      fileName: context.fileName,
      fileSize: context.fileSize,
      userId: context.userId,
      status: auditResult.status,
      confidence: auditResult.confidence,
      reason: auditResult.reason,
      riskLevel: auditResult.riskLevel,
      details: auditResult.details,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('image_audits').add(auditRecord);
    console.log(`审核记录已保存: ${fileId}, 状态: ${auditResult.status}`);

  } catch (error) {
    console.error('保存审核记录失败:', error);
  }
}

// 拉黑用户
async function blockUser({ targetUserId }, userId) {
  if (userId === targetUserId) {
    return {
      success: false,
      message: '不能拉黑自己'
    };
  }

  const existing = await db.collection('blacklist')
    .where({ user_id: userId, blocked_user_id: targetUserId })
    .get();

  if (existing.data.length > 0) {
    return {
      success: false,
      message: '您已经拉黑过这个用户了'
    };
  }

  // 检查是否正在关注该用户，如果是则先取消关注
  const followRelation = await db.collection('follows')
    .where({ follower_id: userId, following_id: targetUserId })
    .get();

  if (followRelation.data.length > 0) {
    // 取消关注
    await db.collection('follows').doc(followRelation.data[0]._id).remove();
  }

  // 添加拉黑记录
  await db.collection('blacklist').add({
    data: {
      user_id: userId,
      blocked_user_id: targetUserId,
      created_at: new Date()
    }
  });

  return {
    success: true,
    message: '拉黑成功'
  };
}

// 取消拉黑用户
async function unblockUser({ targetUserId }, userId) {
  const result = await db.collection('blacklist')
    .where({ user_id: userId, blocked_user_id: targetUserId })
    .remove();

  if (result.deleted === 0) {
    return {
      success: false,
      message: '该用户不在您的拉黑列表中'
    };
  }

  return {
    success: true,
    message: '取消拉黑成功'
  };
}

// 获取拉黑列表
async function getBlockedUsers({ limit = 20, offset = 0 }, userId) {
  const result = await db.collection('blacklist')
    .where({ user_id: userId })
    .orderBy('created_at', 'desc')
    .skip(offset)
    .limit(limit)
    .get();

  // 获取被拉黑用户的详细信息
  const blockedUserIds = result.data.map(item => item.blocked_user_id);
  let users = [];

  if (blockedUserIds.length > 0) {
    const usersResult = await db.collection('users')
      .where({
        _id: db.command.in(blockedUserIds)
      })
      .get();

    users = usersResult.data;
  }

  // 合并数据，返回前端期望的格式
  const blockedUsers = result.data.map(blockItem => {
    const user = users.find(u => u._id === blockItem.blocked_user_id);
    return {
      _id: blockItem.blocked_user_id,
      nickname: user?.nickname || '用户已删除',
      avatar_url: user?.avatar_url || '',
      bio: user?.bio || '',
      blockedAt: blockItem.created_at
    };
  });

  return {
    success: true,
    data: blockedUsers
  };
}

// 获取分类列表
async function getCategories() {
  const result = await db.collection('categories')
    .orderBy('level', 'asc')
    .orderBy('order', 'asc')
    .get();
  
  return {
    success: true,
    data: result.data
  };
}

// 关注/取消关注用户
async function toggleFollow({ targetUserId }, userId) {
  if (userId === targetUserId) {
    return {
      success: false,
      message: '不能关注自己'
    };
  }

  const existing = await db.collection('follows')
    .where({ follower_id: userId, following_id: targetUserId })
    .get();

  if (existing.data.length > 0) {
    // 取消关注
    await db.collection('follows').doc(existing.data[0]._id).remove();
    return {
      success: true,
      action: 'unfollowed',
      message: '取消关注成功'
    };
  } else {
    // 检查是否已拉黑该用户，如果是则先取消拉黑
    const blacklistRelation = await db.collection('blacklist')
      .where({ user_id: userId, blocked_user_id: targetUserId })
      .get();

    if (blacklistRelation.data.length > 0) {
      // 取消拉黑
      await db.collection('blacklist').doc(blacklistRelation.data[0]._id).remove();
    }

    // 添加关注
    await db.collection('follows').add({
      data: {
        follower_id: userId,
        following_id: targetUserId,
        created_at: new Date()
      }
    });
    return {
      success: true,
      action: 'followed',
      message: '关注成功'
    };
  }
}

// 获取用户信息
async function getUserInfo(data) {
  const { userId } = data || {};

  if (!userId) {
    return {
      success: false,
      message: '用户ID不能为空'
    };
  }

  try {
    const user = await db.collection('users').doc(userId).get();

    if (user.data && user.data._id) {
      // 真实用户，获取用户统计信息
      const userData = user.data;

      const [posts, followers, following, totalLikes, bookmarks] = await Promise.all([
        db.collection('posts').where({ author_id: userId }).count(),
        db.collection('follows').where({ following_id: userId }).count(),
        db.collection('follows').where({ follower_id: userId }).count(),
        db.collection('likes').where({ user_id: userId }).count(),
        db.collection('bookmarks').where({ user_id: userId }).count()
      ]);

      // 过滤敏感字段，不返回密码等敏感信息
      const { password, ...safeUserData } = userData;

      return {
        success: true,
        data: {
          ...safeUserData,
          id: userId, // 确保返回用户ID
          posts_count: posts.total,
          followers_count: followers.total,
          following_count: following.total,
          likes_count: totalLikes.total,
          bookmarks_count: bookmarks.total
        }
      };
    } else {
      // 用户不存在
      console.log('用户不存在:', userId);
      return {
        success: false,
        message: '用户不存在'
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);

    // 检查是否是文档不存在的错误
    if ((error.errMsg && error.errMsg.includes('does not exist')) ||
        (error.message && error.message.includes('does not exist'))) {
      return {
        success: false,
        message: '用户不存在'
      };
    }

    // 其他错误
    return {
      success: false,
      message: '获取用户信息失败'
    };
  }
}



// 格式化时间为相对时间
function getTimeAgo(date) {
  const now = new Date();
  const diffInMs = now - new Date(date);
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return '刚刚';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  } else if (diffInDays < 30) {
    return `${diffInDays}天前`;
  } else {
    return new Date(date).toLocaleDateString('zh-CN');
  }
}

// 获取用户收藏列表
async function getUserBookmarks({ limit = 10, offset = 0 }, userId) {
  if (!userId) {
    return {
      success: true,
      data: [],
      pagination: { page: Math.floor(offset / limit) + 1, limit, hasMore: false }
    };
  }

  const bookmarks = await db.collection('bookmarks')
    .where({ user_id: userId })
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  // 获取收藏的帖子详情
  const postIds = bookmarks.data.map(bookmark => bookmark.post_id);
  if (postIds.length === 0) {
    return {
      success: true,
      data: [],
      pagination: { page: Math.floor(offset / limit) + 1, limit, hasMore: false }
    };
  }

  const posts = await db.collection('posts')
    .where({ _id: db.command.in(postIds) })
    .get();

  // 处理帖子数据
  const postsWithStats = await Promise.all(
    posts.data.map(async (post) => {
      const [likes, wants, ratings, bookmarksCount, processedImages] = await Promise.all([
        db.collection('likes').where({ post_id: post._id }).count(),
        db.collection('wants').where({ post_id: post._id }).count(),
        db.collection('ratings').where({ post_id: post._id }).get(),
        db.collection('bookmarks').where({ post_id: post._id }).count(),
        processImageUrls(post.images || [], post._id)
      ]);

      const avgRating = ratings.data.length > 0
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length
        : 0;

      // 获取作者信息
      let authorInfo = { nickname: '神秘用户', avatar_url: '/default-avatar.png' };
      const authorId = post.author_id || post.userId;
      if (authorId) {
        try {
          const authorResult = await db.collection('users').doc(authorId).get();
          if (authorResult.data) {
            authorInfo = {
              _id: authorId,
              nickname: authorResult.data.nickname || '神秘用户',
              avatar_url: authorResult.data.avatar_url || '/default-avatar.png'
            };
          }
        } catch (error) {
          console.error('获取作者信息失败:', error);
        }
      }

      return {
        ...post,
        images: processedImages,
        likes_count: likes.total,
        wants_count: wants.total,
        bookmarks_count: bookmarksCount.total,
        ratings_count: ratings.data.length,
        avg_rating: Math.round(avgRating * 10) / 10,
        author: authorInfo
      };
    })
  );

  return {
    success: true,
    data: postsWithStats,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: bookmarks.data.length === limit
    }
  };
}

// 获取用户发布列表
async function getUserPosts({ limit = 10, offset = 0, targetUserId }, userId) {
  const queryUserId = targetUserId || userId;

  if (!queryUserId) {
    return {
      success: true,
      data: [],
      pagination: { page: Math.floor(offset / limit) + 1, limit, hasMore: false }
    };
  }

  // 如果查看的是其他用户的帖子，检查是否被拉黑
  if (targetUserId && targetUserId !== userId && userId && userId !== 'anonymous') {
    const blacklistCheck = await db.collection('blacklist')
      .where({
        user_id: targetUserId,
        blocked_user_id: userId
      })
      .get();

    if (blacklistCheck.data.length > 0) {
      // 被拉黑用户无法查看对方的帖子
      return {
        success: true,
        data: [],
        pagination: { page: Math.floor(offset / limit) + 1, limit, hasMore: false }
      };
    }
  }

  const posts = await db.collection('posts')
    .where({
      author_id: queryUserId,
      status: 'published' // 只显示已发布的帖子
    })
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  // 处理帖子数据
  const postsWithStats = await Promise.all(
    posts.data.map(async (post) => {
      const [likes, wants, ratings, bookmarks, processedImages] = await Promise.all([
        db.collection('likes').where({ post_id: post._id }).count(),
        db.collection('wants').where({ post_id: post._id }).count(),
        db.collection('ratings').where({ post_id: post._id }).get(),
        db.collection('bookmarks').where({ post_id: post._id }).count(),
        processImageUrls(post.images || [], post._id)
      ]);

      const avgRating = ratings.data.length > 0
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length
        : 0;

      // 获取作者信息
      let authorInfo = { nickname: '神秘用户', avatar_url: '/default-avatar.png' };
      const authorId = post.author_id || post.userId;
      if (authorId) {
        try {
          const authorResult = await db.collection('users').doc(authorId).get();
          if (authorResult.data) {
            authorInfo = {
              _id: authorId,
              nickname: authorResult.data.nickname || '神秘用户',
              avatar_url: authorResult.data.avatar_url || '/default-avatar.png'
            };
          }
        } catch (error) {
          console.error('获取作者信息失败:', error);
        }
      }

      return {
        ...post,
        images: processedImages,
        likes_count: likes.total,
        wants_count: wants.total,
        bookmarks_count: bookmarks.total,
        ratings_count: ratings.data.length,
        avg_rating: Math.round(avgRating * 10) / 10,
        author: authorInfo
      };
    })
  );

  return {
    success: true,
    data: postsWithStats,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: posts.data.length === limit
    }
  };
}

// 获取用户关注列表
async function getUserFollowing({ limit = 10, offset = 0, targetUserId }, userId) {
  const queryUserId = targetUserId || userId;

  const follows = await db.collection('follows')
    .where({ follower_id: queryUserId })
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  // 获取被关注用户的详细信息
  const followingUserIds = follows.data.map(follow => follow.following_id);
  let followingUsers = [];

  if (followingUserIds.length > 0) {
    const usersResult = await db.collection('users')
      .where({
        _id: db.command.in(followingUserIds)
      })
      .get();

    // 创建用户信息映射
    const userMap = {};
    usersResult.data.forEach(user => {
      userMap[user._id] = user;
    });

    // 合并关注记录和用户信息
    followingUsers = follows.data.map(follow => {
      const userInfo = userMap[follow.following_id] || {};
      return {
        ...follow,
        following_nickname: userInfo.nickname || '用户已删除',
        following_avatar_url: userInfo.avatar_url || '/default-avatar.png',
        following_bio: userInfo.bio || '',
        following_user_info: userInfo
      };
    });
  }

  return {
    success: true,
    data: followingUsers,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: follows.data.length === limit
    }
  };
}

// 获取用户粉丝列表
async function getUserFollowers({ limit = 10, offset = 0, targetUserId }, userId) {
  const queryUserId = targetUserId || userId;

  const followers = await db.collection('follows')
    .where({ following_id: queryUserId })
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  return {
    success: true,
    data: followers.data,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: followers.data.length === limit
    }
  };
}

// 创建通知
async function createNotification({ type, recipient_id, sender_id, post_id, message, data = {} }) {
  await db.collection('notifications').add({
    data: {
      type, // 'contact', 'system', 'report', 'appeal'
      recipient_id,
      sender_id,
      post_id,
      message,
      data,
      is_read: false,
      created_at: new Date()
    }
  });
}

// 创建联系方式交换通知
async function createContactNotification({ requester_id, owner_id, post_id, post_title, post_type, requester_contact, owner_contact, requester_nickname, owner_nickname }) {
  // 修复：应该给发起联系的用户发送对方的联系方式
  // 发起方（requester）应该收到帖子作者（owner）的联系方式

  // 转换联系方式格式：从 {phone, wechat} 转换为 {type, value}
  let contactInfo = { type: 'wechat', value: '未提供' };

  if (owner_contact) {
    if (owner_contact.type && owner_contact.value) {
      // 如果已经是 {type, value} 格式
      contactInfo = owner_contact;
    } else {
      // 如果是 {phone, wechat} 格式，优先使用微信，其次手机
      if (owner_contact.wechat) {
        contactInfo = { type: 'wechat', value: owner_contact.wechat };
      } else if (owner_contact.phone) {
        contactInfo = { type: 'phone', value: owner_contact.phone };
      }
    }
  }

  await createNotification({
    type: 'contact',
    recipient_id: requester_id, // 发给发起联系的用户
    sender_id: owner_id,        // 来自帖子作者
    post_id: post_id,
    message: `已取得${owner_nickname}的联系方式`,
    data: {
      post_title: post_title,
      post_type: post_type,
      sender_contact: contactInfo,
      sender_nickname: owner_nickname,     // 帖子作者的昵称
      author_nickname: requester_nickname, // 发起方的昵称
      action_type: 'obtained' // 获得联系方式
    }
  });
}

// 创建系统举报通知
async function createReportNotification({ user_id, report_type, reason, post_title, post_id }) {
  let message = '';
  let data = {};

  if (report_type === 'post_hidden') {
    message = `您发布的宝贝${post_title}被人多次举报已经隐藏，举报理由为：${reason}。如要申诉请点击右边的申诉按钮。`;
    data = {
      report_type: 'post_hidden',
      post_title: post_title,
      post_id: post_id,
      reason: reason,
      can_appeal: true
    };
  } else if (report_type === 'user_warned') {
    message = `您被人多次举报涉嫌诈骗，如要申诉请点击右边申诉按钮。`;
    data = {
      report_type: 'user_warned',
      reason: reason,
      can_appeal: true
    };
  }

  await createNotification({
    type: 'system',
    recipient_id: user_id,
    sender_id: 'system',
    post_id: post_id,
    message: message,
    data: data
  });
}

// 获取用户通知列表
async function getUserNotifications({ limit = 20, offset = 0, type }, userId) {
  if (!userId) {
    return {
      success: true,
      data: [],
      pagination: { page: Math.floor(offset / limit) + 1, limit, hasMore: false }
    };
  }

  let query = db.collection('notifications').where({ recipient_id: userId });

  if (type) {
    query = query.where({ type });
  }

  const notifications = await query
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  return {
    success: true,
    data: notifications.data,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: notifications.data.length === limit
    }
  };
}

// 标记通知为已读
async function markNotificationRead({ notificationId }, userId) {
  const notification = await db.collection('notifications').doc(notificationId).get();

  if (!notification.data || notification.data.recipient_id !== userId) {
    throw new Error('通知不存在或无权限');
  }

  await db.collection('notifications').doc(notificationId).update({
    data: {
      is_read: true,
      read_at: new Date()
    }
  });

  return {
    success: true,
    message: '标记为已读成功'
  };
}

// 删除通知
async function deleteNotification({ notificationId }, userId) {
  const notification = await db.collection('notifications').doc(notificationId).get();

  if (!notification.data || notification.data.recipient_id !== userId) {
    throw new Error('通知不存在或无权限');
  }

  await db.collection('notifications').doc(notificationId).remove();

  return {
    success: true,
    message: '删除通知成功'
  };
}

// 批量删除通知
async function bulkDeleteNotifications({ notificationIds }, userId) {
  if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
    throw new Error('通知ID列表不能为空');
  }

  // 验证所有通知都属于当前用户 - 逐个验证避免 _id in 查询问题
  const validIds = [];
  for (const id of notificationIds) {
    try {
      const notification = await db.collection('notifications').doc(id).get();
      console.log(`验证通知 ${id}:`, notification.data ? '存在' : '不存在');
      if (notification.data) {
        console.log(`通知 ${id} 的 recipient_id:`, notification.data.recipient_id);
        console.log(`当前用户ID:`, userId);
        if (notification.data.recipient_id === userId) {
          validIds.push(id);
          console.log(`通知 ${id} 验证通过`);
        } else {
          console.log(`通知 ${id} 权限验证失败`);
        }
      }
    } catch (error) {
      console.log(`通知 ${id} 不存在或无权限:`, error.message);
    }
  }

  if (validIds.length === 0) {
    throw new Error('没有有效的通知可删除');
  }

  // 逐个删除通知
  let deletedCount = 0;
  for (const id of validIds) {
    try {
      const result = await db.collection('notifications').doc(id).remove();
      if (result.deleted > 0) {
        deletedCount++;
      }
    } catch (error) {
      console.log(`删除通知 ${id} 失败:`, error.message);
    }
  }

  return {
    success: true,
    message: `成功删除${deletedCount}条通知`,
    deletedCount: deletedCount
  };
}

// 搜索帖子
async function searchPosts({
  keyword = '',
  category = '',
  type = '',
  location = '',
  limit = 10,
  offset = 0
}) {
  console.log('搜索参数:', { keyword, category, type, location, limit, offset });

  // 构建查询条件
  let query = db.collection('posts');

  // 添加筛选条件
  const conditions = [];

  if (category) {
    conditions.push({ category });
  }

  if (type) {
    conditions.push({ type });
  }

  if (location) {
    conditions.push({ location: db.command.regex({ regexp: location, options: 'i' }) });
  }

  // 如果有多个条件，使用 and 组合
  if (conditions.length > 0) {
    query = query.where(db.command.and(conditions));
  }

  // 获取帖子
  const posts = await query
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  // 如果有关键词，进行智能过滤
  let filteredPosts = posts.data;
  if (keyword) {
    const keywordLower = keyword.toLowerCase();

    // 智能搜索：支持标题、描述、品种、品种标签
    filteredPosts = posts.data.filter(post => {
      // 基础文本搜索
      const titleMatch = post.title.toLowerCase().includes(keywordLower);
      const descMatch = post.description.toLowerCase().includes(keywordLower);

      // 品种搜索
      const breedMatch = post.breed && post.breed.toLowerCase().includes(keywordLower);

      // 品种标签搜索（支持别名和关键词）
      const breedTagsMatch = post.breed_tags && post.breed_tags.some(tag =>
        tag.toLowerCase().includes(keywordLower)
      );

      // 位置搜索
      const locationMatch = post.location && post.location.toLowerCase().includes(keywordLower);

      return titleMatch || descMatch || breedMatch || breedTagsMatch || locationMatch;
    });

    // 按相关性排序
    filteredPosts.sort((a, b) => {
      let scoreA = 0;
      let scoreB = 0;

      // 标题匹配权重最高
      if (a.title.toLowerCase().includes(keywordLower)) scoreA += 10;
      if (b.title.toLowerCase().includes(keywordLower)) scoreB += 10;

      // 品种精确匹配权重高
      if (a.breed && a.breed.toLowerCase() === keywordLower) scoreA += 8;
      if (b.breed && b.breed.toLowerCase() === keywordLower) scoreB += 8;

      // 品种包含匹配
      if (a.breed && a.breed.toLowerCase().includes(keywordLower)) scoreA += 6;
      if (b.breed && b.breed.toLowerCase().includes(keywordLower)) scoreB += 6;

      // 品种标签匹配
      if (a.breed_tags && a.breed_tags.some(tag => tag.toLowerCase().includes(keywordLower))) scoreA += 4;
      if (b.breed_tags && b.breed_tags.some(tag => tag.toLowerCase().includes(keywordLower))) scoreB += 4;

      // 描述匹配
      if (a.description.toLowerCase().includes(keywordLower)) scoreA += 2;
      if (b.description.toLowerCase().includes(keywordLower)) scoreB += 2;

      return scoreB - scoreA; // 降序排列
    });
  }

  // 处理帖子数据
  const postsWithStats = await Promise.all(
    filteredPosts.map(async (post) => {
      const [likes, wants, ratings, bookmarks, processedImages] = await Promise.all([
        db.collection('likes').where({ post_id: post._id }).count(),
        db.collection('wants').where({ post_id: post._id }).count(),
        db.collection('ratings').where({ post_id: post._id }).get(),
        db.collection('bookmarks').where({ post_id: post._id }).count(),
        processImageUrls(post.images || [], post._id)
      ]);

      const avgRating = ratings.data.length > 0
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length
        : 0;

      // 获取作者信息
      let authorInfo = { nickname: '神秘用户', avatar_url: '/default-avatar.png' };
      const authorId = post.author_id || post.userId;
      if (authorId) {
        try {
          const authorResult = await db.collection('users').doc(authorId).get();
          if (authorResult.data) {
            authorInfo = {
              _id: authorId,
              nickname: authorResult.data.nickname || '神秘用户',
              avatar_url: authorResult.data.avatar_url || '/default-avatar.png'
            };
          }
        } catch (error) {
          console.error('获取作者信息失败:', error);
        }
      }

      return {
        ...post,
        images: processedImages,
        likes_count: likes.total,
        wants_count: wants.total,
        bookmarks_count: bookmarks.total,
        ratings_count: ratings.data.length,
        avg_rating: Math.round(avgRating * 10) / 10,
        author: authorInfo
      };
    })
  );

  return {
    success: true,
    data: postsWithStats,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: filteredPosts.length === limit,
      total: filteredPosts.length
    }
  };
}

// 管理员权限检查（通过OPENID）
async function checkAdminPermission(openId) {
  try {
    console.log('检查管理员权限，OPENID:', openId);

    // 查找是否有管理员会话记录
    const sessionResult = await db.collection('admin_sessions')
      .where({ openid: openId, status: 'active' })
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    console.log('查找到的会话记录数量:', sessionResult.data.length);

    if (sessionResult.data.length === 0) {
      console.log('没有找到活跃的管理员会话');
      return false;
    }

    const session = sessionResult.data[0];
    console.log('找到会话记录:', session);

    // 检查会话是否过期（24小时）
    const sessionAge = Date.now() - session.created_at.getTime();
    console.log('会话年龄（毫秒）:', sessionAge);

    if (sessionAge > 24 * 60 * 60 * 1000) {
      // 会话过期，删除记录
      console.log('会话已过期，删除记录');
      await db.collection('admin_sessions').doc(session._id).remove();
      return false;
    }

    // 验证管理员是否仍然有效
    const admin = await getAdminById(session.admin_id);
    console.log('管理员信息:', admin);

    const isValid = admin && admin.status === 'active';
    console.log('权限检查结果:', isValid);

    return isValid;

  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

// 获取举报列表
async function getReports({ type = 'post', status = 'pending', limit = 20, offset = 0 }, userId) {
  // 检查管理员权限
  const isAdmin = await checkAdminPermission(userId);
  if (!isAdmin) {
    throw new Error('无权限访问');
  }

  const collection = type === 'post' ? 'post_reports' : 'user_reports';

  let query = db.collection(collection);
  if (status !== 'all') {
    query = query.where({ status });
  }

  const reports = await query
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  return {
    success: true,
    data: reports.data,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: reports.data.length === limit
    }
  };
}

// 处理举报
async function handleReport({ reportId, action, reason }, userId) {
  // 检查管理员权限
  const isAdmin = await checkAdminPermission(userId);
  if (!isAdmin) {
    throw new Error('无权限操作');
  }

  // 更新举报状态
  await db.collection('post_reports').doc(reportId).update({
    data: {
      status: action, // 'approved', 'rejected', 'pending'
      admin_id: userId,
      admin_reason: reason,
      handled_at: new Date()
    }
  });

  return {
    success: true,
    message: '举报处理成功'
  };
}

// 封禁用户
async function banUser({ targetUserId, reason, duration }, userId) {
  // 检查管理员权限
  const isAdmin = await checkAdminPermission(userId);
  if (!isAdmin) {
    throw new Error('无权限操作');
  }

  const banUntil = duration ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000) : null;

  // 创建封禁记录
  await db.collection('user_bans').add({
    data: {
      user_id: targetUserId,
      admin_id: userId,
      reason,
      ban_until: banUntil,
      is_permanent: !duration,
      created_at: new Date()
    }
  });

  return {
    success: true,
    message: '用户封禁成功'
  };
}

// 提交申诉
async function submitAppeal({ reportId, reason, type = 'user' }, userId) {
  try {
    // 如果有reportId，检查是否已经对此举报申诉过
    if (reportId) {
      const existing = await db.collection('appeals')
        .where({ report_id: reportId, appellant_id: userId })
        .get();

      if (existing.data.length > 0) {
        return {
          success: false,
          message: '您已经对此处罚提交过申诉了'
        };
      }
    } else {
      // 如果没有reportId，检查是否有待处理的用户申诉
      const existingUserAppeal = await db.collection('appeals')
        .where({
          appellant_id: userId,
          status: 'pending',
          type: 'user'
        })
        .get();

      if (existingUserAppeal.data.length > 0) {
        return {
          success: false,
          message: '您已有待处理的申诉，请等待管理员处理'
        };
      }

      // 检查用户是否有权限申诉（是否被处罚）
      const permissions = await db.collection('user_permissions').doc(userId).get();
      if (!permissions.data || (permissions.data.canPublishPost && permissions.data.canContact)) {
        return {
          success: false,
          message: '您当前没有需要申诉的处罚'
        };
      }
    }

    // 创建申诉记录
    const appealData = {
      appellant_id: userId,
      reason: reason,
      type: type, // 'post' 或 'user'
      status: 'pending',
      created_at: new Date()
    };

    if (reportId) {
      appealData.report_id = reportId;
    }

    await db.collection('appeals').add({
      data: appealData
    });

    // 发送申诉提交通知给用户
    await createNotification({
      type: 'system',
      recipient_id: userId,
      sender_id: 'system',
      message: '您的申诉已提交，我们会在3个工作日内处理',
      data: {
        appeal_type: 'submitted',
        report_id: reportId || null
      }
    });

    return {
      success: true,
      message: '申诉提交成功'
    };
  } catch (error) {
    console.error('提交申诉失败:', error);
    return {
      success: false,
      message: '提交申诉失败，请重试'
    };
  }
}

// 获取申诉列表（管理员）
async function getAppeals({ status = 'pending', limit = 20, offset = 0 }, userId) {
  // 检查管理员权限
  const isAdmin = await checkAdminPermission(userId);
  if (!isAdmin) {
    throw new Error('无权限访问');
  }

  let query = db.collection('appeals');
  if (status !== 'all') {
    query = query.where({ status });
  }

  const appeals = await query
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  return {
    success: true,
    data: appeals.data,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: appeals.data.length === limit
    }
  };
}

// 处理申诉（管理员）
async function handleAppeal({ appealId, action, adminReason }, userId) {
  // 检查管理员权限
  const isAdmin = await checkAdminPermission(userId);
  if (!isAdmin) {
    throw new Error('无权限操作');
  }

  // 获取申诉信息
  const appeal = await db.collection('appeals').doc(appealId).get();
  if (!appeal.data) {
    throw new Error('申诉不存在');
  }

  // 更新申诉状态
  await db.collection('appeals').doc(appealId).update({
    data: {
      status: action, // 'approved', 'rejected'
      admin_id: userId,
      admin_reason: adminReason,
      handled_at: new Date()
    }
  });

  // 如果申诉通过，需要撤销原处罚
  if (action === 'approved') {
    // 根据申诉类型撤销相应的处罚
    if (appeal.data.type === 'post') {
      // 撤销帖子举报处罚
      if (appeal.data.report_id) {
        await db.collection('post_reports').doc(appeal.data.report_id).update({
          data: {
            status: 'appeal_approved',
            appeal_handled_at: new Date()
          }
        });
      }
    } else if (appeal.data.type === 'user') {
      // 撤销用户举报处罚，恢复用户权限
      if (appeal.data.report_id) {
        await db.collection('user_reports').doc(appeal.data.report_id).update({
          data: {
            status: 'appeal_approved',
            appeal_handled_at: new Date()
          }
        });
      }

      // 恢复用户权限
      await db.collection('user_permissions').doc(appeal.data.appellant_id).set({
        data: {
          user_id: appeal.data.appellant_id,
          canPublishPost: true,
          canContact: true,
          canLike: true,
          canDislike: true,
          canReportPost: true,
          canReportUser: true,
          bannedUntil: null,
          banReason: null,
          restoredAt: new Date(),
          restoredBy: userId
        }
      });
    }
  }

  // 发送申诉结果通知给用户
  const resultMessage = action === 'approved'
    ? '您的申诉已通过，相关处罚已撤销'
    : '您的申诉已被驳回，原处罚维持';

  await createNotification({
    type: 'system',
    recipient_id: appeal.data.appellant_id,
    sender_id: 'system',
    message: resultMessage,
    data: {
      appeal_type: 'result',
      appeal_id: appealId,
      action: action,
      admin_reason: adminReason
    }
  });

  return {
    success: true,
    message: '申诉处理成功'
  };
}

// 管理员登录
async function adminLogin({ username, password }, openId) {
  try {
    console.log('管理员登录尝试，用户名:', username);
    console.log('接收到的参数:', { username, password: password ? '***' : undefined });

    // 特殊处理：如果是超级管理员首次登录，自动创建账号
    if (username === 'superadminTT' && password === '019870416tao') {
      // 查找超级管理员账号
      const existingAdmin = await db.collection('admins')
        .where({ username: 'superadminTT' })
        .get();

      if (existingAdmin.data.length === 0) {
        // 创建超级管理员账号 - 使用正确的密码哈希
        const bcrypt = require('bcryptjs');
        const superAdminPasswordHash = await bcrypt.hash('019870416tao', 12);

        const superAdminData = {
          username: 'superadminTT',
          password_hash: superAdminPasswordHash, // 使用bcrypt哈希
          role: 'super_admin',
          level: 0, // 最高级别
          status: 'active',
          created_at: new Date(),
          updated_at: new Date(),
          permissions: ['*'], // 所有权限
          failed_login_count: 0,
          login_attempts: 0,
          last_login: null,
          can_create_admin: true,
          can_delete_admin: true,
          max_admin_level: 4,
          is_system_account: true,
          two_factor_enabled: false,
          session_timeout: 3600
        };

        await db.collection('admins').add({
          data: superAdminData
        });

        console.log('超级管理员账号已自动创建');
      }
    }

    // 查找管理员账号
    const adminResult = await db.collection('admins')
      .where({ username: username, status: 'active' })
      .get();

    if (adminResult.data.length === 0) {
      return {
        success: false,
        message: '用户名或密码错误'
      };
    }

    const admin = adminResult.data[0];

    // 使用bcrypt验证密码（所有管理员统一使用安全的哈希验证）
    let isValidPassword = false;
    try {
      const bcrypt = require('bcryptjs');
      isValidPassword = await bcrypt.compare(password, admin.password_hash);
    } catch (error) {
      console.error('密码验证失败:', error);
      isValidPassword = false;
    }

    if (!isValidPassword) {
      // 记录失败登录
      await db.collection('admins').doc(admin._id).update({
        data: {
          failed_login_count: admin.failed_login_count + 1,
          login_attempts: admin.login_attempts + 1
        }
      });

      return {
        success: false,
        message: '密码错误'
      };
    }

    // 更新登录信息
    await db.collection('admins').doc(admin._id).update({
      data: {
        last_login: new Date(),
        failed_login_count: 0
      }
    });

    // 记录登录日志
    await logAdminAction(admin._id, 'login', 'system', null, {
      login_time: new Date(),
      ip_address: 'unknown' // 实际应该获取真实IP
    });

    // 创建管理员会话记录
    const sessionToken = 'admin-token-' + admin._id + '-' + Date.now();
    await db.collection('admin_sessions').add({
      data: {
        admin_id: admin._id,
        openid: openId,
        token: sessionToken,
        status: 'active',
        created_at: new Date(),
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
      }
    });

    // 返回管理员信息（不包含密码）
    const { password_hash, ...adminInfo } = admin;
    return {
      success: true,
      data: {
        ...adminInfo,
        token: sessionToken
      }
    };

  } catch (error) {
    console.error('管理员登录失败:', error);
    return {
      success: false,
      message: '登录失败，请重试'
    };
  }
}

// 获取管理员列表
async function getAdmins({ level_greater_than, limit = 20, offset = 0 }, openId) {
  // 权限验证：检查是否为管理员
  let isAdmin = false;
  let currentAdmin = null;

  // 方法1：直接通过ID查找
  try {
    const adminResult = await db.collection('admins').doc(openId).get();
    if (adminResult.data) {
      isAdmin = true;
      currentAdmin = adminResult.data;
      console.log('通过ID找到管理员:', adminResult.data.username);
    }
  } catch (error) {
    console.log('通过ID查找管理员失败:', error.message);
  }

  // 方法2：如果ID查找失败，尝试通过用户名查找超级管理员
  if (!isAdmin) {
    try {
      const adminByUsername = await db.collection('admins').where({
        username: 'superadminTT'
      }).get();

      if (adminByUsername.data && adminByUsername.data.length > 0) {
        isAdmin = true;
        currentAdmin = adminByUsername.data[0];
        console.log('通过用户名找到超级管理员:', adminByUsername.data[0].username);
      }
    } catch (error) {
      console.log('通过用户名查找管理员失败:', error.message);
    }
  }

  // 方法3：如果还是没找到，检查是否有任何超级管理员权限
  if (!isAdmin) {
    try {
      const superAdmins = await db.collection('admins').where({
        role: 'super_admin'
      }).get();

      if (superAdmins.data && superAdmins.data.length > 0) {
        isAdmin = true;
        currentAdmin = superAdmins.data[0]; // 使用第一个超级管理员
        console.log('找到超级管理员，允许访问');
      }
    } catch (error) {
      console.log('查找超级管理员失败:', error.message);
    }
  }

  if (!isAdmin) {
    throw new Error('无权限访问');
  }

  let query = db.collection('admins');

  // 超级管理员可以查看所有管理员，其他管理员只能查看下级
  if (currentAdmin.role === 'super_admin') {
    // 超级管理员可以查看所有管理员
    if (level_greater_than !== undefined) {
      query = query.where({ level: db.command.gt(level_greater_than) });
    }
    // 不添加额外的level限制，可以查看所有管理员
  } else {
    // 普通管理员只能查看下级管理员
    if (level_greater_than !== undefined) {
      query = query.where({ level: db.command.gt(level_greater_than) });
    } else {
      query = query.where({ level: db.command.gt(currentAdmin.level) });
    }
  }

  const admins = await query
    .field({ password_hash: false }) // 不返回密码
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  return {
    success: true,
    data: admins.data,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: admins.data.length === limit
    }
  };
}

// 创建管理员
async function createAdmin(adminData, openId) {
  try {
    console.log('开始创建管理员，OPENID:', openId);
    console.log('管理员数据:', adminData);

    // 权限验证：检查是否为管理员
    let isAdmin = false;
    let creator = null;

    // 方法1：直接通过ID查找
    try {
      const adminResult = await db.collection('admins').doc(openId).get();
      if (adminResult.data) {
        isAdmin = true;
        creator = adminResult.data;
        console.log('通过ID找到管理员:', adminResult.data.username);
      }
    } catch (error) {
      console.log('通过ID查找管理员失败:', error.message);
    }

    // 方法2：如果ID查找失败，尝试通过用户名查找超级管理员
    if (!isAdmin) {
      try {
        const adminByUsername = await db.collection('admins').where({
          username: 'superadminTT'
        }).get();

        if (adminByUsername.data && adminByUsername.data.length > 0) {
          isAdmin = true;
          creator = adminByUsername.data[0];
          console.log('通过用户名找到超级管理员:', adminByUsername.data[0].username);
        }
      } catch (error) {
        console.log('通过用户名查找管理员失败:', error.message);
      }
    }

    // 方法3：如果还是没找到，检查是否有任何超级管理员权限
    if (!isAdmin) {
      try {
        const superAdmins = await db.collection('admins').where({
          role: 'super_admin'
        }).get();

        if (superAdmins.data && superAdmins.data.length > 0) {
          isAdmin = true;
          creator = superAdmins.data[0]; // 使用第一个超级管理员
          console.log('找到超级管理员，允许创建操作');
        }
      } catch (error) {
        console.log('查找超级管理员失败:', error.message);
      }
    }

    if (!isAdmin) {
      throw new Error('无权限创建管理员');
    }

  // 简化权限检查：只有超级管理员可以创建其他管理员
  if (creator.role !== 'super_admin') {
    throw new Error('只有超级管理员才能创建其他管理员');
  }

  // 检查用户名是否已存在
  const existingUsername = await db.collection('admins')
    .where({ username: adminData.username })
    .get();

  if (existingUsername.data.length > 0) {
    throw new Error('用户名已存在');
  }

  // 加密密码 - 使用OWASP推荐的工作因子12
  const bcrypt = require('bcryptjs');

  // 检查密码长度（bcrypt限制72字节）
  if (Buffer.byteLength(adminData.password, 'utf8') > 72) {
    throw new Error('密码长度不能超过72字节');
  }

  const password_hash = await bcrypt.hash(adminData.password, 12);

  // 根据角色设置默认权限
  const defaultPermissions = getDefaultPermissionsByRole(adminData.role);

  // 创建管理员
  const newAdmin = {
    username: adminData.username,
    password_hash: password_hash,
    role: adminData.role,
    level: adminData.level,
    permissions: [...defaultPermissions, ...(adminData.permissions || [])],
    created_by: creator._id,
    created_at: new Date(),
    updated_at: new Date(),
    status: 'active',
    is_system_account: false,
    can_create_admin: adminData.can_create_admin || false,
    can_delete_admin: adminData.can_delete_admin || false,
    max_admin_level: adminData.max_admin_level || 4,
    last_login: null,
    login_attempts: 0,
    failed_login_count: 0,
    two_factor_enabled: false,
    session_timeout: 3600
  };



  const result = await db.collection('admins').add({
    data: newAdmin
  });

  // 记录操作日志
  await logAdminAction(creator._id, 'create_admin', 'admin', result._id, {
    target_email: adminData.email,
    target_role: adminData.role
  });

  return {
    success: true,
    data: {
      id: result._id,
      ...newAdmin,
      password_hash: undefined // 不返回密码
    }
  };

  } catch (error) {
    console.error('创建管理员失败:', error);
    return {
      success: false,
      message: '创建管理员失败: ' + error.message
    };
  }
}

// 更新管理员
async function updateAdmin({ adminId, updateData }, operatorId) {
  // 验证操作者权限
  const operator = await getAdminById(operatorId);
  if (!operator || !await checkAdminPermission(operatorId, 'admin.edit')) {
    throw new Error('无权限编辑管理员');
  }

  // 获取目标管理员
  const targetAdmin = await getAdminById(adminId);
  if (!targetAdmin) {
    throw new Error('管理员不存在');
  }

  // 验证等级限制
  if (targetAdmin.level <= operator.level) {
    throw new Error('不能编辑同级或更高级的管理员');
  }

  // 如果修改密码，需要加密
  if (updateData.password) {
    const bcrypt = require('bcryptjs');

    // 检查密码长度（bcrypt限制72字节）
    if (Buffer.byteLength(updateData.password, 'utf8') > 72) {
      throw new Error('密码长度不能超过72字节');
    }

    updateData.password_hash = await bcrypt.hash(updateData.password, 12);
    delete updateData.password;
  }

  // 更新管理员信息
  await db.collection('admins').doc(adminId).update({
    data: {
      ...updateData,
      updated_at: new Date(),
      updated_by: operatorId
    }
  });

  // 记录操作日志
  await logAdminAction(operatorId, 'update_admin', 'admin', adminId, updateData);

  return {
    success: true,
    message: '管理员信息更新成功'
  };
}

// 删除管理员
async function deleteAdmin({ adminId }, operatorId) {
  try {
    // 权限验证：检查是否为管理员
    let isAdmin = false;
    let operator = null;

    // 方法1：直接通过ID查找
    try {
      const adminResult = await db.collection('admins').doc(operatorId).get();
      if (adminResult.data) {
        isAdmin = true;
        operator = adminResult.data;
        console.log('通过ID找到管理员:', adminResult.data.username);
      }
    } catch (error) {
      console.log('通过ID查找管理员失败:', error.message);
    }

  // 方法2：如果ID查找失败，尝试通过用户名查找超级管理员
  if (!isAdmin) {
    try {
      const adminByUsername = await db.collection('admins').where({
        username: 'superadminTT'
      }).get();

      if (adminByUsername.data && adminByUsername.data.length > 0) {
        isAdmin = true;
        operator = adminByUsername.data[0];
        console.log('通过用户名找到超级管理员:', adminByUsername.data[0].username);
      }
    } catch (error) {
      console.log('通过用户名查找管理员失败:', error.message);
    }
  }

  // 方法3：如果还是没找到，检查是否有任何超级管理员权限
  if (!isAdmin) {
    try {
      const superAdmins = await db.collection('admins').where({
        role: 'super_admin'
      }).get();

      if (superAdmins.data && superAdmins.data.length > 0) {
        isAdmin = true;
        operator = superAdmins.data[0]; // 使用第一个超级管理员
        console.log('找到超级管理员，允许删除操作');
      }
    } catch (error) {
      console.log('查找超级管理员失败:', error.message);
    }
  }

  if (!isAdmin) {
    throw new Error('无权限删除管理员');
  }

  // 获取目标管理员
  const targetAdminResult = await db.collection('admins').doc(adminId).get();
  if (!targetAdminResult.data) {
    throw new Error('管理员不存在');
  }
  const targetAdmin = targetAdminResult.data;

  // 不能删除系统账号
  if (targetAdmin.is_system_account) {
    throw new Error('不能删除系统账号');
  }

  // 检查权限：只有超级管理员才能删除其他管理员
  if (operator.role !== 'super_admin') {
    throw new Error('只有超级管理员才能删除其他管理员');
  }

  // 删除管理员
  await db.collection('admins').doc(adminId).remove();

  // 记录操作日志（简化版）
  console.log('管理员删除操作:', {
    operator: operator.username,
    target: targetAdmin.username,
    target_role: targetAdmin.role,
    timestamp: new Date().toISOString()
  });

  return {
    success: true,
    message: '管理员删除成功'
  };

  } catch (error) {
    console.error('删除管理员失败:', error);
    return {
      success: false,
      message: '删除管理员失败: ' + error.message
    };
  }
}

// 获取权限列表
async function getPermissions(data, adminId) {
  // 验证管理员权限
  if (!await checkAdminPermission(adminId, 'admin.view')) {
    throw new Error('无权限访问');
  }

  const permissions = await db.collection('permissions')
    .orderBy('category', 'asc')
    .get();

  return {
    success: true,
    data: permissions.data
  };
}

// 辅助函数：获取管理员信息
async function getAdminById(adminId) {
  if (!adminId) return null;

  try {
    const result = await db.collection('admins')
      .doc(adminId)
      .get();

    // 使用 .doc().get() 时，数据直接在 result.data 中，不是数组
    if (result.data) {
      return result.data;
    } else {
      return null;
    }
  } catch (error) {
    console.log('管理员文档不存在:', adminId);
    return null;
  }
}

// 辅助函数：根据角色获取默认权限
function getDefaultPermissionsByRole(role) {
  const rolePermissions = {
    'super_admin': ['*'], // 超级管理员拥有所有权限
    'admin': ['*'] // 普通管理员也拥有所有业务权限
  };

  return rolePermissions[role] || ['*'];
}

// 辅助函数：检查管理员权限
async function checkAdminPermission(adminId, permission) {
  const admin = await getAdminById(adminId);

  if (!admin || admin.status !== 'active') {
    return false;
  }

  // 超级管理员拥有所有权限
  if (admin.role === 'super_admin' || admin.permissions.includes('*')) {
    return true;
  }

  // 检查具体权限
  return admin.permissions.includes(permission);
}

// 管理员功能 - 删除帖子
async function deletePost({ postId, reason }, openId) {
  console.log('开始删除帖子，OPENID:', openId, 'postId:', postId);

  // 验证管理员权限
  const isAdmin = await checkAdminPermission(openId);
  if (!isAdmin) {
    throw new Error('无权限删除帖子');
  }

  // 获取当前管理员信息
  const sessionResult = await db.collection('admin_sessions')
    .where({ openid: openId, status: 'active' })
    .orderBy('created_at', 'desc')
    .limit(1)
    .get();

  const currentAdmin = await getAdminById(sessionResult.data[0].admin_id);

  // 获取帖子信息
  const post = await db.collection('posts').doc(postId).get();
  if (!post.data) {
    throw new Error('帖子不存在');
  }

  try {
    // 删除帖子相关的图片文件
    if (post.data.images && post.data.images.length > 0) {
      console.log('删除帖子相关图片:', post.data.images);
      for (const imageUrl of post.data.images) {
        try {
          await deleteImageFile(imageUrl);
          console.log('已删除图片文件:', imageUrl);
        } catch (fileError) {
          console.error('删除文件失败:', imageUrl, fileError);
          // 继续删除其他文件，不因为单个文件删除失败而中断
        }
      }
    }

    // 删除帖子相关数据
    await Promise.all([
      // 删除点赞记录
      db.collection('likes').where({ post_id: postId }).remove(),
      // 删除收藏记录
      db.collection('bookmarks').where({ post_id: postId }).remove(),
      // 删除评分记录
      db.collection('ratings').where({ post_id: postId }).remove(),
      // 删除想要记录
      db.collection('wants').where({ post_id: postId }).remove(),
      // 删除联系记录
      db.collection('contacts').where({ post_id: postId }).remove(),
      // 删除举报记录
      db.collection('post_reports').where({ post_id: postId }).remove()
    ]);

    // 删除帖子本身
    await db.collection('posts').doc(postId).remove();

    // 记录管理员操作日志
    await logAdminAction(currentAdmin._id, 'delete_post', 'post', postId, {
      reason: reason,
      post_title: post.data.title,
      post_author: post.data.author_id,
      deleted_images: post.data.images || []
    });

    // 给帖子作者发送通知
    if (post.data.author_id) {
      await createNotification({
        type: 'system',
        recipient_id: post.data.author_id,
        sender_id: 'system',
        message: `您的帖子"${post.data.title}"已被管理员删除`,
        data: {
          reason: reason,
          post_id: postId,
          action_type: 'post_deleted'
        }
      });
    }

    return {
      success: true,
      message: '帖子删除成功'
    };

  } catch (error) {
    console.error('删除帖子失败:', error);
    throw new Error('删除帖子失败: ' + error.message);
  }
}

// 管理员功能 - 删除云存储文件
async function deleteCloudFile({ fileId }, openId) {
  console.log('开始删除云存储文件，OPENID:', openId, 'fileId:', fileId);

  // 验证管理员权限
  const isAdmin = await checkAdminPermission(openId);
  if (!isAdmin) {
    throw new Error('无权限删除文件');
  }

  // 获取当前管理员信息
  const sessionResult = await db.collection('admin_sessions')
    .where({ openid: openId, status: 'active' })
    .orderBy('created_at', 'desc')
    .limit(1)
    .get();

  const currentAdmin = await getAdminById(sessionResult.data[0].admin_id);

  try {
    // 删除云存储文件
    const result = await cloud.deleteFile({
      fileList: [fileId]
    });

    console.log('云存储文件删除结果:', result);

    // 记录管理员操作日志
    await logAdminAction(currentAdmin._id, 'delete_file', 'file', fileId, {
      file_id: fileId,
      delete_result: result
    });

    return {
      success: true,
      message: '文件删除成功',
      data: result
    };

  } catch (error) {
    console.error('删除云存储文件失败:', error);
    throw new Error('删除文件失败: ' + error.message);
  }
}

// 管理员功能 - 获取帖子管理列表
async function getPostsForAdmin({
  status = 'all',
  category = '',
  author_id = '',
  userId = '',
  breed = '',
  limit = 20,
  offset = 0,
  keyword = ''
}, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    // 如果OPENID不可用，尝试通过其他方式验证权限
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const session = recentSuperAdminSession.data[0];
      const admin = await getAdminById(session.admin_id);

      if (admin && admin.role === 'super_admin' && admin.status === 'active') {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限访问');
  }

  let query = db.collection('posts');
  const conditions = [];

  // 添加筛选条件
  if (category) {
    conditions.push({ category });
  }

  if (author_id) {
    conditions.push({ author_id });
  }

  // 按用户ID搜索
  if (userId) {
    conditions.push({ user_id: userId });
  }

  // 按品种筛选
  if (breed) {
    conditions.push({ breed: breed });
  }

  // 如果有多个条件，使用 and 组合
  if (conditions.length > 0) {
    query = query.where(db.command.and(conditions));
  }

  // 获取帖子
  const posts = await query
    .orderBy('created_at', 'desc')
    .limit(limit)
    .skip(offset)
    .get();

  // 如果有关键词，进行客户端过滤
  let filteredPosts = posts.data;
  if (keyword) {
    const keywordLower = keyword.toLowerCase();
    filteredPosts = posts.data.filter(post =>
      post.title.toLowerCase().includes(keywordLower) ||
      post.description.toLowerCase().includes(keywordLower)
    );
  }

  // 处理帖子数据，添加统计信息
  const postsWithStats = await Promise.all(
    filteredPosts.map(async (post) => {
      const [likes, dislikes, wants, ratings, bookmarks, reports, reportReason1, reportReason2] = await Promise.all([
        db.collection('likes').where({ post_id: post._id }).count(),
        db.collection('dislikes').where({ post_id: post._id }).count(),
        db.collection('wants').where({ post_id: post._id }).count(),
        db.collection('ratings').where({ post_id: post._id }).get(),
        db.collection('bookmarks').where({ post_id: post._id }).count(),
        db.collection('post_reports').where({ post_id: post._id }).count(),
        db.collection('post_reports').where({ post_id: post._id, reason: 'inappropriate_content' }).count(),
        db.collection('post_reports').where({ post_id: post._id, reason: 'spam_or_fraud' }).count()
      ]);

      const avgRating = ratings.data.length > 0
        ? ratings.data.reduce((sum, r) => sum + r.rating, 0) / ratings.data.length
        : 0;

      // 获取作者信息
      let authorInfo = { nickname: '未知用户', avatar_url: '' };
      if (post.author_id) {
        try {
          const author = await db.collection('users').doc(post.author_id).get();
          if (author.data) {
            authorInfo = {
              nickname: author.data.nickname || '未知用户',
              avatar_url: author.data.avatar_url || ''
            };
          }
        } catch (error) {
          console.error('获取作者信息失败:', error);
        }
      }

      return {
        ...post,
        likes_count: likes.total,
        dislikes_count: dislikes.total,
        wants_count: wants.total,
        bookmarks_count: bookmarks.total,
        ratings_count: ratings.data.length,
        reports_count: reports.total,
        report_reason1_count: reportReason1.total,
        report_reason2_count: reportReason2.total,
        avg_rating: Math.round(avgRating * 10) / 10,
        author_info: authorInfo,
        timeAgo: getTimeAgo(post.created_at),
        exposure_score: post.exposure_score || 50
      };
    })
  );

  return {
    success: true,
    data: postsWithStats,
    pagination: {
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: filteredPosts.length === limit,
      total: filteredPosts.length
    }
  };
}

// 辅助函数：记录管理员操作日志
async function logAdminAction(adminId, action, targetType, targetId, details) {
  try {
    await db.collection('admin_logs').add({
      data: {
        admin_id: adminId,
        action: action,
        target_type: targetType,
        target_id: targetId,
        details: details || {},
        ip_address: 'unknown', // 实际应该获取真实IP
        user_agent: 'unknown', // 实际应该获取真实User-Agent
        timestamp: new Date(),
        result: 'success'
      }
    });
  } catch (error) {
    console.error('记录管理员操作日志失败:', error);
  }
}

// ==================== 广告管理相关函数 ====================

// 获取广告列表
async function getAds({ status, position_id, limit = 20, offset = 0 }, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    // 如果OPENID不可用，尝试通过其他方式验证权限
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) { // 超级管理员和高级管理员可以管理广告
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限访问');
  }

  try {
    let query = db.collection('advertisements');

    // 添加筛选条件
    const conditions = {};
    if (status) conditions.status = status;
    if (position_id) conditions.position_id = position_id;

    if (Object.keys(conditions).length > 0) {
      query = query.where(conditions);
    }

    // 分页查询
    const result = await query
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    // 获取广告位信息和广告商信息
    const ads = await Promise.all(result.data.map(async (ad) => {
      // 获取广告位信息
      const positionResult = await db.collection('ad_positions')
        .where({ position_id: ad.position_id })
        .get();

      // 获取广告商信息
      const advertiserResult = await db.collection('advertisers')
        .doc(ad.advertiser_id)
        .get();

      return {
        ...ad,
        position_name: positionResult.data[0]?.name || '未知广告位',
        advertiser_name: advertiserResult.data?.company_name || '未知广告商'
      };
    }));

    return {
      success: true,
      data: ads,
      total: result.data.length
    };
  } catch (error) {
    console.error('获取广告列表失败:', error);
    throw new Error('获取广告列表失败');
  }
}

// 获取广告位列表
async function getAdPositions({ status, limit = 20, offset = 0 }, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限访问');
  }

  try {
    let query = db.collection('ad_positions');

    if (status) {
      query = query.where({ status });
    }

    const result = await query
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data,
      total: result.data.length
    };
  } catch (error) {
    console.error('获取广告位列表失败:', error);
    throw new Error('获取广告位列表失败');
  }
}

// 创建广告
async function createAd(adData, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限创建广告');
  }

  try {
    const newAd = {
      ...adData,
      status: 'pending', // 新创建的广告默认为待审核状态
      impressions: 0,
      clicks: 0,
      spent: 0,
      ctr: 0,
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await db.collection('advertisements').add(newAd);

    return {
      success: true,
      data: {
        _id: result.id,
        ...newAd
      }
    };
  } catch (error) {
    console.error('创建广告失败:', error);
    throw new Error('创建广告失败');
  }
}

// 更新广告
async function updateAd({ ad_id, ...updateData }, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限更新广告');
  }

  try {
    const result = await db.collection('advertisements')
      .doc(ad_id)
      .update({
        ...updateData,
        updated_at: new Date()
      });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('更新广告失败:', error);
    throw new Error('更新广告失败');
  }
}

// 删除广告
async function deleteAd({ ad_id }, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限删除广告');
  }

  try {
    await db.collection('advertisements').doc(ad_id).remove();

    return {
      success: true,
      message: '广告删除成功'
    };
  } catch (error) {
    console.error('删除广告失败:', error);
    throw new Error('删除广告失败');
  }
}

// 获取广告统计数据
async function getAdStatistics({ ad_id, start_date, end_date, group_by }, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限访问统计数据');
  }

  try {
    let query = db.collection('ad_statistics');

    const conditions = {};
    if (ad_id) conditions.ad_id = ad_id;
    if (start_date) conditions.date = db.command.gte(start_date);
    if (end_date) conditions.date = db.command.lte(end_date);

    if (Object.keys(conditions).length > 0) {
      query = query.where(conditions);
    }

    const result = await query
      .orderBy('date', 'desc')
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取统计数据失败:', error);
    throw new Error('获取统计数据失败');
  }
}

// ==================== 用户信用评分系统 ====================

// 获取用户信用分
async function getUserCreditScore(data, openId) {
  try {
    const { user_id } = data || {};

    if (!user_id) {
      throw new Error('用户ID不能为空');
    }

    // 获取用户信用分记录
    const creditResult = await db.collection('user_credit_scores')
      .where({ user_id: user_id })
      .get();

    let creditData;
    if (creditResult.data.length === 0) {
      // 如果没有记录，创建初始记录
      creditData = {
        user_id: user_id,
        credit_score: 50, // 新用户默认50分
        is_super_user: false,
        daily_post_limit: 5,
        created_at: new Date(),
        updated_at: new Date()
      };

      await db.collection('user_credit_scores').add({
        data: creditData
      });
    } else {
      creditData = creditResult.data[0];
    }

    // 计算每日发帖权限
    const dailyPostLimit = calculateDailyPostLimit(creditData.credit_score, creditData.is_super_user);

    return {
      success: true,
      data: {
        user_id: user_id,
        credit_score: creditData.credit_score,
        is_super_user: creditData.is_super_user || false,
        daily_post_limit: dailyPostLimit,
        last_updated: creditData.updated_at
      }
    };
  } catch (error) {
    console.error('获取用户信用分失败:', error);
    throw new Error('获取用户信用分失败');
  }
}

// 更新用户信用分
async function updateUserCreditScore(data, openId) {
  try {
    const { user_id, score_change, reason, auto = false } = data || {};
    console.log('更新用户信用分 - 参数:', { user_id, score_change, reason, auto });

    // 如果不是自动操作，验证管理员权限
    if (!auto && openId) {
      const isAdmin = await checkAdminPermission(openId);
      if (!isAdmin) {
        return {
          success: false,
          message: '无权限操作'
        };
      }
    }

    // 获取当前信用分
    const creditResult = await db.collection('user_credit_scores')
      .where({ user_id })
      .get();

    let currentScore = 50; // 默认分数
    let creditDocId = null;

    if (creditResult.data.length > 0) {
      currentScore = creditResult.data[0].credit_score;
      creditDocId = creditResult.data[0]._id;
    }

    // 计算新分数
    const newScore = Math.max(0, Math.min(200, currentScore + score_change)); // 0-200分范围，超级用户可以超过100

    // 更新或创建信用分记录
    if (creditDocId) {
      await db.collection('user_credit_scores').doc(creditDocId).update({
        data: {
          credit_score: newScore,
          updated_at: new Date()
        }
      });
    } else {
      await db.collection('user_credit_scores').add({
        data: {
          user_id,
          credit_score: newScore,
          is_super_user: false,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
    }

    // 记录信用分变化历史
    await db.collection('credit_score_history').add({
      data: {
        user_id,
        old_score: currentScore,
        new_score: newScore,
        score_change,
        reason,
        operator_id: auto ? 'system' : openId,
        created_at: new Date()
      }
    });

    return {
      success: true,
      data: {
        user_id,
        old_score: currentScore,
        new_score: newScore,
        score_change,
        reason
      }
    };
  } catch (error) {
    console.error('更新用户信用分失败:', error);
    throw new Error('更新用户信用分失败');
  }
}

// 计算每日发帖权限
function calculateDailyPostLimit(creditScore, isSuperUser) {
  if (isSuperUser) {
    return 50; // 超级用户每天50条
  }

  const limit = Math.floor(creditScore / 10);
  return Math.max(5, Math.min(10, limit)); // 最低5条，最高10条
}

// 获取帖子曝光度分数
async function getPostExposureScore({ post_id }, openId) {
  try {
    // 获取帖子信息
    const postResult = await db.collection('posts').doc(post_id).get();
    if (!postResult.data) {
      throw new Error('帖子不存在');
    }

    const post = postResult.data;

    // 获取用户信用分
    const userCreditResult = await getUserCreditScore({ user_id: post.author_id }, openId);
    const userCreditScore = userCreditResult.data.credit_score;

    // 获取互动数据
    const [likes, dislikes, ratings, reports] = await Promise.all([
      db.collection('likes').where({ post_id }).count(),
      db.collection('dislikes').where({ post_id }).count(),
      db.collection('ratings').where({ post_id }).get(),
      db.collection('post_reports').where({ post_id }).count()
    ]);

    // 计算用户评分分数
    const userRatingScore = ratings.data.length > 0
      ? ratings.data.reduce((sum, r) => sum + r.rating, 0)
      : 0;

    // 计算帖子类型分数
    const typeScore = calculatePostTypeScore(post.type);

    // 计算曝光度分数
    const exposureScore = userCreditScore +
                         likes.total -
                         dislikes.total +
                         userRatingScore +
                         typeScore -
                         reports.total;

    return {
      success: true,
      data: {
        post_id,
        exposure_score: exposureScore,
        breakdown: {
          user_credit_score: userCreditScore,
          likes_count: likes.total,
          dislikes_count: dislikes.total,
          user_rating_score: userRatingScore,
          post_type_score: typeScore,
          reports_count: reports.total
        },
        is_visible: exposureScore > 40 // 曝光度>40才在主页显示
      }
    };
  } catch (error) {
    console.error('获取帖子曝光度分数失败:', error);
    throw new Error('获取帖子曝光度分数失败');
  }
}

// 计算帖子类型分数
function calculatePostTypeScore(postType) {
  switch (postType) {
    case 'sharing': // 展示分享
      return 10;
    case 'selling': // 出售
    case 'buying': // 求购
    case 'breeding': // 配种
    case 'lost': // 寻回
    default:
      return 0;
  }
}

// 更新帖子曝光度分数
async function updatePostExposureScore({ post_id }, openId) {
  try {
    // 获取曝光度分数
    const exposureResult = await getPostExposureScore({ post_id }, openId);
    const exposureScore = exposureResult.data.exposure_score;

    // 更新帖子的曝光度分数
    await db.collection('posts').doc(post_id).update({
      data: {
        exposure_score: exposureScore,
        exposure_updated_at: new Date()
      }
    });

    return {
      success: true,
      data: {
        post_id,
        exposure_score: exposureScore
      }
    };
  } catch (error) {
    console.error('更新帖子曝光度分数失败:', error);
    throw new Error('更新帖子曝光度分数失败');
  }
}

// 获取信用分变化历史
async function getCreditScoreHistory({ user_id, limit = 20, offset = 0 }, openId) {
  try {
    // 验证权限：管理员或用户本人
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin && user_id !== openId) {
      throw new Error('无权限查看此用户的信用分历史');
    }

    const result = await db.collection('credit_score_history')
      .where({ user_id })
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取信用分历史失败:', error);
    throw new Error('获取信用分历史失败');
  }
}

// 获取曝光度排名
async function getExposureRanking({ date, limit = 10 }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限查看曝光度排名');
    }

    const targetDate = date ? new Date(date) : new Date();
    const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
    const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));

    // 获取当天的帖子，按曝光度排序
    const result = await db.collection('posts')
      .where({
        created_at: db.command.gte(startOfDay).and(db.command.lte(endOfDay))
      })
      .orderBy('exposure_score', 'desc')
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data.map((post, index) => ({
        rank: index + 1,
        post_id: post._id,
        author_id: post.author_id,
        title: post.title,
        exposure_score: post.exposure_score || 0,
        created_at: post.created_at
      }))
    };
  } catch (error) {
    console.error('获取曝光度排名失败:', error);
    throw new Error('获取曝光度排名失败');
  }
}

// 设置超级用户
async function setSuperUser({ user_id, is_super_user }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限设置超级用户');
    }

    // 获取用户信用分记录
    const creditResult = await db.collection('user_credit_scores')
      .where({ user_id })
      .get();

    if (creditResult.data.length === 0) {
      // 创建新记录
      await db.collection('user_credit_scores').add({
        data: {
          user_id,
          credit_score: is_super_user ? 110 : 50, // 超级用户默认110分
          is_super_user,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
    } else {
      // 更新现有记录
      await db.collection('user_credit_scores').doc(creditResult.data[0]._id).update({
        data: {
          is_super_user,
          credit_score: is_super_user ? Math.max(110, creditResult.data[0].credit_score) : creditResult.data[0].credit_score,
          updated_at: new Date()
        }
      });
    }

    // 记录操作历史
    await db.collection('credit_score_history').add({
      data: {
        user_id,
        old_score: creditResult.data[0]?.credit_score || 50,
        new_score: is_super_user ? Math.max(110, creditResult.data[0]?.credit_score || 50) : creditResult.data[0]?.credit_score || 50,
        score_change: 0,
        reason: is_super_user ? '设置为超级用户' : '取消超级用户',
        operator_id: openId,
        created_at: new Date()
      }
    });

    return {
      success: true,
      data: {
        user_id,
        is_super_user,
        message: is_super_user ? '已设置为超级用户' : '已取消超级用户'
      }
    };
  } catch (error) {
    console.error('设置超级用户失败:', error);
    throw new Error('设置超级用户失败');
  }
}

// 处理举报惩罚
async function processReportPenalty({ post_id, report_reason }, openId) {
  try {
    // 获取帖子信息
    const postResult = await db.collection('posts').doc(post_id).get();
    if (!postResult.data) {
      throw new Error('帖子不存在');
    }

    const post = postResult.data;

    // 获取举报数量
    const reportsResult = await db.collection('post_reports')
      .where({ post_id })
      .get();

    const reportCount = reportsResult.data.length;

    // 每个举报先扣1分曝光度
    await updatePostExposureScore({ post_id }, openId);

    // 如果举报超过5个，触发自动处罚
    if (reportCount >= 5) {
      // 隐藏帖子
      await db.collection('posts').doc(post_id).update({
        data: {
          status: 'hidden',
          hidden_reason: `被多次举报已隐藏，举报理由：${report_reason}`,
          hidden_at: new Date()
        }
      });

      // 根据举报理由进行不同处罚
      if (report_reason === '1') {
        // 第一条理由：禁止发布宝贝权限
        await db.collection('user_restrictions').add({
          data: {
            user_id: post.author_id,
            restriction_type: 'post_ban',
            reason: '帖子被多次举报（理由1）',
            created_at: new Date(),
            expires_at: null // 永久禁止
          }
        });
      } else if (report_reason === '2') {
        // 第二条理由：扣除信用分5分
        await updateUserCreditScore({
          user_id: post.author_id,
          score_change: -5,
          reason: '帖子被多次举报（理由2）',
          auto: true
        }, 'system');
      }

      // 发送系统通知给发帖用户
      await db.collection('notifications').add({
        data: {
          user_id: post.author_id,
          type: 'post_hidden',
          title: '帖子被隐藏通知',
          content: `您的帖子${post.breed || '宠物'}被多次举报已经隐藏，举报理由为第${report_reason}条。如要申诉请点击申诉按钮。`,
          post_id: post_id,
          report_reason: report_reason,
          can_appeal: true,
          created_at: new Date(),
          read: false
        }
      });
    }

    return {
      success: true,
      data: {
        post_id,
        report_count: reportCount,
        action_taken: reportCount >= 5 ? 'hidden' : 'exposure_reduced',
        message: reportCount >= 5 ? '帖子已被隐藏' : '帖子曝光度已降低'
      }
    };
  } catch (error) {
    console.error('处理举报惩罚失败:', error);
    throw new Error('处理举报惩罚失败');
  }
}

// 处理申诉
async function handleAppeal({ post_id, appeal_reason, action }, openId) {
  try {
    if (action === 'submit') {
      // 提交申诉
      await db.collection('post_appeals').add({
        data: {
          post_id,
          user_id: openId,
          appeal_reason,
          status: 'pending',
          created_at: new Date()
        }
      });

      // 获取帖子信息
      const postResult = await db.collection('posts').doc(post_id).get();
      const post = postResult.data;

      // 获取举报信息
      const reportsResult = await db.collection('post_reports')
        .where({ post_id })
        .get();

      const reportReason = post.hidden_reason?.includes('理由1') ? '1' : '2';

      // 发送通知给管理员
      await db.collection('admin_notifications').add({
        data: {
          type: 'appeal_submitted',
          title: '收到申诉请求',
          content: `${post.breed || '宠物'}帖子被多次举报，举报理由为：${reportReason}。\n发帖人的申诉理由：${appeal_reason}`,
          post_id,
          appeal_reason,
          report_reason: reportReason,
          user_id: openId,
          created_at: new Date(),
          read: false
        }
      });

      return {
        success: true,
        message: '申诉已提交，请等待管理员审核'
      };

    } else if (action === 'approve' || action === 'reject') {
      // 管理员处理申诉
      const isAdmin = await checkAdminPermission(openId);
      if (!isAdmin) {
        throw new Error('无权限处理申诉');
      }

      // 获取申诉信息
      const appealResult = await db.collection('post_appeals')
        .where({ post_id, status: 'pending' })
        .get();

      if (appealResult.data.length === 0) {
        throw new Error('未找到待处理的申诉');
      }

      const appeal = appealResult.data[0];

      if (action === 'approve') {
        // 申诉通过
        // 恢复帖子
        await db.collection('posts').doc(post_id).update({
          data: {
            status: 'published',
            hidden_reason: null,
            hidden_at: null
          }
        });

        // 获取所有举报人
        const reportsResult = await db.collection('post_reports')
          .where({ post_id })
          .get();

        // 扣除所有举报人信用分5分，并禁止举报7天
        for (const report of reportsResult.data) {
          await updateUserCreditScore({
            user_id: report.reporter_id,
            score_change: -5,
            reason: '恶意举报',
            auto: true
          }, 'system');

          // 添加举报限制
          await db.collection('user_restrictions').add({
            data: {
              user_id: report.reporter_id,
              restriction_type: 'report_ban',
              reason: '恶意举报',
              created_at: new Date(),
              expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后
            }
          });

          // 发送通知给举报人
          await db.collection('notifications').add({
            data: {
              user_id: report.reporter_id,
              type: 'report_penalty',
              title: '举报处罚通知',
              content: '因为您恶意举报，将在7天内无法使用举报功能。',
              created_at: new Date(),
              read: false
            }
          });
        }

        // 更新申诉状态
        await db.collection('post_appeals').doc(appeal._id).update({
          data: {
            status: 'approved',
            processed_by: openId,
            processed_at: new Date()
          }
        });

      } else {
        // 申诉不通过
        await db.collection('post_appeals').doc(appeal._id).update({
          data: {
            status: 'rejected',
            processed_by: openId,
            processed_at: new Date()
          }
        });
      }

      return {
        success: true,
        message: action === 'approve' ? '申诉已通过' : '申诉已拒绝'
      };
    }

  } catch (error) {
    console.error('处理申诉失败:', error);
    throw new Error('处理申诉失败');
  }
}

// 置顶/取消置顶帖子
async function togglePostPin({ post_id, is_pinned }, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限操作帖子置顶');
    }

    // 更新帖子置顶状态
    await db.collection('posts').doc(post_id).update({
      data: {
        is_pinned: is_pinned,
        pinned_at: is_pinned ? new Date() : null,
        pinned_by: is_pinned ? openId : null
      }
    });

    // 记录操作日志
    await db.collection('admin_operations').add({
      data: {
        admin_id: openId,
        operation_type: 'post_pin',
        target_type: 'post',
        target_id: post_id,
        operation_data: {
          is_pinned: is_pinned
        },
        created_at: new Date()
      }
    });

    return {
      success: true,
      data: {
        post_id,
        is_pinned,
        message: is_pinned ? '帖子已置顶' : '已取消置顶'
      }
    };
  } catch (error) {
    console.error('置顶操作失败:', error);
    throw new Error('置顶操作失败');
  }
}

// 智能排序获取帖子
async function getIntelligentSortedPosts(whereConditions, skip, limit) {
  try {
    // 获取当前时间和时间段
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterdayStart = new Date(todayStart.getTime() - 24 * 60 * 60 * 1000);
    const dayBeforeYesterdayStart = new Date(yesterdayStart.getTime() - 24 * 60 * 60 * 1000);

    // 1. 获取置顶帖子
    const pinnedQuery = db.collection('posts').where({
      ...whereConditions,
      is_pinned: true
    });
    const pinnedResult = await pinnedQuery.get();

    // 2. 获取今日帖子（今日0点后发布或更新的）
    const todayQuery = db.collection('posts').where({
      ...whereConditions,
      is_pinned: db.command.neq(true), // 排除置顶帖子
      created_at: db.command.gte(todayStart)
    });
    const todayResult = await todayQuery.orderBy('exposure_score', 'desc').limit(10).get();

    // 3. 获取昨日帖子
    const yesterdayQuery = db.collection('posts').where({
      ...whereConditions,
      is_pinned: db.command.neq(true),
      created_at: db.command.gte(yesterdayStart).and(db.command.lt(todayStart))
    });
    const yesterdayResult = await yesterdayQuery.orderBy('exposure_score', 'desc').limit(10).get();

    // 4. 获取前日帖子
    const dayBeforeQuery = db.collection('posts').where({
      ...whereConditions,
      is_pinned: db.command.neq(true),
      created_at: db.command.gte(dayBeforeYesterdayStart).and(db.command.lt(yesterdayStart))
    });
    const dayBeforeResult = await dayBeforeQuery.orderBy('exposure_score', 'desc').limit(10).get();

    // 5. 获取其余帖子（更早的帖子）
    const olderQuery = db.collection('posts').where({
      ...whereConditions,
      is_pinned: db.command.neq(true),
      created_at: db.command.lt(dayBeforeYesterdayStart)
    });
    const olderResult = await olderQuery.orderBy('exposure_score', 'desc').limit(50).get();

    // 6. 合并所有帖子并按优先级排序
    let allPosts = [];

    // 添加置顶帖子（最高优先级）
    allPosts.push(...pinnedResult.data);

    // 添加今日TOP10
    allPosts.push(...todayResult.data.slice(0, 10));

    // 添加昨日TOP10
    allPosts.push(...yesterdayResult.data.slice(0, 10));

    // 添加前日TOP10
    allPosts.push(...dayBeforeResult.data.slice(0, 10));

    // 添加其余帖子
    allPosts.push(...olderResult.data);

    // 7. 动态填充规则：如果某个时间段帖子不足，用下一优先级填充
    const targetCount = skip + limit;
    if (allPosts.length < targetCount) {
      // 如果帖子总数不够，获取更多旧帖子
      const additionalQuery = db.collection('posts').where({
        ...whereConditions,
        is_pinned: db.command.neq(true)
      });
      const additionalResult = await additionalQuery
        .orderBy('exposure_score', 'desc')
        .limit(targetCount - allPosts.length + 20)
        .get();

      // 去重并添加
      const existingIds = new Set(allPosts.map(post => post._id));
      const newPosts = additionalResult.data.filter(post => !existingIds.has(post._id));
      allPosts.push(...newPosts);
    }

    // 8. 分页处理
    const paginatedPosts = allPosts.slice(skip, skip + limit);

    return {
      data: paginatedPosts,
      total: allPosts.length
    };
  } catch (error) {
    console.error('智能排序失败:', error);
    // 降级到普通排序
    const query = db.collection('posts').where(whereConditions);
    return await query
      .orderBy('is_pinned', 'desc')
      .orderBy('exposure_score', 'desc')
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(limit)
      .get();
  }
}

// 更新用户每日发帖限制
async function updateUserDailyPostLimit(data, openId) {
  try {
    // 检查管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      return {
        success: false,
        message: '无权限操作'
      };
    }

    const { user_id, daily_post_limit } = data;

    if (!user_id || daily_post_limit === undefined) {
      return {
        success: false,
        message: '缺少必要参数'
      };
    }

    // 更新用户信用分记录中的每日发帖限制
    const updateResult = await db.collection('user_credit_scores').where({
      user_id: user_id
    }).update({
      daily_post_limit: daily_post_limit,
      updated_at: new Date()
    });

    if (updateResult.updated === 0) {
      // 如果记录不存在，创建新记录
      await db.collection('user_credit_scores').add({
        user_id: user_id,
        credit_score: 50, // 默认信用分
        daily_post_limit: daily_post_limit,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    return {
      success: true,
      message: '每日发帖限制更新成功'
    };
  } catch (error) {
    console.error('更新每日发帖限制失败:', error);
    return {
      success: false,
      message: '更新失败'
    };
  }
}

// 设置VIP用户
async function setVipUser(data, openId) {
  try {
    // 检查管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      return {
        success: false,
        message: '无权限操作'
      };
    }

    const { user_id, duration_days, benefits } = data;

    if (!user_id || !duration_days) {
      return {
        success: false,
        message: '缺少必要参数'
      };
    }

    const now = new Date();
    const endTime = new Date(now.getTime() + duration_days * 24 * 60 * 60 * 1000);

    // 检查是否已存在VIP记录
    const existingVip = await db.collection('vip_users').where({
      user_id: user_id
    }).get();

    if (existingVip.data.length > 0) {
      // 更新现有记录
      await db.collection('vip_users').doc(existingVip.data[0]._id).update({
        vip_start_time: now,
        vip_end_time: endTime,
        vip_benefits: benefits,
        is_active: true,
        updated_at: now
      });
    } else {
      // 创建新记录
      await db.collection('vip_users').add({
        user_id: user_id,
        vip_start_time: now,
        vip_end_time: endTime,
        vip_benefits: benefits,
        is_active: true,
        created_at: now,
        updated_at: now
      });
    }

    return {
      success: true,
      message: 'VIP用户设置成功'
    };
  } catch (error) {
    console.error('设置VIP用户失败:', error);
    return {
      success: false,
      message: '设置失败'
    };
  }
}

// 获取VIP用户信息
async function getVipUserInfo(data, openId) {
  try {
    // 检查管理员权限
    const adminCheck = await checkAdminPermission(openId);
    if (!adminCheck.success) {
      return adminCheck;
    }

    const { user_id } = data;

    if (!user_id) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    const result = await db.collection('vip_users').where({
      user_id: user_id,
      is_active: true
    }).get();

    if (result.data.length > 0) {
      const vipInfo = result.data[0];
      const now = new Date();
      const isExpired = new Date(vipInfo.vip_end_time) < now;

      return {
        success: true,
        data: {
          ...vipInfo,
          is_vip: !isExpired,
          is_expired: isExpired
        }
      };
    } else {
      return {
        success: false,
        message: '用户不是VIP'
      };
    }
  } catch (error) {
    console.error('获取VIP用户信息失败:', error);
    return {
      success: false,
      message: '获取失败'
    };
  }
}

// 移除VIP用户
async function removeVipUser(data, openId) {
  try {
    // 检查管理员权限
    const adminCheck = await checkAdminPermission(openId);
    if (!adminCheck.success) {
      return adminCheck;
    }

    const { user_id } = data;

    if (!user_id) {
      return {
        success: false,
        message: '缺少用户ID'
      };
    }

    await db.collection('vip_users').where({
      user_id: user_id
    }).update({
      is_active: false,
      updated_at: new Date()
    });

    return {
      success: true,
      message: 'VIP用户移除成功'
    };
  } catch (error) {
    console.error('移除VIP用户失败:', error);
    return {
      success: false,
      message: '移除失败'
    };
  }
}

// 获取VIP用户列表
async function getVipUserList(data, openId) {
  try {
    // 检查管理员权限
    const adminCheck = await checkAdminPermission(openId);
    if (!adminCheck.success) {
      return adminCheck;
    }

    const { limit = 50, offset = 0, status = 'all' } = data;

    let query = db.collection('vip_users');

    if (status === 'active') {
      query = query.where({
        is_active: true
      });
    } else if (status === 'expired') {
      query = query.where({
        is_active: false
      });
    }

    const result = await query.orderBy('created_at', 'desc').limit(limit).skip(offset).get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取VIP用户列表失败:', error);
    return {
      success: false,
      message: '获取失败'
    };
  }
}

// 获取系统设置
async function getSystemSettings() {
  try {
    console.log('开始获取系统设置');

    const result = await db.collection('system_settings').doc('main').get();
    console.log('系统设置查询结果:', result);

    if (result.data) {
      console.log('返回已存在的系统设置');
      return {
        success: true,
        data: result.data
      };
    } else {
      console.log('返回默认系统设置');
      // 返回默认设置
      const defaultSettings = {
        maxImageSize: 5,
        maxImagesPerPost: 9,
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
        autoReportThreshold: 10,
        normalUserPostLimit: 100,
        superUserPostLimit: 500,
        autoArchiveEnabled: true,
        // 每日发帖限制设置
        normalUserDailyPostLimit: 5,
        vipUserDailyPostLimit: 20
      };

      return {
        success: true,
        data: defaultSettings
      };
    }
  } catch (error) {
    console.error('获取系统设置失败:', error);
    console.error('错误详情:', error.message, error.stack);

    // 返回默认设置而不是抛出错误
    const defaultSettings = {
      maxImageSize: 5,
      maxImagesPerPost: 9,
      allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
      autoReportThreshold: 10,
      normalUserPostLimit: 100,
      superUserPostLimit: 500,
      autoArchiveEnabled: true,
      normalUserDailyPostLimit: 5,
      vipUserDailyPostLimit: 20
    };

    return {
      success: true,
      data: defaultSettings
    };
  }
}

// 更新系统设置
async function updateSystemSettings(settings, openId) {
  try {
    // 验证管理员权限
    const isAdmin = await checkAdminPermission(openId);
    if (!isAdmin) {
      throw new Error('无权限修改系统设置');
    }

    await db.collection('system_settings').doc('main').set({
      data: {
        ...settings,
        updated_at: new Date(),
        updated_by: openId
      }
    });

    return {
      success: true,
      message: '系统设置更新成功'
    };
  } catch (error) {
    console.error('更新系统设置失败:', error);
    throw new Error('更新系统设置失败');
  }
}

// 检查用户帖子数量限制
async function checkPostLimit({ user_id }, openId) {
  try {
    // 获取系统设置
    const settingsResult = await getSystemSettings();
    const settings = settingsResult.data;

    if (!settings.autoArchiveEnabled) {
      return {
        success: true,
        needArchive: false,
        message: '自动下架功能已禁用'
      };
    }

    // 获取用户信息，判断是否为超级用户
    let isSuper = false;
    try {
      const creditResult = await db.collection('user_credit_scores')
        .where({ user_id: user_id })
        .get();
      if (creditResult.data.length > 0) {
        isSuper = creditResult.data[0].is_super_user || false;
      }
    } catch (error) {
      console.warn('获取用户信用信息失败:', error);
    }

    // 确定用户的帖子上限
    const postLimit = isSuper ? settings.superUserPostLimit : settings.normalUserPostLimit;

    // 获取用户当前发布的帖子数量
    const postsResult = await db.collection('posts')
      .where({
        author_id: user_id,
        status: 'published'
      })
      .orderBy('created_at', 'asc') // 按发布时间升序，最早的在前面
      .get();

    const currentPostCount = postsResult.data.length;

    if (currentPostCount >= postLimit) {
      // 需要下架最早的帖子
      const postsToArchive = postsResult.data.slice(0, currentPostCount - postLimit + 1);

      return {
        success: true,
        needArchive: true,
        postsToArchive: postsToArchive,
        postLimit: postLimit,
        currentCount: currentPostCount,
        message: `用户帖子数量已达上限(${postLimit}条)，需要下架${postsToArchive.length}条最早的帖子`
      };
    }

    return {
      success: true,
      needArchive: false,
      postLimit: postLimit,
      currentCount: currentPostCount,
      message: `用户帖子数量正常(${currentPostCount}/${postLimit})`
    };
  } catch (error) {
    console.error('检查帖子数量限制失败:', error);
    throw new Error('检查帖子数量限制失败');
  }
}

// 自动下架旧帖子
async function archiveOldPosts({ user_id, posts_to_archive }, openId) {
  try {
    if (!posts_to_archive || posts_to_archive.length === 0) {
      return {
        success: true,
        archivedCount: 0,
        message: '没有需要下架的帖子'
      };
    }

    const archivedPosts = [];

    for (const post of posts_to_archive) {
      try {
        // 1. 将帖子状态改为已下架
        await db.collection('posts').doc(post._id).update({
          data: {
            status: 'archived',
            archived_at: new Date(),
            archived_reason: 'auto_limit_exceeded'
          }
        });

        // 2. 将帖子数据保存到草稿表
        await db.collection('user_drafts').add({
          data: {
            user_id: user_id,
            original_post_id: post._id,
            title: post.title,
            content: post.content,
            images: post.images || [],
            category: post.category,
            type: post.type,
            location: post.location,
            breed: post.breed,
            age: post.age,
            gender: post.gender,
            price: post.price,
            contact_info: post.contact_info,
            tags: post.tags || [],
            draft_type: 'auto_archived', // 标记为自动下架的草稿
            created_at: new Date(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
          }
        });

        archivedPosts.push({
          post_id: post._id,
          title: post.title,
          created_at: post.created_at
        });

      } catch (error) {
        console.error(`下架帖子 ${post._id} 失败:`, error);
      }
    }

    // 3. 记录操作日志
    await db.collection('admin_operations').add({
      data: {
        admin_id: 'system',
        operation_type: 'auto_archive_posts',
        target_type: 'user',
        target_id: user_id,
        operation_data: {
          archived_posts: archivedPosts,
          reason: 'post_limit_exceeded'
        },
        created_at: new Date()
      }
    });

    return {
      success: true,
      archivedCount: archivedPosts.length,
      archivedPosts: archivedPosts,
      message: `成功下架 ${archivedPosts.length} 条帖子并转为草稿`
    };
  } catch (error) {
    console.error('自动下架帖子失败:', error);
    throw new Error('自动下架帖子失败');
  }
}

// 获取用户草稿
async function getUserDrafts({ user_id }, openId) {
  try {
    // 验证用户权限（用户只能获取自己的草稿）
    if (user_id !== openId) {
      throw new Error('无权限获取其他用户的草稿');
    }

    const result = await db.collection('user_drafts')
      .where({ user_id: user_id })
      .orderBy('created_at', 'desc')
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取用户草稿失败:', error);
    throw new Error('获取用户草稿失败');
  }
}

// 删除用户草稿
async function deleteUserDraft({ draft_id }, openId) {
  try {
    // 先获取草稿信息验证权限
    const draftResult = await db.collection('user_drafts').doc(draft_id).get();

    if (!draftResult.data) {
      throw new Error('草稿不存在');
    }

    // 验证用户权限
    if (draftResult.data.user_id !== openId) {
      throw new Error('无权限删除其他用户的草稿');
    }

    await db.collection('user_drafts').doc(draft_id).remove();

    return {
      success: true,
      message: '草稿删除成功'
    };
  } catch (error) {
    console.error('删除用户草稿失败:', error);
    throw new Error('删除用户草稿失败');
  }
}

// 清理过期草稿（30天后自动清理）
async function cleanExpiredDrafts() {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // 查询过期的草稿
    const expiredDrafts = await db.collection('user_drafts')
      .where({
        expires_at: db.command.lt(thirtyDaysAgo)
      })
      .get();

    if (expiredDrafts.data.length === 0) {
      return {
        success: true,
        cleanedCount: 0,
        message: '没有过期的草稿需要清理'
      };
    }

    // 批量删除过期草稿
    const deletePromises = expiredDrafts.data.map(draft =>
      db.collection('user_drafts').doc(draft._id).remove()
    );

    await Promise.all(deletePromises);

    // 记录清理日志
    await db.collection('admin_operations').add({
      data: {
        admin_id: 'system',
        operation_type: 'clean_expired_drafts',
        target_type: 'drafts',
        target_id: 'batch',
        operation_data: {
          cleaned_count: expiredDrafts.data.length,
          expired_before: thirtyDaysAgo
        },
        created_at: new Date()
      }
    });

    return {
      success: true,
      cleanedCount: expiredDrafts.data.length,
      message: `成功清理 ${expiredDrafts.data.length} 条过期草稿`
    };
  } catch (error) {
    console.error('清理过期草稿失败:', error);
    throw new Error('清理过期草稿失败');
  }
}













// ==================== 活动系统相关函数 ====================

// 创建活动
async function createActivity(data, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限创建活动');
  }

  try {
    const {
      title,
      description,
      type,
      startTime,
      endTime,
      durationDays,
      resultDisplayDays = 3,
      config
    } = data;

    // 验证必填字段
    if (!title || !type || !startTime || !endTime || !config) {
      throw new Error('缺少必填字段');
    }

    // 验证活动类型
    if (!['CONTEST', 'VOTING', 'DISCUSSION'].includes(type)) {
      throw new Error('无效的活动类型');
    }

    // 计算结果展示结束时间
    const resultDisplayEndTime = new Date(endTime);
    resultDisplayEndTime.setDate(resultDisplayEndTime.getDate() + resultDisplayDays);

    // 创建活动
    const activityId = generateId();
    const activity = {
      _id: activityId,
      title,
      description: description || '',
      type,
      start_time: new Date(startTime),
      end_time: new Date(endTime),
      result_display_end_time: resultDisplayEndTime,
      duration_days: durationDays,
      result_display_days: resultDisplayDays,
      status: 'ACTIVE',
      config: config,
      final_results: null,
      statistics_summary: null,
      created_by: openId,
      created_at: new Date(),
      updated_at: new Date(),
      archived_at: null
    };

    await db.collection('activities').add({
      data: activity
    });

    return {
      success: true,
      data: {
        activity_id: activityId,
        message: '活动创建成功'
      }
    };
  } catch (error) {
    console.error('创建活动失败:', error);
    throw new Error('创建活动失败: ' + error.message);
  }
}

// 获取活动列表
async function getActivities(data, openId) {
  try {
    const {
      status = 'all',
      type = 'all',
      limit = 20,
      offset = 0,
      includeArchived = false
    } = data || {};

    let query = db.collection('activities');
    let whereConditions = {};

    // 状态筛选
    if (status !== 'all') {
      // 状态映射：前端传递的小写状态映射到数据库的大写状态
      const statusMap = {
        'active': 'ACTIVE',
        'ended': 'ENDED',
        'draft': 'DRAFT',
        'archived': 'ARCHIVED'
      };
      whereConditions.status = statusMap[status] || status;
    } else if (!includeArchived) {
      // 如果没有指定状态但不包含归档，则排除归档状态
      whereConditions.status = db.command.neq('ARCHIVED');
    }

    // 类型筛选
    if (type !== 'all') {
      whereConditions.type = type;
    }

    // 应用筛选条件
    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions);
    }

    const result = await query
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    // 更新活动状态（检查是否需要自动更新状态）
    const now = new Date();
    const updatedActivities = await Promise.all(result.data.map(async (activity) => {
      let updatedActivity = { ...activity };

      // 检查是否需要更新状态
      if (activity.status === 'ACTIVE' && now > activity.end_time) {
        updatedActivity.status = 'ENDED';
        await db.collection('activities').doc(activity._id).update({
          status: 'ENDED',
          updated_at: now
        });
      } else if (activity.status === 'ENDED' && now > activity.result_display_end_time) {
        updatedActivity.status = 'ARCHIVED';
        await db.collection('activities').doc(activity._id).update({
          status: 'ARCHIVED',
          archived_at: now,
          updated_at: now
        });
        // 触发数据清理
        await cleanupActivityData(activity._id);
      }

      return updatedActivity;
    }));

    return {
      success: true,
      data: updatedActivities,
      pagination: {
        total: result.data.length,
        limit,
        offset,
        hasMore: result.data.length === limit
      }
    };
  } catch (error) {
    console.error('获取活动列表失败:', error);
    throw new Error('获取活动列表失败');
  }
}

// 获取活动详情
async function getActivityDetail(data, openId) {
  try {
    const { activity_id } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    const result = await db.collection('activities').doc(activity_id).get();

    if (!result.data) {
      throw new Error('活动不存在');
    }

    const activity = result.data;
    const now = new Date();

    // 检查并更新活动状态
    let updatedActivity = { ...activity };
    if (activity.status === 'ACTIVE' && now > activity.end_time) {
      updatedActivity.status = 'ENDED';
      await db.collection('activities').doc(activity_id).update({
        status: 'ENDED',
        updated_at: now
      });
    } else if (activity.status === 'ENDED' && now > activity.result_display_end_time) {
      updatedActivity.status = 'ARCHIVED';
      await db.collection('activities').doc(activity_id).update({
        status: 'ARCHIVED',
        archived_at: now,
        updated_at: now
      });
      await cleanupActivityData(activity_id);
    }

    // 获取用户参与状态
    let userParticipation = null;
    if (openId) {
      const participationResult = await db.collection('activity_participants')
        .where({
          activity_id: activity_id,
          user_id: openId
        })
        .get();

      if (participationResult.data.length > 0) {
        userParticipation = participationResult.data[0];
      }
    }

    // 获取活动统计
    const stats = await getActivityStatistics(activity_id);

    return {
      success: true,
      data: {
        activity: updatedActivity,
        user_participation: userParticipation,
        statistics: stats
      }
    };
  } catch (error) {
    console.error('获取活动详情失败:', error);
    throw new Error('获取活动详情失败');
  }
}

// 参与活动（投票）
async function voteInActivity(data, openId) {
  try {
    const { activity_id, option_id, target_id } = data || {};

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    if (!openId) {
      throw new Error('用户未登录');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 检查活动状态
    if (activity.status !== 'ACTIVE') {
      throw new Error('活动未开始或已结束');
    }

    // 检查是否已经投票
    const existingVote = await db.collection('activity_votes')
      .where({
        activity_id: activity_id,
        voter_id: openId
      })
      .get();

    if (existingVote.data.length > 0) {
      throw new Error('您已经投过票了');
    }

    // 记录投票
    const voteId = generateId();
    const vote = {
      _id: voteId,
      activity_id: activity_id,
      voter_id: openId,
      option_id: option_id || null,
      target_id: target_id || null,
      vote_type: activity.type === 'VOTING' ? 'choice' : 'like',
      vote_value: 1,
      created_at: new Date()
    };

    await db.collection('activity_votes').add(vote);

    // 更新活动统计
    await updateActivityStatistics(activity_id);

    return {
      success: true,
      data: {
        vote_id: voteId,
        message: '投票成功'
      }
    };
  } catch (error) {
    console.error('投票失败:', error);
    throw new Error('投票失败: ' + error.message);
  }
}

// 添加活动评论
async function addActivityComment(data, openId) {
  try {
    const { activity_id, content, parent_id } = data;

    if (!activity_id || !content) {
      throw new Error('缺少必填字段');
    }

    if (!openId) {
      throw new Error('用户未登录');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 检查活动状态
    if (activity.status !== 'ACTIVE') {
      throw new Error('活动已结束，无法评论');
    }

    // 检查评论功能是否开启
    if (!activity.config.comments_enabled) {
      throw new Error('该活动未开启评论功能');
    }

    // 检查是否需要投票后才能评论
    if (activity.config.comments_after_vote) {
      const hasVoted = await db.collection('activity_votes')
        .where({
          activity_id: activity_id,
          voter_id: openId
        })
        .get();

      if (hasVoted.data.length === 0) {
        throw new Error('请先投票后再评论');
      }
    }

    // 检查评论频率限制
    const lastCommentResult = await db.collection('activity_comments')
      .where({
        user_id: openId,
        activity_id: activity_id
      })
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    if (lastCommentResult.data.length > 0) {
      const lastCommentTime = lastCommentResult.data[0].created_at;
      const timeDiff = Date.now() - lastCommentTime.getTime();
      const rateLimitMs = (activity.config.rate_limit?.interval || 10) * 1000;

      if (timeDiff < rateLimitMs) {
        const waitTime = Math.ceil((rateLimitMs - timeDiff) / 1000);
        throw new Error(`评论太频繁，请等待 ${waitTime} 秒后再试`);
      }
    }

    // 内容长度检查
    if (content.length < 2 || content.length > 100) {
      throw new Error('评论长度应在2-100字符之间');
    }

    // 获取用户信息
    const userResult = await db.collection('users').doc(openId).get();
    const username = userResult.data?.username || '匿名用户';

    // 添加评论
    const commentId = generateId();
    const comment = {
      _id: commentId,
      activity_id: activity_id,
      user_id: openId,
      username: username,
      parent_id: parent_id || null,
      content: content.trim(),
      status: 'approved', // 暂时自动通过，后续可加审核
      like_count: 0,
      reply_count: 0,
      created_at: new Date(),
      updated_at: new Date()
    };

    await db.collection('activity_comments').add(comment);

    // 如果是回复，更新父评论的回复数
    if (parent_id) {
      await db.collection('activity_comments').doc(parent_id).update({
        reply_count: db.command.inc(1)
      });
    }

    return {
      success: true,
      data: {
        comment_id: commentId,
        comment: comment,
        message: '评论发表成功'
      }
    };
  } catch (error) {
    console.error('添加评论失败:', error);
    throw new Error('添加评论失败: ' + error.message);
  }
}

// 获取活动评论
async function getActivityComments(data, openId) {
  try {
    const { activity_id, page = 1, limit = 20, parent_id = null } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    const offset = (page - 1) * limit;

    let query = db.collection('activity_comments')
      .where({
        activity_id: activity_id,
        status: 'approved'
      });

    // 如果指定了parent_id，获取回复；否则获取顶级评论
    if (parent_id) {
      query = query.where({ parent_id: parent_id });
    } else {
      query = query.where({ parent_id: null });
    }

    const result = await query
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data,
      pagination: {
        page,
        limit,
        total: result.data.length,
        hasMore: result.data.length === limit
      }
    };
  } catch (error) {
    console.error('获取评论失败:', error);
    throw new Error('获取评论失败');
  }
}

// 获取系统配置
async function getSystemConfig(data, openId) {
  try {
    // 获取系统配置
    const configResult = await db.collection('system_config')
      .where({ key: 'activity_system' })
      .get();

    let config = {
      enabled: false,
      comments_enabled: true,
      rate_limit_interval: 10,
      max_comment_length: 100,
      default_result_display_days: 3
    };

    if (configResult.data.length > 0) {
      config = { ...config, ...configResult.data[0].value };
    }

    return {
      success: true,
      data: config
    };
  } catch (error) {
    console.error('获取系统配置失败:', error);
    throw new Error('获取系统配置失败');
  }
}

// 更新系统配置
async function updateSystemConfig(data, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限修改系统配置');
  }

  try {
    const { config } = data;

    if (!config) {
      throw new Error('缺少配置数据');
    }

    // 更新或创建系统配置
    const existingConfig = await db.collection('system_config')
      .where({ key: 'activity_system' })
      .get();

    if (existingConfig.data.length > 0) {
      await db.collection('system_config').doc(existingConfig.data[0]._id).update({
        value: config,
        updated_at: new Date()
      });
    } else {
      await db.collection('system_config').add({
        key: 'activity_system',
        value: config,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    return {
      success: true,
      data: {
        message: '系统配置更新成功'
      }
    };
  } catch (error) {
    console.error('更新系统配置失败:', error);
    throw new Error('更新系统配置失败: ' + error.message);
  }
}

// 辅助函数：获取活动统计
async function getActivityStatistics(activityId) {
  try {
    // 获取投票统计
    const voteStats = await db.collection('activity_votes')
      .where({ activity_id: activityId })
      .get();

    // 获取评论统计
    const commentStats = await db.collection('activity_comments')
      .where({
        activity_id: activityId,
        status: 'approved'
      })
      .get();

    // 按选项统计投票
    const votesByOption = {};
    voteStats.data.forEach(vote => {
      const key = vote.option_id || vote.target_id || 'default';
      votesByOption[key] = (votesByOption[key] || 0) + 1;
    });

    return {
      total_votes: voteStats.data.length,
      total_comments: commentStats.data.length,
      votes_by_option: votesByOption,
      last_updated: new Date()
    };
  } catch (error) {
    console.error('获取活动统计失败:', error);
    return {
      total_votes: 0,
      total_comments: 0,
      votes_by_option: {},
      last_updated: new Date()
    };
  }
}

// 辅助函数：更新活动统计
async function updateActivityStatistics(activityId) {
  try {
    const stats = await getActivityStatistics(activityId);

    await db.collection('activities').doc(activityId).update({
      statistics_summary: stats,
      updated_at: new Date()
    });
  } catch (error) {
    console.error('更新活动统计失败:', error);
  }
}

// 辅助函数：清理活动数据
async function cleanupActivityData(activityId) {
  try {
    console.log(`开始清理活动 ${activityId} 的数据`);

    // 清理评论数据
    const comments = await db.collection('activity_comments')
      .where({ activity_id: activityId })
      .get();

    for (const comment of comments.data) {
      await db.collection('activity_comments').doc(comment._id).remove();
    }

    // 清理详细投票记录（保留统计）
    const votes = await db.collection('activity_votes')
      .where({ activity_id: activityId })
      .get();

    for (const vote of votes.data) {
      await db.collection('activity_votes').doc(vote._id).remove();
    }

    console.log(`活动 ${activityId} 数据清理完成`);
  } catch (error) {
    console.error('清理活动数据失败:', error);
  }
}

// 更新活动
async function updateActivity(data, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限修改活动');
  }

  try {
    const { activity_id, title, description, config, status } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    // 构建更新数据
    const updateData = {
      updated_at: new Date()
    };

    if (title) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (config) updateData.config = config;
    if (status) updateData.status = status;

    await db.collection('activities').doc(activity_id).update(updateData);

    return {
      success: true,
      data: {
        message: '活动更新成功'
      }
    };
  } catch (error) {
    console.error('更新活动失败:', error);
    throw new Error('更新活动失败: ' + error.message);
  }
}

// 删除活动
async function deleteActivity(data, openId) {
  // 验证管理员权限
  let isAdmin = false;

  if (openId) {
    isAdmin = await checkAdminPermission(openId);
  } else {
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    if (recentSuperAdminSession.data.length > 0) {
      const adminId = recentSuperAdminSession.data[0].admin_id;
      const admin = await getAdminById(adminId);

      if (admin && admin.level <= 1) {
        isAdmin = true;
      }
    }
  }

  if (!isAdmin) {
    throw new Error('无权限删除活动');
  }

  try {
    const { activity_id } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    // 删除相关数据
    await cleanupActivityData(activity_id);

    // 删除活动
    await db.collection('activities').doc(activity_id).remove();

    return {
      success: true,
      data: {
        message: '活动删除成功'
      }
    };
  } catch (error) {
    console.error('删除活动失败:', error);
    throw new Error('删除活动失败: ' + error.message);
  }
}

// 参与活动（通用）
async function participateInActivity(data, openId) {
  try {
    const { activity_id, participation_type, content } = data;

    if (!activity_id || !participation_type) {
      throw new Error('缺少必填字段');
    }

    if (!openId) {
      throw new Error('用户未登录');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 检查活动状态
    if (activity.status !== 'ACTIVE') {
      throw new Error('活动未开始或已结束');
    }

    // 记录参与
    const participationId = generateId();
    const participation = {
      _id: participationId,
      activity_id: activity_id,
      user_id: openId,
      content_type: participation_type,
      content_id: null,
      submission_time: new Date(),
      status: 'approved'
    };

    await db.collection('activity_participants').add(participation);

    return {
      success: true,
      data: {
        participation_id: participationId,
        message: '参与成功'
      }
    };
  } catch (error) {
    console.error('参与活动失败:', error);
    throw new Error('参与活动失败: ' + error.message);
  }
}

// 获取活动结果
async function getActivityResults(data, openId) {
  try {
    const { activity_id } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 如果活动还在进行中，返回实时统计
    if (activity.status === 'ACTIVE') {
      const stats = await getActivityStatistics(activity_id);
      return {
        success: true,
        data: {
          activity_status: 'ACTIVE',
          real_time_stats: stats
        }
      };
    }

    // 如果活动已结束，返回最终结果
    if (activity.final_results) {
      return {
        success: true,
        data: {
          activity_status: activity.status,
          final_results: activity.final_results,
          statistics_summary: activity.statistics_summary
        }
      };
    }

    // 如果没有最终结果，返回统计数据
    const stats = await getActivityStatistics(activity_id);
    return {
      success: true,
      data: {
        activity_status: activity.status,
        statistics_summary: stats
      }
    };
  } catch (error) {
    console.error('获取活动结果失败:', error);
    throw new Error('获取活动结果失败');
  }
}

// 处理活动自动化任务
async function processActivityAutomation(data, openId) {
  try {
    console.log('开始处理活动自动化任务');

    const now = new Date();
    let processedCount = 0;

    // 1. 检查需要结束的活动
    const activeActivities = await db.collection('activities')
      .where({
        status: 'ACTIVE',
        end_time: db.command.lte(now)
      })
      .get();

    for (const activity of activeActivities.data) {
      await db.collection('activities').doc(activity._id).update({
        status: 'ENDED',
        updated_at: now
      });

      // 计算最终结果
      await calculateFinalResults(activity._id);
      processedCount++;

      console.log(`活动 ${activity._id} 已自动结束`);
    }

    // 2. 检查需要归档的活动
    const endedActivities = await db.collection('activities')
      .where({
        status: 'ENDED',
        result_display_end_time: db.command.lte(now)
      })
      .get();

    for (const activity of endedActivities.data) {
      await db.collection('activities').doc(activity._id).update({
        status: 'ARCHIVED',
        archived_at: now,
        updated_at: now
      });

      // 清理活动数据
      await cleanupActivityData(activity._id);
      processedCount++;

      console.log(`活动 ${activity._id} 已自动归档`);
    }

    // 3. 更新活动统计
    const allActiveActivities = await db.collection('activities')
      .where({ status: 'ACTIVE' })
      .get();

    for (const activity of allActiveActivities.data) {
      await updateActivityStatistics(activity._id);
    }

    return {
      success: true,
      data: {
        processed_count: processedCount,
        message: `处理了 ${processedCount} 个活动的状态更新`
      }
    };
  } catch (error) {
    console.error('处理活动自动化任务失败:', error);
    throw new Error('处理活动自动化任务失败');
  }
}

// 计算活动最终结果
async function calculateFinalResults(activityId) {
  try {
    const activity = await db.collection('activities').doc(activityId).get();
    if (!activity.data) return;

    const activityData = activity.data;
    let finalResults = null;

    if (activityData.type === 'CONTEST') {
      // 评选竞赛：获取前N名
      const topCount = activityData.config.rules?.finalSelection?.finalCount || 10;

      // 这里应该根据实际的评分逻辑来获取前N名
      // 暂时使用投票数作为排序依据
      const votes = await db.collection('activity_votes')
        .where({ activity_id: activityId })
        .get();

      const votesByTarget = {};
      votes.data.forEach(vote => {
        const targetId = vote.target_id || vote.option_id;
        if (targetId) {
          votesByTarget[targetId] = (votesByTarget[targetId] || 0) + 1;
        }
      });

      const sortedResults = Object.entries(votesByTarget)
        .sort(([,a], [,b]) => b - a)
        .slice(0, topCount)
        .map(([targetId, votes], index) => ({
          rank: index + 1,
          participant_id: targetId,
          participant_name: `参与者${index + 1}`,
          votes: votes,
          award_title: index === 0 ? '冠军' : index === 1 ? '亚军' : index === 2 ? '季军' : `第${index + 1}名`
        }));

      finalResults = sortedResults;
    } else if (activityData.type === 'VOTING') {
      // 投票活动：统计最终投票结果
      const stats = await getActivityStatistics(activityId);
      finalResults = {
        total_votes: stats.total_votes,
        votes_by_option: stats.votes_by_option,
        winner: Object.entries(stats.votes_by_option)
          .sort(([,a], [,b]) => b - a)[0]
      };
    }

    // 保存最终结果
    await db.collection('activities').doc(activityId).update({
      final_results: finalResults,
      updated_at: new Date()
    });

    console.log(`活动 ${activityId} 最终结果计算完成`);
  } catch (error) {
    console.error('计算活动最终结果失败:', error);
  }
}

// 活动投票
async function voteInActivity(data, openId) {
  try {
    const { activity_id, option_id, target_id } = data || {};

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    if (!openId) {
      throw new Error('用户未登录');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 检查活动状态
    if (activity.status !== 'ACTIVE') {
      throw new Error('活动未开始或已结束');
    }

    // 检查是否已经投票
    const existingVote = await db.collection('activity_votes')
      .where({
        activity_id: activity_id,
        user_id: openId
      })
      .get();

    if (existingVote.data.length > 0) {
      throw new Error('您已经投过票了');
    }

    // 记录投票
    const voteId = generateId();
    const vote = {
      _id: voteId,
      activity_id: activity_id,
      user_id: openId,
      option_id: option_id || null,
      target_id: target_id || null,
      vote_time: new Date(),
      ip_address: null // 可以从context获取
    };

    await db.collection('activity_votes').add({
      data: vote
    });

    // 记录参与
    const participationId = generateId();
    const participation = {
      _id: participationId,
      activity_id: activity_id,
      user_id: openId,
      content_type: 'VOTE',
      content_id: voteId,
      submission_time: new Date(),
      status: 'approved'
    };

    await db.collection('activity_participants').add({
      data: participation
    });

    // 更新活动统计
    await updateActivityStatistics(activity_id);

    return {
      success: true,
      data: {
        vote_id: voteId,
        message: '投票成功'
      }
    };
  } catch (error) {
    console.error('投票失败:', error);
    throw new Error('投票失败: ' + error.message);
  }
}

// 添加活动评论
async function addActivityComment(data, openId) {
  try {
    const { activity_id, content, parent_comment_id } = data;

    if (!activity_id || !content) {
      throw new Error('缺少必填字段');
    }

    if (!openId) {
      throw new Error('用户未登录');
    }

    // 内容长度检查
    if (content.length < 2 || content.length > 100) {
      throw new Error('评论内容长度应在2-100字之间');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 检查活动状态
    if (activity.status !== 'ACTIVE') {
      throw new Error('活动已结束，无法评论');
    }

    // 检查评论是否启用
    if (!activity.config.comments_enabled) {
      throw new Error('该活动未开启评论功能');
    }

    // 检查是否需要先投票
    if (activity.config.comments_after_vote) {
      const userVote = await db.collection('activity_votes')
        .where({
          activity_id: activity_id,
          user_id: openId
        })
        .get();

      if (userVote.data.length === 0) {
        throw new Error('请先投票后再评论');
      }
    }

    // 检查评论频率限制
    const rateLimit = activity.config.rate_limit?.interval || 10;
    const recentComment = await db.collection('activity_comments')
      .where({
        activity_id: activity_id,
        user_id: openId
      })
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    if (recentComment.data.length > 0) {
      const lastCommentTime = new Date(recentComment.data[0].created_at);
      const now = new Date();
      const timeDiff = (now.getTime() - lastCommentTime.getTime()) / 1000;

      if (timeDiff < rateLimit) {
        throw new Error(`请等待 ${Math.ceil(rateLimit - timeDiff)} 秒后再评论`);
      }
    }

    // 获取用户信息
    const userResult = await db.collection('users').where({ openid: openId }).get();
    const username = userResult.data.length > 0 ? userResult.data[0].username : '匿名用户';

    // 添加评论
    const commentId = generateId();
    const comment = {
      _id: commentId,
      activity_id: activity_id,
      user_id: openId,
      username: username,
      content: content,
      parent_comment_id: parent_comment_id || null,
      created_at: new Date(),
      like_count: 0,
      reply_count: 0,
      status: 'approved'
    };

    await db.collection('activity_comments').add(comment);

    // 如果是回复，更新父评论的回复数
    if (parent_comment_id) {
      await db.collection('activity_comments').doc(parent_comment_id).update({
        reply_count: db.command.inc(1)
      });
    }

    // 记录参与
    const participationId = generateId();
    const participation = {
      _id: participationId,
      activity_id: activity_id,
      user_id: openId,
      content_type: 'COMMENT',
      content_id: commentId,
      submission_time: new Date(),
      status: 'approved'
    };

    await db.collection('activity_participants').add(participation);

    return {
      success: true,
      data: {
        comment_id: commentId,
        message: '评论成功'
      }
    };
  } catch (error) {
    console.error('添加评论失败:', error);
    throw new Error('评论失败: ' + error.message);
  }
}

// 获取活动评论
async function getActivityComments(data, openId) {
  try {
    const { activity_id, page = 1, limit = 20, parent_comment_id } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    const offset = (page - 1) * limit;

    // 构建查询条件
    let query = db.collection('activity_comments')
      .where({
        activity_id: activity_id,
        status: 'approved'
      });

    // 如果指定了父评论ID，则查询回复
    if (parent_comment_id) {
      query = query.where({ parent_comment_id: parent_comment_id });
    } else {
      // 否则查询顶级评论
      query = query.where({ parent_comment_id: null });
    }

    const comments = await query
      .orderBy('created_at', 'desc')
      .skip(offset)
      .limit(limit)
      .get();

    return {
      success: true,
      data: comments.data || []
    };
  } catch (error) {
    console.error('获取评论失败:', error);
    throw new Error('获取评论失败');
  }
}

// 获取活动详情
async function getActivityDetail(data, openId) {
  try {
    const { activity_id } = data;

    if (!activity_id) {
      throw new Error('缺少活动ID');
    }

    // 获取活动信息
    const activityResult = await db.collection('activities').doc(activity_id).get();
    if (!activityResult.data) {
      throw new Error('活动不存在');
    }

    const activity = activityResult.data;

    // 获取用户参与情况
    let userParticipation = null;
    if (openId) {
      const voteResult = await db.collection('activity_votes')
        .where({
          activity_id: activity_id,
          user_id: openId
        })
        .get();

      if (voteResult.data.length > 0) {
        userParticipation = voteResult.data[0];
      }
    }

    // 获取活动统计
    const statistics = await getActivityStatistics(activity_id);

    return {
      success: true,
      data: {
        activity: {
          ...activity,
          statistics_summary: statistics
        },
        user_participation: userParticipation
      }
    };
  } catch (error) {
    console.error('获取活动详情失败:', error);
    throw new Error('获取活动详情失败');
  }
}

// 发送联系通知
async function sendContactNotification({ postId, authorId, userContact, authorContact }, userId) {
  if (!userId) {
    throw new Error('用户未登录');
  }

  if (!postId || !authorId) {
    throw new Error('参数不完整');
  }

  try {
    // 检查是否被对方拉黑
    const blacklistCheck = await db.collection('blacklist')
      .where({
        user_id: authorId,
        blocked_user_id: userId
      })
      .get();

    if (blacklistCheck.data.length > 0) {
      return {
        success: false,
        message: '对方已经将您拉黑无法联系'
      };
    }

    // 获取帖子信息
    const post = await db.collection('posts').doc(postId).get();
    if (!post.data) {
      throw new Error('帖子不存在');
    }

    // 获取发送者信息
    const sender = await db.collection('users').doc(userId).get();
    if (!sender.data) {
      throw new Error('用户信息不存在');
    }

    // 获取接收者信息
    const recipient = await db.collection('users').doc(authorId).get();
    if (!recipient.data) {
      throw new Error('接收者信息不存在');
    }

    // 给帖子作者发送通知（包含联系者的联系方式）
    await createNotification({
      type: 'contact',
      recipient_id: authorId,
      sender_id: userId,
      post_id: postId,
      message: `${sender.data.nickname || '用户'} 想要联系您`,
      data: {
        post_title: post.data.title,
        sender_nickname: sender.data.nickname,
        sender_contact: userContact,
        contact_type: 'received'
      }
    });

    // 给联系者发送通知（包含帖子作者的联系方式）
    await createNotification({
      type: 'contact',
      recipient_id: userId,
      sender_id: authorId,
      post_id: postId,
      message: `已获取 ${recipient.data.nickname || '用户'} 的联系方式`,
      data: {
        post_title: post.data.title,
        author_nickname: recipient.data.nickname,
        author_contact: authorContact,
        contact_type: 'sent'
      }
    });

    return {
      success: true,
      message: '联系方式已发送至通知'
    };
  } catch (error) {
    console.error('发送联系通知失败:', error);
    throw new Error('发送联系通知失败');
  }
}

// 创建搜索优化索引
async function createSearchIndexes() {
  try {
    console.log('开始创建搜索索引...');

    // 为posts集合创建复合索引
    const indexesToCreate = [
      // 基础搜索索引
      {
        name: 'category_created_at',
        keys: [
          { name: 'category', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ]
      },
      // 品种搜索索引
      {
        name: 'breed_category',
        keys: [
          { name: 'breed', direction: '1' },
          { name: 'category', direction: '1' }
        ]
      },
      // 品种标签搜索索引
      {
        name: 'breed_tags_category',
        keys: [
          { name: 'breed_tags', direction: '1' },
          { name: 'category', direction: '1' }
        ]
      },
      // 位置搜索索引
      {
        name: 'location_category',
        keys: [
          { name: 'location', direction: '1' },
          { name: 'category', direction: '1' }
        ]
      },
      // 类型筛选索引
      {
        name: 'type_category_created_at',
        keys: [
          { name: 'type', direction: '1' },
          { name: 'category', direction: '1' },
          { name: 'created_at', direction: '-1' }
        ]
      }
    ];

    const results = [];
    for (const index of indexesToCreate) {
      try {
        await db.collection('posts').createIndex({
          name: index.name,
          keys: index.keys
        });
        results.push(`✅ 创建索引: ${index.name}`);
      } catch (error) {
        if (error.message.includes('already exists')) {
          results.push(`⚠️ 索引已存在: ${index.name}`);
        } else {
          results.push(`❌ 创建索引失败 ${index.name}: ${error.message}`);
        }
      }
    }

    return {
      success: true,
      message: '搜索索引创建完成',
      details: results
    };
  } catch (error) {
    console.error('创建搜索索引失败:', error);
    return {
      success: false,
      message: '创建搜索索引失败: ' + error.message
    };
  }
}

// 用户删除自己的帖子
async function deleteMyPost({ postId }, openId) {
  console.log('用户删除自己的帖子，OPENID:', openId, 'postId:', postId);

  if (!postId) {
    throw new Error('缺少帖子ID');
  }

  // 获取帖子信息
  const post = await db.collection('posts').doc(postId).get();
  if (!post.data) {
    throw new Error('帖子不存在');
  }

  // 检查是否是帖子作者
  if (post.data.author_id !== openId) {
    throw new Error('只能删除自己发布的帖子');
  }

  try {
    // 删除帖子相关的图片文件
    if (post.data.images && post.data.images.length > 0) {
      console.log('删除帖子相关图片:', post.data.images);
      for (const imageUrl of post.data.images) {
        try {
          await deleteImageFile(imageUrl);
          console.log('删除图片成功:', imageUrl);
        } catch (fileError) {
          console.warn('删除图片失败:', imageUrl, fileError);
          // 继续删除其他文件，不因为单个文件删除失败而中断
        }
      }
    }

    // 删除帖子相关数据
    await Promise.all([
      // 删除点赞记录
      db.collection('likes').where({ post_id: postId }).remove(),
      // 删除收藏记录
      db.collection('bookmarks').where({ post_id: postId }).remove(),
      // 删除评分记录
      db.collection('ratings').where({ post_id: postId }).remove(),
      // 删除想要记录
      db.collection('wants').where({ post_id: postId }).remove(),
      // 删除联系记录
      db.collection('contacts').where({ post_id: postId }).remove(),
      // 删除举报记录
      db.collection('post_reports').where({ post_id: postId }).remove()
    ]);

    // 删除帖子本身
    await db.collection('posts').doc(postId).remove();

    console.log('用户删除帖子成功:', postId);

    return {
      success: true,
      message: '帖子删除成功'
    };

  } catch (error) {
    console.error('删除帖子失败:', error);
    throw new Error('删除帖子失败: ' + error.message);
  }
}

/**
 * 获取仪表板统计数据
 */
async function getDashboardStats(data, adminOpenId) {
  try {
    // 权限验证：检查是否为管理员
    let isAdmin = false;

    // 方法1：直接通过ID查找
    try {
      const adminResult = await db.collection('admins').doc(adminOpenId).get();
      if (adminResult.data) {
        isAdmin = true;
        console.log('通过ID找到管理员:', adminResult.data.username);
      }
    } catch (error) {
      console.log('通过ID查找管理员失败:', error.message);
    }

    // 方法2：如果ID查找失败，尝试通过用户名查找超级管理员
    if (!isAdmin) {
      try {
        const adminByUsername = await db.collection('admins').where({
          username: 'superadminTT'
        }).get();

        if (adminByUsername.data && adminByUsername.data.length > 0) {
          isAdmin = true;
          console.log('通过用户名找到超级管理员:', adminByUsername.data[0].username);
        }
      } catch (error) {
        console.log('通过用户名查找管理员失败:', error.message);
      }
    }

    // 方法3：如果还是没找到，检查是否有任何超级管理员权限
    if (!isAdmin) {
      try {
        const superAdmins = await db.collection('admins').where({
          role: 'super_admin'
        }).get();

        if (superAdmins.data && superAdmins.data.length > 0) {
          isAdmin = true;
          console.log('找到超级管理员，允许访问');
        }
      } catch (error) {
        console.log('查找超级管理员失败:', error.message);
      }
    }

    if (!isAdmin) {
      return {
        success: false,
        message: '无权限访问'
      };
    }

    // 获取昨天的日期范围
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStart = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0);
    const yesterdayEnd = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);

    console.log('统计昨日数据范围:', yesterdayStart.toISOString(), '到', yesterdayEnd.toISOString());

    // 并行查询所有统计数据
    const [
      totalUsersResult,
      totalPostsResult,
      yesterdayUsersResult,
      yesterdayPostsResult,
      pendingReportsResult,
      pendingAppealsResult,
      activeUsersResult
    ] = await Promise.all([
      // 总用户数
      db.collection('users').count(),

      // 总宝贝数
      db.collection('posts').count(),

      // 昨日新增用户
      db.collection('users').where({
        created_at: _.gte(yesterdayStart).and(_.lte(yesterdayEnd))
      }).count(),

      // 昨日新增宝贝
      db.collection('posts').where({
        created_at: _.gte(yesterdayStart).and(_.lte(yesterdayEnd))
      }).count(),

      // 待处理举报
      db.collection('post_reports').where({
        status: 'pending'
      }).count(),

      // 待处理申诉
      db.collection('appeals').where({
        status: 'pending'
      }).count(),

      // 活跃用户（最近7天有活动的用户）
      (() => {
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return db.collection('users').where({
          last_active_at: _.gte(sevenDaysAgo)
        }).count();
      })()
    ]);

    // 模拟在线用户数（实际项目中可以通过session或其他方式统计）
    const onlineUsers = Math.floor(Math.random() * 200) + 50; // 50-250之间的随机数

    const stats = {
      totalUsers: totalUsersResult.total || 0,
      totalPosts: totalPostsResult.total || 0,
      yesterdayNewUsers: yesterdayUsersResult.total || 0,
      yesterdayNewPosts: yesterdayPostsResult.total || 0,
      totalReports: pendingReportsResult.total || 0,
      totalAppeals: pendingAppealsResult.total || 0,
      activeUsers: activeUsersResult.total || 0,
      onlineUsers: onlineUsers
    };

    console.log('仪表板统计数据:', stats);

    return {
      success: true,
      data: stats,
      message: '获取统计数据成功'
    };

  } catch (error) {
    console.error('获取仪表板统计数据失败:', error);
    return {
      success: false,
      message: '获取统计数据失败: ' + error.message
    };
  }
}

// 获取举报阈值配置API
async function getReportThresholdsAPI(data, userId) {
  try {
    // 检查管理员权限
    const isAdmin = await checkAdminPermission(userId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const thresholds = await getReportThresholds();

    return {
      success: true,
      data: thresholds,
      message: '获取举报阈值配置成功'
    };
  } catch (error) {
    console.error('获取举报阈值配置失败:', error);
    return {
      success: false,
      message: error.message || '获取配置失败'
    };
  }
}

// 更新举报阈值配置API
async function updateReportThresholds(data, userId) {
  try {
    // 检查管理员权限
    const isAdmin = await checkAdminPermission(userId);
    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    const { post_report_threshold, user_report_threshold, auto_hide_posts, auto_warn_users, notification_enabled } = data;

    // 验证参数
    if (typeof post_report_threshold !== 'number' || post_report_threshold < 1 || post_report_threshold > 100) {
      throw new Error('帖子举报阈值必须在1-100之间');
    }

    if (typeof user_report_threshold !== 'number' || user_report_threshold < 1 || user_report_threshold > 100) {
      throw new Error('用户举报阈值必须在1-100之间');
    }

    // 更新配置
    const config = await db.collection('system_config')
      .where({ key: 'report_thresholds' })
      .get();

    const newConfig = {
      post_report_threshold,
      user_report_threshold,
      auto_hide_posts: Boolean(auto_hide_posts),
      auto_warn_users: Boolean(auto_warn_users),
      notification_enabled: Boolean(notification_enabled)
    };

    if (config.data.length > 0) {
      // 更新现有配置
      await db.collection('system_config').doc(config.data[0]._id).update({
        data: {
          value: newConfig,
          updated_at: new Date()
        }
      });
    } else {
      // 创建新配置
      await db.collection('system_config').add({
        data: {
          key: 'report_thresholds',
          value: newConfig,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
    }

    return {
      success: true,
      data: newConfig,
      message: '举报阈值配置更新成功'
    };
  } catch (error) {
    console.error('更新举报阈值配置失败:', error);
    return {
      success: false,
      message: error.message || '更新配置失败'
    };
  }
}

// 管理员功能 - 调整帖子曝光度
async function adjustPostExposure({ post_id, adjustment }, openId) {
  try {
    // 检查是否有活跃的超级管理员会话
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    let isAdmin = false;
    if (recentSuperAdminSession.data.length > 0) {
      const session = recentSuperAdminSession.data[0];
      const admin = await getAdminById(session.admin_id);

      if (admin && admin.role === 'super_admin' && admin.status === 'active') {
        isAdmin = true;
      }
    }

    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    // 获取当前帖子信息
    const post = await db.collection('posts').doc(post_id).get();
    if (!post.data) {
      throw new Error('帖子不存在');
    }

    const currentScore = post.data.exposure_score || 50;
    const newScore = Math.max(0, Math.min(100, currentScore + adjustment));

    // 更新曝光度
    await db.collection('posts').doc(post_id).update({
      data: {
        exposure_score: newScore,
        updated_at: new Date()
      }
    });

    return {
      success: true,
      data: {
        post_id,
        old_score: currentScore,
        new_score: newScore,
        adjustment
      },
      message: `曝光度已${adjustment > 0 ? '增加' : '减少'}${Math.abs(adjustment)}分`
    };
  } catch (error) {
    console.error('调整曝光度失败:', error);
    return {
      success: false,
      message: error.message || '调整失败'
    };
  }
}

// 管理员功能 - 下架帖子
async function takeDownPost({ post_id }, openId) {
  try {
    // 检查是否有活跃的超级管理员会话
    const recentSuperAdminSession = await db.collection('admin_sessions')
      .where({ status: 'active' })
      .orderBy('last_activity', 'desc')
      .limit(1)
      .get();

    let isAdmin = false;
    if (recentSuperAdminSession.data.length > 0) {
      const session = recentSuperAdminSession.data[0];
      const admin = await getAdminById(session.admin_id);

      if (admin && admin.role === 'super_admin' && admin.status === 'active') {
        isAdmin = true;
      }
    }

    if (!isAdmin) {
      throw new Error('无权限操作');
    }

    // 获取当前帖子信息
    const post = await db.collection('posts').doc(post_id).get();
    if (!post.data) {
      throw new Error('帖子不存在');
    }

    // 下架帖子（设置状态为下架，曝光度为0）
    await db.collection('posts').doc(post_id).update({
      data: {
        status: 'taken_down',
        exposure_score: 0,
        taken_down_at: new Date(),
        updated_at: new Date()
      }
    });

    return {
      success: true,
      data: {
        post_id,
        status: 'taken_down'
      },
      message: '帖子已下架'
    };
  } catch (error) {
    console.error('下架帖子失败:', error);
    return {
      success: false,
      message: error.message || '下架失败'
    };
  }
}

// 检查每日发帖限制
async function checkDailyPostLimit(userId) {
  try {
    // 获取今日开始时间
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 查询用户今日发帖数量
    const todayPostsResult = await db.collection('posts').where({
      author_id: userId,
      created_at: db.command.gte(todayStart),
      status: db.command.neq('deleted') // 排除已删除的帖子
    }).count();

    const todayPostCount = todayPostsResult.total;

    // 获取用户的每日发帖限制
    const dailyLimit = await getUserDailyPostLimit(userId);

    return {
      canPost: todayPostCount < dailyLimit,
      currentCount: todayPostCount,
      limit: dailyLimit
    };
  } catch (error) {
    console.error('检查每日发帖限制失败:', error);
    // 出错时允许发帖，避免影响正常用户
    return {
      canPost: true,
      currentCount: 0,
      limit: 5
    };
  }
}

// 获取用户每日发帖限制（优先级：个人设置 > 系统设置）
async function getUserDailyPostLimit(userId) {
  try {
    // 1. 查询用户个人设置
    const userCreditResult = await db.collection('user_credit_scores').where({
      user_id: userId
    }).get();

    if (userCreditResult.data.length > 0) {
      const userCredit = userCreditResult.data[0];
      if (userCredit.daily_post_limit !== undefined && userCredit.daily_post_limit > 0) {
        return userCredit.daily_post_limit;
      }
    }

    // 2. 查询用户VIP状态
    const vipResult = await db.collection('vip_users').where({
      user_id: userId,
      vip_end_time: db.command.gt(new Date()) // VIP未过期
    }).get();

    // 3. 查询系统设置
    const systemSettingsResult = await db.collection('system_settings').limit(1).get();
    let systemSettings = {};
    if (systemSettingsResult.data.length > 0) {
      systemSettings = systemSettingsResult.data[0];
    }

    // 4. 根据用户类型返回相应限制
    if (vipResult.data.length > 0) {
      // VIP用户使用VIP限制
      return systemSettings.vipUserDailyPostLimit || 20;
    } else {
      // 普通用户使用普通限制
      return systemSettings.normalUserDailyPostLimit || 5;
    }
  } catch (error) {
    console.error('获取用户每日发帖限制失败:', error);
    // 出错时返回默认限制
    return 5;
  }
}

// 导出主函数已在文件开头定义