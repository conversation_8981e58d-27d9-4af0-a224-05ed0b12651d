(()=>{var e={};e.id=495,e.ids=[495],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},89313:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d}),t(20914),t(16953),t(35866);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),c=t(95231),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["profile",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20914)),"D:\\web-cloudbase-project\\src\\app\\profile\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\web-cloudbase-project\\src\\app\\profile\\[id]\\page.tsx"],x="/profile/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/profile/[id]/page",pathname:"/profile/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96182:(e,s,t)=>{Promise.resolve().then(t.bind(t,69216))},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},82200:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},17069:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},74975:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},69216:(e,s,t)=>{"use strict";t.d(s,{default:()=>S});var a=t(10326),r=t(17577),l=t(35047),i=t(86333),c=t(924),n=t(67427),d=t(17069),o=t(88378),x=t(76557);let m=(0,x.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var u=t(82200),h=t(67187),p=t(79635),g=t(43810);let y=(0,x.Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var j=t(74975),f=t(41828),v=t(35659),b=t(99837),N=t(22502),w=t(16545),k=t(20603),C=t(28295),_=t(28676);let Z=[{id:1,label:"疑似骗子",description:"该用户存在欺诈行为或疑似诈骗"}],P=({isOpen:e,onClose:s,targetUserId:t,targetUserName:l,onSuccess:i})=>{let[c,n]=(0,r.useState)(1),[d,o]=(0,r.useState)(!1),x=()=>{n(1),s()},m=async()=>{try{o(!0);let e=await f.petAPI.reportUser({targetUserId:t,reason:"疑似骗子"});e.success?(k.C.success("举报提交成功，我们会尽快处理"),i?.(),x()):k.C.error(e.message||"举报失败")}catch(e){console.error("举报用户失败:",e),k.C.error(e.message||"举报失败")}finally{o(!1)}};return a.jsx(_.u_,{isOpen:e,onClose:x,showCloseButton:!1,children:a.jsx(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:a.jsx(u.Z,{className:"h-5 w-5 text-red-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"举报用户"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["举报 ",l]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"请选择举报原因："}),a.jsx("div",{className:"space-y-2",children:Z.map(e=>(0,a.jsxs)("label",{className:`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${c===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"}`,children:[a.jsx("input",{type:"radio",name:"reason",value:e.id,checked:c===e.id,onChange:()=>n(e.id),className:"mt-1 text-red-600 focus:ring-red-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("div",{className:"font-medium text-gray-900",children:e.label}),a.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(b.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:d,children:"取消"}),a.jsx(b.Z,{variant:"danger",onClick:m,loading:d,className:"flex-1",disabled:!1,children:"提交举报"})]})]})})})},I=({isOpen:e,onClose:s,targetUserId:t,targetUserName:l,onSuccess:i})=>{let[c,n]=(0,r.useState)(!1),d=()=>{s()},o=async()=>{try{n(!0);let e=await f.petAPI.blockUser({targetUserId:t});e.success?(k.C.success("已拉黑该用户"),i?.(),d()):k.C.error(e.message||"拉黑失败")}catch(e){console.error("拉黑用户失败:",e),k.C.error(e.message||"拉黑失败")}finally{n(!1)}};return a.jsx(_.u_,{isOpen:e,onClose:d,showCloseButton:!1,children:a.jsx(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-gray-100 rounded-lg",children:a.jsx(h.Z,{className:"h-5 w-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"拉黑用户"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["拉黑 ",l]})]})]}),a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:a.jsx("p",{className:"text-sm text-yellow-700",children:"您将不会再看到该用户发布的内容，该用户也不能再联系您"})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx(b.Z,{variant:"outline",onClick:d,className:"flex-1",disabled:c,children:"取消"}),a.jsx(b.Z,{variant:"danger",onClick:o,loading:c,className:"flex-1",children:"确认拉黑"})]})]})})})};var U=t(68197);let S=({userId:e})=>{let s=(0,l.useParams)(),t=(0,l.useRouter)(),{user:x,isLoggedIn:_}=(0,v.E)(),Z=e||s.id,[S,A]=(0,r.useState)(null),[E,D]=(0,r.useState)([]),[q,M]=(0,r.useState)(!0),[z,O]=(0,r.useState)(!1),[G,L]=(0,r.useState)(!1),[T,F]=(0,r.useState)("created_at"),[H,B]=(0,r.useState)(!1),[R,V]=(0,r.useState)(!1),[X,$]=(0,r.useState)(!1),[J,K]=(0,r.useState)(!1),Q=x?._id===Z,W=async()=>{if(Z)try{await navigator.clipboard.writeText(Z),k.C.success("用户ID已复制到剪贴板")}catch(s){let e=document.createElement("textarea");e.value=Z,document.body.appendChild(e),e.select();try{document.execCommand("copy"),k.C.success("用户ID已复制到剪贴板")}catch(e){k.C.error("复制失败，请手动复制")}document.body.removeChild(e)}},Y=async()=>{try{if(M(!0),!Z){k.C.error("用户ID无效"),t.push("/");return}let e=await f.petAPI.getUserInfo({userId:Z});if(e.success){if(A(e.data),_&&x&&x._id!==Z)try{let e=await f.petAPI.getUserFollowing({targetUserId:x._id,limit:1e3});if(e.success){let s=e.data.some(e=>e.following_id===Z);L(s)}}catch(e){console.warn("检查关注状态失败:",e)}}else k.C.error("用户不存在"),t.push("/")}catch(e){console.error("获取用户信息失败:",e),k.C.error("获取用户信息失败"),t.push("/")}finally{M(!1)}},ee=async()=>{try{O(!0);let e=await f.petAPI.getUserPosts({targetUserId:Z,limit:50,offset:0});e.success&&D(e.data||[])}catch(e){console.error("获取用户帖子失败:",e)}finally{O(!1)}},es=async()=>{if(!_){k.C.warning("请先登录");return}try{let e=await f.petAPI.toggleFollow({targetUserId:Z});e.success?(L("followed"===e.action),k.C.success(e.message),S&&A(s=>s?{...s,followers_count:(s.followers_count||0)+("followed"===e.action?1:-1)}:null)):k.C.error(e.message||"操作失败")}catch(e){k.C.error(e.message||"操作失败")}};if((0,r.useEffect)(()=>{Z&&Y()},[Z]),(0,r.useEffect)(()=>{Z&&ee()},[Z,T]),(0,r.useEffect)(()=>{if(Z&&_&&x&&S){let e=async()=>{try{let e=await f.petAPI.getUserFollowing({targetUserId:x._id,limit:1e3});if(e.success){let s=e.data.some(e=>e.following_id===Z);L(s)}}catch(e){console.warn("检查关注状态失败:",e)}};x._id!==Z&&e()}},[_,x,Z,S]),q)return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex items-center h-16",children:a.jsx("button",{onClick:()=>t.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"h-5 w-5"})})})})}),a.jsx(w.LL,{})]});if(!S)return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),a.jsx(b.Z,{onClick:()=>t.push("/"),children:"返回首页"})]})});let et=[{value:"created_at",label:"最新",icon:c.Z},{value:"likes_count",label:"最受欢迎",icon:n.Z},{value:"wants_count",label:"收藏最多",icon:d.Z}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>t.push("/"),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"h-5 w-5"})}),a.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:S.nickname})]}),Q?a.jsx(b.Z,{variant:"outline",icon:a.jsx(o.Z,{className:"h-4 w-4"}),onClick:()=>t.push("/profile"),children:"个人设置"}):_&&(0,a.jsxs)("div",{className:"relative",children:[a.jsx("button",{onClick:()=>B(!H),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(m,{className:"h-5 w-5 text-gray-600"})}),H&&(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,a.jsxs)("button",{onClick:()=>{V(!0),B(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100",children:[a.jsx(u.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"举报用户"})]}),(0,a.jsxs)("button",{onClick:()=>{$(!0),B(!1)},className:"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[a.jsx(h.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"拉黑用户"})]})]})]})]})})}),a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-6",children:[a.jsx("div",{className:"relative",children:S.avatar_url?a.jsx("img",{src:S.avatar_url,alt:S.nickname,className:"w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"}):a.jsx("div",{className:"w-24 h-24 rounded-full bg-gray-300 border-4 border-white shadow-lg flex items-center justify-center",children:a.jsx("span",{className:"text-2xl font-bold text-gray-600",children:S.nickname.charAt(0).toUpperCase()})})}),a.jsx("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:S.nickname}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3 text-gray-600",children:[a.jsx(p.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{className:"text-sm",children:["ID: ",Z]}),a.jsx("button",{onClick:W,className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"复制用户ID",children:a.jsx(g.Z,{className:"w-4 h-4 text-gray-500 hover:text-blue-600"})})]}),S.bio?a.jsx("p",{className:"text-gray-600 max-w-md",children:S.bio}):a.jsx("p",{className:"text-gray-400 italic",children:Q?"点击添加简介，让大家了解你":"这个人很懒，什么都没留下"})]}),!Q&&_&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(b.Z,{onClick:es,variant:G?"outline":"primary",icon:G?a.jsx(y,{className:"h-4 w-4"}):a.jsx(j.Z,{className:"h-4 w-4"}),children:G?"已关注":"关注"}),a.jsx("div",{className:"relative",children:a.jsx(b.Z,{variant:"outline",onClick:()=>B(!H),icon:a.jsx(m,{className:"h-4 w-4"})})})]})]})}),(0,a.jsxs)("div",{className:"mt-6 flex justify-center space-x-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,C.uf)(S.total_likes||0)}),a.jsx("div",{className:"text-sm text-gray-500",children:"获赞"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,C.uf)(S.followers_count||0)}),a.jsx("div",{className:"text-sm text-gray-500",children:"粉丝"})]}),(0,a.jsxs)("div",{className:(0,C.cn)("text-center",Q&&"cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"),onClick:()=>Q&&K(!0),title:Q?"点击查看关注列表":void 0,children:[a.jsx("div",{className:"text-2xl font-bold text-gray-900",children:(0,C.uf)(S.following_count||0)}),a.jsx("div",{className:"text-sm text-gray-500",children:"关注"})]})]})]})})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["发布的宠物 (",S.posts_count||0,")"]}),a.jsx("div",{className:"flex space-x-2",children:et.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>F(e.value),className:(0,C.cn)("flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",T===e.value?"bg-primary-600 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-200"),children:[a.jsx(s,{className:"h-4 w-4"}),a.jsx("span",{children:e.label})]},e.value)})})]}),z?a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Array.from({length:8}).map((e,s)=>a.jsx(w.gG,{},s))}):E.length>0?a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:E.map(e=>a.jsx(N.Z,{post:e},e._id))}):a.jsx("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[a.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),a.jsx("p",{className:"text-lg font-medium mb-2",children:Q?"还没有发布宠物":"TA还没有发布宠物"}),a.jsx("p",{className:"text-sm",children:Q?"快去发布第一只宠物吧！":"期待TA的第一次分享"}),Q&&a.jsx(b.Z,{className:"mt-4",onClick:()=>t.push("/upload"),children:"发布宠物"})]})})]}),a.jsx(P,{isOpen:R,onClose:()=>V(!1),targetUserId:Z,targetUserName:S?.nickname||"",onSuccess:()=>{k.C.success("举报提交成功"),V(!1)}}),a.jsx(I,{isOpen:X,onClose:()=>$(!1),targetUserId:Z,targetUserName:S?.nickname||"",onSuccess:()=>{k.C.success("已拉黑该用户"),L(!1),$(!1),t.push("/")}}),a.jsx(U.Z,{isOpen:J,onClose:()=>K(!1),userId:x?._id||""})]})}},28676:(e,s,t)=>{"use strict";t.d(s,{fe:()=>o,u_:()=>d});var a=t(10326),r=t(17577),l=t(60962),i=t(94019),c=t(28295),n=t(99837);let d=({isOpen:e,onClose:s,title:t,children:d,size:o="md",showCloseButton:x=!0,closeOnOverlayClick:m=!0,className:u})=>{if((0,r.useEffect)(()=>{let t=e=>{"Escape"===e.key&&s()};return e&&(document.addEventListener("keydown",t),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",t),document.body.style.overflow="unset"}},[e,s]),!e)return null;let h=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:m?s:void 0}),(0,a.jsxs)("div",{className:(0,c.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o],u),onClick:e=>e.stopPropagation(),children:[(t||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[t&&a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:t}),x&&a.jsx(n.Z,{variant:"ghost",size:"sm",onClick:s,className:"p-1 hover:bg-gray-100 rounded-full",children:a.jsx(i.Z,{className:"h-5 w-5"})})]}),a.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,l.createPortal)(h,document.body)},o=({children:e,className:s})=>a.jsx("div",{className:(0,c.cn)("p-4",s),children:e})},20914:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,dynamicParams:()=>i,generateStaticParams:()=>l});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\profile\[id]\ProfilePageClient.tsx#default`);async function l(){return[{id:"eea1754d6873ce81053b899e0f3aa469"},{id:"2ed3518f6875a7f905582dfd0fc94e98"},{id:"c611b94668776114057326f5172f0bc5"},{id:"d77d384f6877626a0574989b7124319b"}]}let i=!0;function c({params:e}){return a.jsx(r,{userId:e.id})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,946,240,535,916],()=>t(89313));module.exports=a})();