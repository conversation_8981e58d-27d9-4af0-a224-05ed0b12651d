(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[676],{98802:function(e,r,a){Promise.resolve().then(a.bind(a,79867))},32660:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53581:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},91723:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},95252:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},88997:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},83774:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},82718:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},13041:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},73247:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88906:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},82023:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},32489:function(e,r,a){"use strict";a.d(r,{Z:function(){return t}});let t=(0,a(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},99376:function(e,r,a){"use strict";var t=a(35475);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(r,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},79867:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return K}});var t=a(57437),s=a(2265),o=a(99376),n=a(32660),i=a(88941),c=a(92827),l=a(56334),d=a(32489),m=a(53581);let g={maxImageSize:5,maxImagesPerPost:9,allowedImageTypes:["image/jpeg","image/png","image/webp"]},y=()=>{try{let e=localStorage.getItem("systemSettings");if(e){let r=JSON.parse(e);return{maxImageSize:r.maxImageSize||g.maxImageSize,maxImagesPerPost:r.maxImagesPerPost||g.maxImagesPerPost,allowedImageTypes:r.allowedImageTypes||g.allowedImageTypes}}return g}catch(e){return console.error("获取系统设置失败:",e),g}},u=e=>e>=1024?"".concat((e/1024).toFixed(1),"GB"):"".concat(e,"MB"),p=e=>{let r={"image/jpeg":"JPG","image/png":"PNG","image/webp":"WebP","image/gif":"GIF"};return e.map(e=>r[e]||e.replace("image/","").toUpperCase()).filter(Boolean).join("、")},h=(e,r)=>{if(!r.allowedImageTypes.includes(e.type)){let e=p(r.allowedImageTypes);return{valid:!1,error:"不支持的文件格式，请上传 ".concat(e," 格式的图片")}}let a=1048576*r.maxImageSize;return e.size>a?{valid:!1,error:"文件大小超过限制，单张图片不能超过 ".concat(u(r.maxImageSize))}:{valid:!0}};var x=a(68661),f=e=>{let{images:r,onImagesChange:a,maxImages:o=9,className:n,draftImages:i=[]}=e,[c,l]=(0,s.useState)(()=>y());console.log("ImageUpload 组件渲染:",{imagesCount:r.length,draftImagesCount:i.length,draftImages:i.map(e=>e?e.substring(0,50)+"...":"null"),systemSettings:c});let[g,f]=(0,s.useState)(!1),b=(0,s.useRef)(null);(0,s.useEffect)(()=>{i.length>0&&(console.log("draftImages 更新:",i.length,"张图片"),f(e=>e))},[i]),(0,s.useEffect)(()=>{let e=()=>{l(y())};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[]);let C=e=>{if(!e)return;let t=[],s=[];Array.from(e).forEach(e=>{let r=h(e,c);r.valid?t.push(e):s.push("".concat(e.name,": ").concat(r.error))}),s.length>0&&console.warn("文件验证失败:",s);let n=Math.min(o,c.maxImagesPerPost)-r.length,i=t.slice(0,n);i.length>0&&a([...r,...i]),t.length>n&&console.warn("只能上传 ".concat(n," 张图片，已忽略多余的文件"))},N=(e,r)=>{try{if(i[r])return console.log("使用草稿图片数据 ".concat(r,":"),i[r].substring(0,50)+"..."),i[r];if(e instanceof File&&e.size>0)return console.log("使用File对象创建预览 ".concat(r,":"),e.name,e.size),URL.createObjectURL(e);return console.log("使用默认图片 ".concat(r)),"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"}catch(e){if(console.error("获取图片预览URL失败:",e),i[r])return i[r];return"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"}},v=e=>{a(r.filter((r,a)=>a!==e))};return(0,t.jsxs)("div",{className:(0,x.cn)("space-y-4",n),children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[r.map((e,r)=>(0,t.jsxs)("div",{className:"relative aspect-square",children:[(0,t.jsx)("img",{src:N(e,r),alt:"预览 ".concat(r+1),className:"w-full h-full object-cover rounded-lg border border-gray-200",onError:e=>{e.currentTarget.src="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"}}),(0,t.jsx)("button",{onClick:()=>v(r),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors",children:(0,t.jsx)(d.Z,{className:"h-3 w-3"})}),(0,t.jsx)("div",{className:"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:r+1})]},r)),r.length<o&&(0,t.jsxs)("div",{onClick:()=>{var e;null===(e=b.current)||void 0===e||e.click()},onDragOver:e=>{e.preventDefault(),f(!0)},onDragLeave:e=>{e.preventDefault(),f(!1)},onDrop:e=>{e.preventDefault(),f(!1),C(e.dataTransfer.files)},className:(0,x.cn)("aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors",g?"border-primary-500 bg-primary-50":"border-gray-300 hover:border-primary-400 hover:bg-gray-50"),children:[(0,t.jsx)(m.Z,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-500 text-center",children:0===r.length?"添加图片":"继续添加"}),(0,t.jsxs)("span",{className:"text-xs text-gray-400 mt-1",children:[r.length,"/",o]})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,t.jsxs)("p",{children:["• 最多可上传 ",Math.min(o,c.maxImagesPerPost)," 张图片"]}),(0,t.jsxs)("p",{children:["• 支持 ",p(c.allowedImageTypes)," 格式，单张图片不超过 ",u(c.maxImageSize)]}),(0,t.jsx)("p",{children:"• 第一张图片将作为封面显示"}),(0,t.jsx)("p",{children:"• 可拖拽图片到上传区域"})]}),(0,t.jsx)("input",{ref:b,type:"file",accept:c.allowedImageTypes.join(","),multiple:!0,onChange:e=>C(e.target.files),className:"hidden"})]})},b=a(83774),C=e=>{let{value:r,onChange:a,error:o,userId:n}=e,[i,c]=(0,s.useState)(""),[l,d]=(0,s.useState)([]),[m,g]=(0,s.useState)(!1),[y,u]=(0,s.useState)(""),p=["北京","上海","天津","重庆","广州","深圳","杭州","成都","武汉","西安","南京","苏州","长沙","郑州","济南","青岛","大连","沈阳","哈尔滨","长春","石家庄","太原","呼和浩特","兰州","西宁","银川","乌鲁木齐","合肥","福州","厦门","南昌","昆明","贵阳","拉萨","海口","南宁","宁波","温州","嘉兴","绍兴","金华","台州","丽水","湖州","衢州","舟山","无锡","常州","徐州","南通","连云港","淮安","盐城","扬州","镇江","泰州","宿迁"];(0,s.useEffect)(()=>{if(n){let e=localStorage.getItem("address_".concat(n));e&&(u(e),!r&&e&&(a("#".concat(e)),c(e)))}},[n,a]),(0,s.useEffect)(()=>{r&&c(r.replace(/^#/,""))},[r]);let h=e=>{c(e),e.length>0?(d(p.filter(r=>r.includes(e)||r.toLowerCase().includes(e.toLowerCase())).slice(0,8)),g(!0)):(d([]),g(!1)),a(e?"#".concat(e):"")},x=e=>{c(e),g(!1),a("#".concat(e))};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:["发布地区 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),y&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"您的常用地址："}),(0,t.jsxs)("button",{type:"button",onClick:()=>x(y),className:"px-3 py-1 text-sm rounded-full border transition-colors ".concat(i===y?"bg-blue-500 text-white border-blue-500":"bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100"),children:["#",y]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(b.Z,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{type:"text",value:i,onChange:e=>h(e.target.value),onFocus:()=>i&&g(l.length>0),onBlur:()=>{setTimeout(()=>{g(!1)},200)},className:"block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(o?"border-red-300":"border-gray-300"),placeholder:"输入发布地区，建议精确到县/区一级，如：北京市朝阳区"}),m&&l.length>0&&(0,t.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto",children:l.map(e=>(0,t.jsxs)("button",{type:"button",onClick:()=>x(e),className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-2",children:[(0,t.jsx)(b.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{children:["#",e]})]},e))})]}),r&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-blue-50 rounded-lg",children:[(0,t.jsx)(b.Z,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("span",{className:"text-sm text-blue-700",children:["位置标签: ",(0,t.jsx)("span",{className:"font-medium",children:r})]})]}),o&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:o}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:(0,t.jsx)("p",{children:"• 系统会自动添加#标签，便于搜索和筛选"})})]})},N=a(82718),v=a(13041),j=e=>{let{value:r,onChange:a,petType:o="",required:n=!1,error:i,className:l}=e,[d,m]=(0,s.useState)(()=>r.wechat?"wechat":r.phone&&!r.wechat?"phone":"wechat");(0,s.useEffect)(()=>{!r[d]&&(r.wechat?m("wechat"):r.phone&&m("phone"))},[r,d]);let g=["breeding","selling","lost","wanted"].includes(o),y=n||g,u=[{type:"wechat",label:"微信号",icon:N.Z,placeholder:"请输入微信号",pattern:/^[a-zA-Z][\w-]{5,19}$/,errorMessage:"微信号格式：6-20位，字母开头，可包含字母、数字、下划线、减号"},{type:"phone",label:"手机号",icon:v.Z,placeholder:"请输入手机号",pattern:/^1[3-9]\d{9}$/,errorMessage:"请输入正确的手机号格式"}],p=u.find(e=>e.type===d),h=r[d]||"",f=e=>{m(e)},b=e=>{let t={...r};e.trim()?t[d]=e.trim():delete t[d],a(t)},C=h.trim()?p.pattern.test(h.trim())?null:p.errorMessage:y?"请填写联系方式":null;return(0,t.jsxs)("div",{className:(0,x.cn)("space-y-4",l),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:["联系方式 ",y&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),g&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["breeding"===o&&"配种需要联系方式","selling"===o&&"出售需要联系方式","lost"===o&&"寻回需要联系方式","wanted"===o&&"求购需要联系方式"]})]}),(0,t.jsx)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:u.map(e=>{let a=e.icon,s=d===e.type,o=r[e.type]&&r[e.type].trim().length>0;return(0,t.jsxs)("button",{type:"button",onClick:()=>f(e.type),className:(0,x.cn)("flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-200","hover:bg-white hover:shadow-sm",s?"bg-white text-primary-600 shadow-sm ring-1 ring-primary-200":"text-gray-600 hover:text-gray-900",o&&!s&&"text-green-600"),disabled:!1,children:[(0,t.jsx)(a,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:e.label}),o&&(0,t.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full",title:"已填写"})]})]},e.type)})}),(0,t.jsx)(c.I,{placeholder:p.placeholder,value:h,onChange:e=>b(e.target.value),leftIcon:(0,t.jsx)(p.icon,{className:"h-4 w-4"}),error:C||i,type:"phone"===d?"tel":"text"}),(r.wechat||r.phone)&&(0,t.jsxs)("div",{className:"text-xs bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-blue-800 font-medium mb-1",children:"已保存的联系方式："}),(0,t.jsxs)("div",{className:"space-y-1",children:[r.wechat&&(0,t.jsxs)("p",{className:"text-blue-700",children:["• 微信号: ",r.wechat,"wechat"===d&&(0,t.jsx)("span",{className:"ml-2 text-blue-500",children:"(当前显示)"})]}),r.phone&&(0,t.jsxs)("p",{className:"text-blue-700",children:["• 手机号: ",r.phone,"phone"===d&&(0,t.jsx)("span",{className:"ml-2 text-blue-500",children:"(当前显示)"})]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[!g&&(0,t.jsx)("p",{children:"• 如需要其他用户联系您，请填写联系方式"}),g&&(0,t.jsx)("p",{children:"• 请确保联系方式准确，以便买家/配种方/失主/卖家联系您"}),(0,t.jsx)("p",{children:"• 平台不会公开您的联系方式，仅在用户点击联系时显示"}),(0,t.jsx)("p",{children:"• 您可以同时填写微信号和手机号，用户可选择联系方式"})]})]})},w=a(39763);let A=(0,w.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var _=a(95252),k=a(88997),S=a(73247);let I=(0,w.Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var M=e=>{let{value:r,onChange:a,gender:s="",onGenderChange:o,error:n,className:i}=e,c=[{value:"",label:"展示分享",description:"分享宝贝照片，不涉及交易",icon:A,color:"gray"},{value:"selling",label:"出售",description:"出售宝贝，需要联系方式",icon:_.Z,color:"green"},{value:"breeding",label:"配种",description:"寻找配种对象，需要联系方式和性别",icon:k.Z,color:"pink"},{value:"lost",label:"寻回",description:"寻找走失宝贝，需要联系方式",icon:S.Z,color:"orange"},{value:"wanted",label:"求购",description:"发布求购信息，需要联系方式",icon:I,color:"purple"}],l=(e,r)=>{let a={gray:r?"bg-gray-100 border-gray-300 text-gray-900":"border-gray-200 text-gray-600 hover:border-gray-300",green:r?"bg-green-50 border-green-300 text-green-900":"border-gray-200 text-gray-600 hover:border-green-200",pink:r?"bg-pink-50 border-pink-300 text-pink-900":"border-gray-200 text-gray-600 hover:border-pink-200",orange:r?"bg-orange-50 border-orange-300 text-orange-900":"border-gray-200 text-gray-600 hover:border-orange-200",blue:r?"bg-blue-50 border-blue-300 text-blue-900":"border-gray-200 text-gray-600 hover:border-blue-200",purple:r?"bg-purple-50 border-purple-300 text-purple-900":"border-gray-200 text-gray-600 hover:border-purple-200"};return a[e]||a.gray};return(0,t.jsxs)("div",{className:(0,x.cn)("space-y-4",i),children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"发布类型"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3",children:c.map(e=>{let s=e.icon,n=r===e.value;return(0,t.jsx)("button",{type:"button",onClick:()=>{a(e.value),"breeding"!==e.value&&o&&o("")},className:(0,x.cn)("p-4 border-2 rounded-lg text-left transition-all duration-200",l(e.color,n),"hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(s,{className:"h-5 w-5 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.label}),(0,t.jsx)("div",{className:"text-xs mt-1 opacity-75",children:e.description})]})]})},e.value)})}),"breeding"===r&&o&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:["宝贝性别 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("div",{className:"flex space-x-3",children:[{value:"male",label:"雄性 ♂",color:"blue"},{value:"female",label:"雌性 ♀",color:"pink"}].map(e=>{let r=s===e.value;return(0,t.jsx)("button",{type:"button",onClick:()=>o(e.value),className:(0,x.cn)("flex-1 py-3 px-4 border-2 rounded-lg text-center transition-all duration-200",l(e.color,r),"hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"),children:(0,t.jsx)("div",{className:"font-medium text-sm",children:e.label})},e.value)})}),"breeding"===r&&!s&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:"配种信息需要选择宝贝性别"})]}),n&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:n}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:(0,t.jsx)("p",{children:"• 除展示分享外其他发布类型都需填写联系方式"})})]})},P=a(9356),E=a(98011),L=a(83565);let Z={柴犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},泰迪犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},贵宾犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},比熊犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},博美犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},吉娃娃:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},法国斗牛犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},巴哥犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"small_dogs",secondaryCategoryName:"小型犬",confidence:1},柯基犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},边境牧羊犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},可卡犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},英国斗牛犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"medium_dogs",secondaryCategoryName:"中型犬",confidence:1},拉布拉多犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},金毛寻回犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},哈士奇:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},萨摩耶:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},阿拉斯加犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"large_dogs",secondaryCategoryName:"大型犬",confidence:1},德国牧羊犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"working_dogs",secondaryCategoryName:"工作犬",confidence:1},罗威纳犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"working_dogs",secondaryCategoryName:"工作犬",confidence:1},杜宾犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"working_dogs",secondaryCategoryName:"工作犬",confidence:1},中华田园犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大黄狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大白狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大黑狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},花狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},草狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},唐狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},柴狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},大笨狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},太行犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"domestic_dogs",secondaryCategoryName:"田园犬",confidence:1},串串犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"mixed_dogs",secondaryCategoryName:"串串犬",confidence:1},混血犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",secondaryCategory:"mixed_dogs",secondaryCategoryName:"串串犬",confidence:1},英国短毛猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},美国短毛猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},俄罗斯蓝猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},暹罗猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},银渐层:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},金渐层:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"short_hair_cats",secondaryCategoryName:"短毛猫",confidence:1},波斯猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},布偶猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},缅因猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},挪威森林猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"long_hair_cats",secondaryCategoryName:"长毛猫",confidence:1},狸花猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},橘猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},三花猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},奶牛猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},中华田园猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",secondaryCategory:"domestic_cats",secondaryCategoryName:"田园猫",confidence:1},虎皮鹦鹉:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"parrots",secondaryCategoryName:"鸦鹉类",confidence:1},玄凤鹦鹉:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"parrots",secondaryCategoryName:"鸦鹉类",confidence:1},牡丹鹦鹉:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"parrots",secondaryCategoryName:"鸦鹉类",confidence:1},金丝雀:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"songbirds",secondaryCategoryName:"鸣禽类",confidence:1},文鸟:{primaryCategory:"birds",primaryCategoryName:"鸟类",secondaryCategory:"songbirds",secondaryCategoryName:"鸣禽类",confidence:1},金鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},锦鲤:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},孔雀鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},斗鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",secondaryCategory:"freshwater_fish",secondaryCategoryName:"淡水鱼",confidence:1},仓鼠:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"hamsters",secondaryCategoryName:"仓鼠类",confidence:1},龙猫:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"small_mammals",secondaryCategoryName:"小型哺乳",confidence:1},兔子:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"rabbits",secondaryCategoryName:"兔子类",confidence:1},荷兰猪:{primaryCategory:"small_pets",primaryCategoryName:"小宠",secondaryCategory:"rodent_pets",secondaryCategoryName:"鼠类宠物",confidence:1},巴西龟:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"turtles",secondaryCategoryName:"龟类",confidence:1},草龟:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"turtles",secondaryCategoryName:"龟类",confidence:1},蜥蜴:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"lizards",secondaryCategoryName:"蜥蜴类",confidence:1},守宫:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",secondaryCategory:"lizards",secondaryCategoryName:"蜥蜴类",confidence:1}},D={拉拉:"拉布拉多犬",拉布拉多:"拉布拉多犬",金毛:"金毛寻回犬",泰迪:"泰迪犬",贵宾:"贵宾犬",比熊:"比熊犬",博美:"博美犬",二哈:"哈士奇",萨摩:"萨摩耶",边牧:"边境牧羊犬",德牧:"德国牧羊犬",法斗:"法国斗牛犬",英斗:"英国斗牛犬",田园犬:"中华田园犬",土狗:"中华田园犬",本地狗:"中华田园犬",黄狗:"大黄狗",白狗:"大白狗",黑狗:"大黑狗",花花狗:"花狗",土狗子:"中华田园犬",英短:"英国短毛猫",美短:"美国短毛猫",布偶:"布偶猫",缅因:"缅因猫",俄蓝:"俄罗斯蓝猫",狸花:"狸花猫",三花:"三花猫",奶牛:"奶牛猫",虎皮:"虎皮鹦鹉",玄凤:"玄凤鹦鹉",牡丹:"牡丹鹦鹉",天竺鼠:"荷兰猪"},T={狗狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",confidence:.8,isGeneralCategory:!0},狗:{primaryCategory:"dogs",primaryCategoryName:"狗狗",confidence:.8,isGeneralCategory:!0},犬:{primaryCategory:"dogs",primaryCategoryName:"狗狗",confidence:.8,isGeneralCategory:!0},猫咪:{primaryCategory:"cats",primaryCategoryName:"猫咪",confidence:.8,isGeneralCategory:!0},猫:{primaryCategory:"cats",primaryCategoryName:"猫咪",confidence:.8,isGeneralCategory:!0},鸟类:{primaryCategory:"birds",primaryCategoryName:"鸟类",confidence:.8,isGeneralCategory:!0},鸟:{primaryCategory:"birds",primaryCategoryName:"鸟类",confidence:.8,isGeneralCategory:!0},水族:{primaryCategory:"aquatic",primaryCategoryName:"水族",confidence:.8,isGeneralCategory:!0},鱼:{primaryCategory:"aquatic",primaryCategoryName:"水族",confidence:.8,isGeneralCategory:!0},小宠:{primaryCategory:"small_pets",primaryCategoryName:"小宠",confidence:.8,isGeneralCategory:!0},爬宠:{primaryCategory:"reptiles",primaryCategoryName:"爬宠",confidence:.8,isGeneralCategory:!0},两栖:{primaryCategory:"amphibians",primaryCategoryName:"两栖",confidence:.8,isGeneralCategory:!0},昆虫:{primaryCategory:"insects",primaryCategoryName:"昆虫",confidence:.8,isGeneralCategory:!0}};var z=a(60719);let B="pet_breed_input_history";var O=e=>{let{value:r,onChange:a,error:o,placeholder:n="请输入宠物品种，如：拉布拉多、英短蓝猫等"}=e,[i,c]=(0,s.useState)(null),[l,d]=(0,s.useState)([]),[m,g]=(0,s.useState)(!1),y=()=>{try{let e=localStorage.getItem(B);return e?JSON.parse(e):[]}catch(e){return[]}},u=e=>{try{localStorage.setItem(B,JSON.stringify(e))}catch(e){}},p=e=>{if(!e.trim())return;let r=[e,...y().filter(r=>r!==e)].slice(0,10);d(r),u(r)};(0,s.useEffect)(()=>{d(y())},[]),(0,s.useEffect)(()=>{if(!r||!r.trim()){c(null),a(r,null);return}let e=function(e){if(!e||!e.trim())return null;let r=e.trim();if(Z[r])return Z[r];let a=D[r];if(a&&Z[a])return Z[a];if(T[r])return T[r];for(let[e,a]of Object.entries(Z))if(e.includes(r)||r.includes(e))return{...a,confidence:.9};for(let[e,a]of Object.entries(D))if(e.includes(r)||r.includes(e))return{...Z[a],confidence:.9};for(let[e,a]of Object.entries(T))if(e.includes(r)||r.includes(e))return{...a,confidence:.7};return{primaryCategory:"others",primaryCategoryName:"其他",confidence:.5}}(r);c(e),a(r,e)},[r]);let h=e=>{a(e,null),g(!1)},x=()=>{r&&r.trim()&&p(r.trim())},f=(0,z.O)(()=>{g(!1)});return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("input",{type:"text",value:r,onChange:e=>{a(e.target.value,null)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),x())},onBlur:x,placeholder:n,className:"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ".concat(o?"border-red-500":"border-gray-300")}),o&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:o})]}),(0,t.jsxs)("div",{className:"relative",ref:f,children:[(0,t.jsx)("button",{type:"button",onClick:()=>{g(!m)},className:"px-3 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看输入历史",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),m&&l.length>0&&(0,t.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,t.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,t.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"输入历史"]}),(0,t.jsx)("button",{onClick:()=>{d([]),u([]),g(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),(0,t.jsx)("div",{className:"max-h-48 overflow-y-auto",children:l.map((e,r)=>(0,t.jsx)("div",{onClick:()=>h(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors",children:(0,t.jsx)("span",{className:"text-sm",children:e})},"".concat(e,"-").concat(r)))})]})]})]}),i&&(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,t.jsx)("div",{className:"flex items-center text-sm text-green-800",children:(0,t.jsx)("span",{className:"font-medium",children:"智能分类结果："})}),(0,t.jsxs)("div",{className:"mt-1 flex items-center",children:[(0,t.jsx)("span",{className:"text-green-600 mr-2",children:"✅"}),(0,t.jsxs)("span",{className:"font-medium text-green-800",children:[i.primaryCategoryName," > ",i.secondaryCategoryName||"其他"]}),(0,t.jsxs)("span",{className:"ml-2 text-green-600",children:["(",Math.round(100*i.confidence),"%匹配)"]})]}),(0,t.jsx)("div",{className:"mt-1 text-sm text-green-700",children:i.isGeneralCategory?"\uD83D\uDCA1 输入具体品种可以让其他用户更精准地找到您的宝贝":"\uD83D\uDCA1 分类识别成功！"})]}),!r&&(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,t.jsx)("p",{children:"\uD83D\uDCA1 输入具体品种有助于其他用户更快找到您的宝贝"}),l.length>0&&(0,t.jsx)("p",{children:"\uD83D\uDD52 点击历史记录按钮查看之前的输入"})]})]})};let R=()=>new Promise(e=>{let r=new Image;r.onload=()=>e(!0),r.onerror=()=>e(!1),r.src="data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A="}),G=async()=>{if(await R())return"avif";let e=document.createElement("canvas");return(e.width=1,e.height=1,0===e.toDataURL("image/webp").indexOf("data:image/webp"))?"webp":"jpeg"},F=async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{maxWidth:a=1e3,maxHeight:t=1e3,quality:s=.75,outputFormat:o="auto",generateThumbnails:n=!1,thumbnailSizes:i=[150,300,600]}=r;return new Promise((r,c)=>{let l=document.createElement("canvas"),d=l.getContext("2d"),m=new Image;m.onload=async()=>{try{let{width:c,height:g}=m,{targetWidth:y,targetHeight:u,targetQuality:p}=q(c,g,a,t,s),h="auto"===o?await G():o,x="image/".concat(h);console.log("选择的图片格式: ".concat(h)),l.width=y,l.height=u,d&&(d.imageSmoothingEnabled=!0,d.imageSmoothingQuality="high",d.drawImage(m,0,0,y,u));let f=await W(l,e.name,x,p,h),b=[];n&&(b=await U(m,e.name,i,h));let C=Math.round((1-f.size/e.size)*100);r({original:e,compressed:f,thumbnails:b,compressionRatio:C,outputFormat:h})}catch(e){c(e)}},m.onerror=()=>c(Error("图片加载失败")),m.src=URL.createObjectURL(e)})},q=(e,r,a,t,s)=>{let o=e,n=r,i=s,c=e/r;if(e>2e3||r>2e3||e*r*3>5e6){let c=Math.min(a/e,t/r,.6);o=Math.floor(e*c),n=Math.floor(r*c),i=Math.max(.6,s-.15)}else e>a||r>t?(c>1?n=(o=a)/c:o=(n=t)*c,i=Math.max(.65,s-.1)):e>800||r>800?(o=Math.floor(.8*e),n=Math.floor(.8*r),i=Math.max(.7,s-.05)):i=Math.max(.8,s);return{targetWidth:o,targetHeight:n,targetQuality:i}},W=(e,r,a,t,s)=>new Promise((o,n)=>{e.toBlob(e=>{e?o(new File([e],r.replace(/\.[^/.]+$/,".".concat(s)),{type:a,lastModified:Date.now()})):n(Error("Canvas转换失败"))},a,t)}),U=async(e,r,a,t)=>{let s=[],o="image/".concat(t);for(let n of a){let a=document.createElement("canvas"),i=a.getContext("2d"),{width:c,height:l}=e,d=c/l,m=n,g=n;d>1?g=n/d:m=n*d,a.width=m,a.height=g,i&&(i.imageSmoothingEnabled=!0,i.imageSmoothingQuality="high",i.drawImage(e,0,0,m,g));let y=await W(a,r.replace(/\.[^/.]+$/,"_thumb_".concat(n,".").concat(t)),o,.8,t);s.push(y)}return s};var H=a(80240),Q=a(88906),X=a(82023),Y=a(91723),J=a(65302);function V(e){let{isOpen:r,onComplete:a,postTitle:o="您的宠物"}=e,[n,i]=(0,s.useState)(0),[c,l]=(0,s.useState)(0),[d,m]=(0,s.useState)(12),g=[{icon:Q.Z,text:"内容安全检查",duration:2e3},{icon:X.Z,text:"图片质量分析",duration:3e3},{icon:Y.Z,text:"信息完整性验证",duration:2e3},{icon:J.Z,text:"最终审核确认",duration:3e3}];return((0,s.useEffect)(()=>{if(!r){i(0),l(0),m(12);return}let e=Math.floor(5e3*Math.random())+8e3;m(Math.ceil(e/1e3));let t=0,s=setInterval(()=>{let r=Math.min((t+=100)/e*100,100);i(r),m(Math.max(0,Math.ceil((e-t)/1e3))),l(Math.min(Math.floor(r/25),g.length-1)),t>=e&&(clearInterval(s),setTimeout(()=>{a()},500))},100);return()=>clearInterval(s)},[r,a]),r)?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl max-w-md w-full p-8 text-center",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(Q.Z,{className:"w-8 h-8 text-blue-600 animate-pulse"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"正在审核您的发布"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm",children:[o," 正在接受平台安全审核"]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"审核进度"}),(0,t.jsxs)("span",{className:"text-sm font-medium text-blue-600",children:[Math.round(n),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300 ease-out",style:{width:"".concat(n,"%")}})}),(0,t.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["预计还需 ",d," 秒"]})]}),(0,t.jsx)("div",{className:"space-y-4 mb-8",children:g.map((e,r)=>{let a=e.icon,s=r===c,o=r<c;return(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ".concat(s?"bg-blue-50 border border-blue-200":o?"bg-green-50 border border-green-200":"bg-gray-50 border border-gray-200"),children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(s?"bg-blue-100 text-blue-600":o?"bg-green-100 text-green-600":"bg-gray-100 text-gray-400"),children:o?(0,t.jsx)(J.Z,{className:"w-5 h-5"}):(0,t.jsx)(a,{className:"w-5 h-5 ".concat(s?"animate-pulse":"")})}),(0,t.jsx)("span",{className:"text-sm font-medium ".concat(s?"text-blue-900":o?"text-green-900":"text-gray-500"),children:e.text}),s&&(0,t.jsx)("div",{className:"ml-auto",children:(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"})}),o&&(0,t.jsx)("div",{className:"ml-auto",children:(0,t.jsx)(J.Z,{className:"w-4 h-4 text-green-600"})})]},r)})}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(Y.Z,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"审核说明"}),(0,t.jsxs)("ul",{className:"text-xs space-y-1",children:[(0,t.jsx)("li",{children:"• 我们正在检查内容是否符合平台规范"}),(0,t.jsx)("li",{children:"• 审核通过后将自动发布到平台"}),(0,t.jsx)("li",{children:"• 请耐心等待，不要关闭页面"})]})]})]})})]})}):null}var K=()=>{let e=(0,o.useRouter)(),r=(0,o.useSearchParams)().get("draftId"),{user:d}=(0,i.E)(),[m,g]=(0,s.useState)(!1),[y,u]=(0,s.useState)({title:"",description:"",images:[],category:"",breed:"",type:"",gender:"",location:"",contact_info:{phone:"",wechat:""}}),[p,h]=(0,s.useState)([]),[x,b]=(0,s.useState)(null),[N,v]=(0,s.useState)(null),[w,A]=(0,s.useState)({}),[_,k]=(0,s.useState)(!1),[S,I]=(0,s.useState)(0),[Z,D]=(0,s.useState)(!1),[T,z]=(0,s.useState)(!1),[B,R]=(0,s.useState)(!1),[G,q]=(0,s.useState)(!1),W=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"image.jpg";try{var a;if(!e||!e.includes(","))throw Error("无效的base64字符串格式");let t=e.split(",");if(2!==t.length)throw Error("base64字符串格式不正确");let s=(null===(a=t[0].match(/:(.*?);/))||void 0===a?void 0:a[1])||"image/jpeg",o=atob(t[1]);if(0===o.length)throw Error("base64解码后数据为空");let n=o.length,i=new Uint8Array(n);for(;n--;)i[n]=o.charCodeAt(n);let c=new File([i],r,{type:s});return console.log("成功转换base64为File: ".concat(r,", 大小: ").concat(c.size," bytes")),c}catch(r){throw console.error("base64转File失败:",r,"原始数据长度:",null==e?void 0:e.length),r}};(0,s.useEffect)(()=>{if(r){let e=setTimeout(async()=>{try{console.log("正在加载草稿，ID:",r);let e=(0,L.eJ)(r);if(console.log("获取到的草稿数据:",e),e){let r=[],a=[];if(e.images&&Array.isArray(e.images))for(let t=0;t<e.images.length;t++){let s=e.images[t];if("string"==typeof s){if(s.startsWith("data:")){a.push(s);try{let e=W(s,"draft_image_".concat(t,".jpg"));r.push(e)}catch(a){console.error("base64转File失败:",a);let e=new File([],"draft_image_".concat(t,".jpg"),{type:"image/jpeg"});r.push(e)}}else if(s.startsWith("http")){a.push(s);let e=new File([],"url_image_".concat(t,".jpg"),{type:"image/jpeg"});r.push(e)}else{a.push("https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center");let e=new File([],"default_image_".concat(t,".jpg"),{type:"image/jpeg"});r.push(e)}}}u({title:e.title||"",description:e.description||"",images:r,category:e.category||"",breed:e.breed||"",type:e.type||"selling",gender:e.gender||"",location:e.location||"",contact_info:e.contact_info||{}}),console.log("设置草稿图片数据:",a),setTimeout(()=>{console.log("延迟设置草稿图片数据:",a),h(a)},0),e.breed&&b(null),P.C.success("草稿已加载")}else console.warn("未找到草稿，ID:",r),P.C.error("未找到指定的草稿")}catch(e){console.error("加载草稿失败:",e),P.C.error("加载草稿失败，请重试")}},100);return()=>clearTimeout(e)}},[r]),(0,s.useEffect)(()=>{if((null==d?void 0:d._id)&&!r){let e=localStorage.getItem("contact_".concat(d._id));if(e)try{let r=JSON.parse(e),a={phone:"phone"===r.type?r.value:"",wechat:"wechat"===r.type?r.value:""};u(e=>({...e,contact_info:a}))}catch(e){console.error("解析联系方式失败:",e)}Y()}},[null==d?void 0:d._id,r]);let U=(e,r)=>{u(a=>({...a,[e]:r})),w[e]&&A(r=>({...r,[e]:""}))},Q=()=>{let e={};0===y.images.length&&(e.images="请至少上传一张图片"),y.breed&&y.breed.trim()||(e.breed="请输入宠物品种"),y.description.trim()?y.description.length>500&&(e.description="描述不能超过500个字符"):e.description="请输入宝贝描述","breeding"!==y.type||y.gender||(e.gender="配种信息需要选择宝贝性别"),y.location&&y.location.trim()?y.location.startsWith("#")||(e.location="位置格式错误，请重新选择"):e.location="请选择或输入发布地区";let r=["breeding","selling","lost","wanted"].includes(y.type||""),{phone:a,wechat:t}=y.contact_info||{};return r&&!(a||t)&&(e.contact="该类型发布需要填写联系方式"),A(e),0===Object.keys(e).length},X=async e=>{let r=Date.now(),a=e.map(async(r,a)=>{try{console.log("开始优化图片 ".concat(a+1,"/").concat(e.length,": ").concat(r.name));let t=await F(r,{maxWidth:1200,maxHeight:1200,quality:.85,outputFormat:"auto"});return console.log("图片 ".concat(r.name," 优化完成，压缩率: ").concat(t.compressionRatio,"%")),console.log("格式: ".concat(t.outputFormat,", 原始大小: ").concat(r.size,", 压缩后: ").concat(t.compressed.size)),await (0,E.uploadFile)(t.compressed)}catch(e){return console.error("图片 ".concat(r.name," 优化失败，使用原图:"),e),await (0,E.uploadFile)(r)}}),t=await Promise.all(a),s=Date.now()-r;return H.Bm.recordApiResponse("imageUpload",s),console.log("所有图片上传完成，总耗时: ".concat(s,"ms")),t},Y=async()=>{if(null==d?void 0:d._id)try{let e=await E.petAPI.checkPostLimit({user_id:d._id});e.success&&v({currentCount:e.currentCount||0,postLimit:e.postLimit||100,isNearLimit:(e.currentCount||0)>=.8*(e.postLimit||100)})}catch(e){console.error("检查帖子数量限制失败:",e)}},J=async()=>{if(console.log("\uD83D\uDE80 开始发布，当前表单数据:",y),console.log("\uD83D\uDD0D 开始表单验证..."),!Q()){console.log("❌ 表单验证失败"),P.C.error("请检查表单信息");return}console.log("✅ 表单验证通过，开始发布...");try{var e,r,a,t,s,o,n;g(!0),P.C.loading("正在上传图片...");let i=await X(y.images);P.C.dismiss(),P.C.loading("正在发布...");let c={title:(null===(e=y.breed)||void 0===e?void 0:e.trim())||"宠物发布",description:y.description.trim(),images:i,category:y.category,breed:(null===(r=y.breed)||void 0===r?void 0:r.trim())||void 0,type:y.type||void 0,gender:"breeding"===y.type?y.gender:void 0,location:(null===(a=y.location)||void 0===a?void 0:a.trim())||void 0,contact_info:{phone:(null===(s=y.contact_info)||void 0===s?void 0:null===(t=s.phone)||void 0===t?void 0:t.trim())||void 0,wechat:(null===(n=y.contact_info)||void 0===n?void 0:null===(o=n.wechat)||void 0===o?void 0:o.trim())||void 0}},l=await E.petAPI.createPost(c);if(l.success)P.C.dismiss(),q(!0),console.log("✅ 帖子创建成功，开始审核流程");else throw Error(l.message||"发布失败")}catch(e){console.error("发布失败:",e),P.C.dismiss(),P.C.error(e.message||"发布失败，请重试")}finally{g(!1)}},K=async()=>{q(!1);try{let{dataCache:e}=await a.e(882).then(a.bind(a,33882));if(e.clear(),["posts_all","posts_".concat(y.category),"posts_".concat(y.type),"posts_latest"].forEach(r=>e.delete(r)),console.log("✅ 缓存已清理，新帖子将立即显示"),r)try{let{deleteDraft:e}=await Promise.resolve().then(a.bind(a,83565));e(r),console.log("✅ 草稿已删除:",r)}catch(e){console.warn("删除草稿失败:",e)}}catch(e){console.warn("清理缓存失败:",e)}P.C.success("发布成功！您的宠物已通过审核并发布到平台"),setTimeout(()=>{e.push("/")},1500)},$=async()=>{var a;if(!(null===(a=y.breed)||void 0===a?void 0:a.trim())){P.C.error("请至少填写宠物品种");return}try{g(!0),r?(await (0,L.XD)(r,y),P.C.success("草稿已更新")):(await (0,L.DO)(y),P.C.success("已保存到待发布")),e.back()}catch(e){console.error("保存草稿失败:",e),P.C.error("保存失败，请重试")}finally{g(!1)}};return(0,t.jsx)(i.Y,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,t.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"flex items-center h-16",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,t.jsx)(n.Z,{className:"h-5 w-5"})}),(0,t.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"发布宝贝"})]})})})}),(0,t.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[N&&(0,t.jsxs)("div",{className:"mb-4 p-4 rounded-lg border ".concat(N.isNearLimit?"bg-yellow-50 border-yellow-200":"bg-blue-50 border-blue-200"),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(N.isNearLimit?"bg-yellow-500":"bg-blue-500")}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(N.isNearLimit?"text-yellow-800":"text-blue-800"),children:["帖子数量：",N.currentCount,"/",N.postLimit]})]}),(0,t.jsx)("p",{className:"mt-1 text-xs ".concat(N.isNearLimit?"text-yellow-700":"text-blue-700"),children:N.isNearLimit?"您的帖子数量接近上限，发布新帖子时最早的帖子可能会被自动下架并转为草稿":"您可以继续发布帖子，系统会自动管理帖子数量"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:["宝贝图片 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)(f,{images:y.images,onImagesChange:e=>U("images",e),draftImages:p}),w.images&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.images})]}),(0,t.jsx)(O,{value:y.breed||"",onChange:(e,r)=>{let a=(null==r?void 0:r.secondaryCategory)||(null==r?void 0:r.primaryCategory)||"";u(r=>({...r,breed:e,category:a})),b(r),w.breed&&A(e=>({...e,breed:""})),w.category&&A(e=>({...e,category:""}))},error:w.breed,placeholder:"请输入宠物品种，如：拉布拉多、英短蓝猫、虎皮鹦鹉等"}),(0,t.jsx)(c.g,{label:"宝贝描述",placeholder:"请简洁扼要的描述您宝贝的不凡",value:y.description,onChange:e=>U("description",e.target.value),error:w.description,rows:4,maxLength:500,required:!0}),(0,t.jsx)(C,{value:y.location||"",onChange:e=>U("location",e),error:w.location,userId:null==d?void 0:d._id}),(0,t.jsx)(M,{value:y.type||"",onChange:e=>U("type",e),gender:y.gender||"",onGenderChange:e=>U("gender",e),error:w.gender}),(0,t.jsx)(j,{value:y.contact_info||{},onChange:e=>{u(r=>({...r,contact_info:e})),w.contact&&A(e=>({...e,contact:""}))},petType:y.type,error:w.contact}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[(0,t.jsxs)("h4",{className:"font-medium text-red-900 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"text-red-500 mr-2",children:"⚠️"}),"法律提醒"]}),(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"请遵守国家法律，禁止出售受保护的野生动物"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"发布须知"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• 请确保发布信息真实有效，如实描述物品状况"}),(0,t.jsx)("li",{children:"• 禁止发布血腥、暴力、违法违规内容"}),(0,t.jsx)("li",{children:"• 交易时请注意安全，建议当面验货交易"}),(0,t.jsx)("li",{children:"• 平台仅提供信息展示服务，不参与具体交易过程"}),(0,t.jsx)("li",{children:"• 用户需自行承担交易风险，请谨慎甄别"})]})]}),(0,t.jsx)("div",{className:"pt-4 space-y-3",children:(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,t.jsx)(l.Z,{onClick:()=>{e.back()},variant:"outline",disabled:m,className:"w-full",children:"取消发布"}),(0,t.jsx)(l.Z,{onClick:$,variant:"outline",disabled:m,className:"w-full",children:"保存到待发布"}),(0,t.jsx)(l.Z,{onClick:J,loading:m,disabled:m,className:"w-full",children:m?"发布中...":"发布宝贝"})]})})]})]}),(0,t.jsx)(V,{isOpen:G,onComplete:K,postTitle:y.breed||"您的宠物"})]})})}},88941:function(e,r,a){"use strict";a.d(r,{AuthProvider:function(){return c},E:function(){return l},Y:function(){return d}});var t=a(57437),s=a(2265),o=a(98734),n=a(31215);let i=(0,s.createContext)(void 0),c=e=>{let{children:r}=e,a=(0,o.a)();return a.isLoading&&!a.user?(0,t.jsx)(n.SX,{text:"正在初始化..."}):(0,t.jsx)(i.Provider,{value:a,children:r})},l=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e},d=e=>{let{children:r,fallback:a}=e,{isLoggedIn:s,isLoading:o}=l();return o?(0,t.jsx)(n.SX,{text:"验证登录状态..."}):s?(0,t.jsx)(t.Fragment,{children:r}):a||(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"需要登录"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"请先登录后再访问此页面"})]})})}},56334:function(e,r,a){"use strict";var t=a(57437),s=a(2265),o=a(68661);let n=s.forwardRef((e,r)=>{let{className:a,variant:s="primary",size:n="md",loading:i=!1,icon:c,children:l,disabled:d,...m}=e;return(0,t.jsxs)("button",{className:(0,o.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],a),ref:r,disabled:d||i,...m,children:[i&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!i&&c&&(0,t.jsx)("span",{className:"mr-2",children:c}),l]})});n.displayName="Button",r.Z=n},92827:function(e,r,a){"use strict";a.d(r,{I:function(){return n},g:function(){return i}});var t=a(57437),s=a(2265),o=a(68661);let n=s.forwardRef((e,r)=>{let{className:a,type:s="text",label:n,error:i,helperText:c,leftIcon:l,rightIcon:d,variant:m="default",...g}=e,y=i?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,t.jsxs)("div",{className:"w-full",children:[n&&(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:n}),(0,t.jsxs)("div",{className:"relative",children:[l&&(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("span",{className:"text-gray-400",children:l})}),(0,t.jsx)("input",{type:s,className:(0,o.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[m],y,l&&"pl-10",d&&"pr-10",a),ref:r,...g}),d&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)("span",{className:"text-gray-400",children:d})})]}),(i||c)&&(0,t.jsx)("p",{className:(0,o.cn)("mt-1 text-xs",i?"text-red-600":"text-gray-500"),children:i||c})]})});n.displayName="Input";let i=s.forwardRef((e,r)=>{let{className:a,label:s,error:n,helperText:i,variant:c="default",...l}=e,d=n?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,t.jsxs)("div",{className:"w-full",children:[s&&(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,t.jsx)("textarea",{className:(0,o.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed resize-none",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[c],d,a),ref:r,...l}),(n||i)&&(0,t.jsx)("p",{className:(0,o.cn)("mt-1 text-xs",n?"text-red-600":"text-gray-500"),children:n||i})]})});i.displayName="Textarea"},31215:function(e,r,a){"use strict";a.d(r,{LL:function(){return c},SX:function(){return n},gG:function(){return i},gb:function(){return o}});var t=a(57437);a(2265);var s=a(68661);let o=e=>{let{size:r="md",variant:a="spinner",className:o,text:n}=e,i={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},c={sm:"text-sm",md:"text-base",lg:"text-lg"};if("spinner"===a)return(0,t.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",o),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsxs)("svg",{className:(0,s.cn)("animate-spin text-primary-600",i[r]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n&&(0,t.jsx)("p",{className:(0,s.cn)("text-gray-500",c[r]),children:n})]})});if("dots"===a){let e="sm"===r?"w-2 h-2":"md"===r?"w-3 h-3":"w-4 h-4";return(0,t.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",o),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:(0,s.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"0ms"}}),(0,t.jsx)("div",{className:(0,s.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"150ms"}}),(0,t.jsx)("div",{className:(0,s.cn)("bg-primary-600 rounded-full animate-bounce",e),style:{animationDelay:"300ms"}})]}),n&&(0,t.jsx)("p",{className:(0,s.cn)("text-gray-500",c[r]),children:n})]})})}return"pulse"===a?(0,t.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",o),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)("div",{className:(0,s.cn)("bg-primary-600 rounded-full animate-pulse",i[r])}),n&&(0,t.jsx)("p",{className:(0,s.cn)("text-gray-500",c[r]),children:n})]})}):null},n=e=>{let{text:r="加载中..."}=e;return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsx)(o,{size:"lg",text:r})})},i=()=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse",children:[(0,t.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,t.jsxs)("div",{className:"p-3 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})]}),c=()=>(0,t.jsx)("div",{className:"animate-pulse",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4 p-6",children:[(0,t.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48"}),(0,t.jsxs)("div",{className:"flex space-x-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 mb-1"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-8"})]})]}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-24"})]})})},60719:function(e,r,a){"use strict";a.d(r,{O:function(){return s}});var t=a(2265);function s(e){let r=(0,t.useRef)(null);return(0,t.useEffect)(()=>{let a=a=>{r.current&&!r.current.contains(a.target)&&e()};return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[e]),r}},80240:function(e,r,a){"use strict";a.d(r,{Bm:function(){return s}});class t{static getInstance(){return t.instance||(t.instance=new t),t.instance}initializeObservers(){try{this.observeNavigation(),this.observePaint(),this.observeLayoutShift(),this.observeFirstInputDelay(),this.observeResources()}catch(e){console.warn("性能监控初始化失败:",e)}}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let r=e.getEntries(),a=r[r.length-1];this.metrics.largestContentfulPaint=a.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,r=new PerformanceObserver(r=>{r.getEntries().forEach(r=>{r.hadRecentInput||(e+=r.value,this.metrics.cumulativeLayoutShift=e)})});r.observe({entryTypes:["layout-shift"]}),this.observers.push(r)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let r=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(r)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,r){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(r)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((r,a)=>{let t=r.reduce((e,r)=>e+r,0)/r.length;e[a]={avgResponseTime:Math.round(t),callCount:r.length,maxResponseTime:Math.round(Math.max(...r)),minResponseTime:Math.round(Math.min(...r))}});let r=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,r)=>e+r,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(r)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,r=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,a=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+r+a)/3),breakdown:{loading:e,interactivity:r,visualStability:a}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}}let s=t.getInstance()}},function(e){e.O(0,[649,19,347,554,222,11,734,971,117,744],function(){return e(e.s=98802)}),_N_E=e.O()}]);