"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[696],{89841:function(e,l,s){s.d(l,{Z:function(){return m}});var t=s(57437),a=s(2265),r=s(27648),i=s(32489),c=s(92369),n=s(83774),o=s(88997),d=s(68661),x=s(28819),u=e=>{let{src:l,alt:s,className:r,placeholder:i,fallback:c="/images/placeholder.jpg",onLoad:n,onError:o,priority:u=!1,quality:m=75,sizes:h}=e,[g,f]=(0,a.useState)("loading"),[b,p]=(0,a.useState)(i||""),v=(0,a.useRef)(null),{ref:j,inView:y}=(0,x.YD)({threshold:0,rootMargin:"200px",triggerOnce:!0,skip:u}),w=function(e){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:75;if(!e)return e;if(e.includes(".myqcloud.com")||e.includes(".cos.")){let s=e.includes("?")?"&":"?";return"".concat(e).concat(s,"imageMogr2/quality/").concat(l,"/format/webp")}return e.includes(".tcloudbaseapp.com"),e},N=e=>new Promise((l,s)=>{let t=new Image;t.onload=()=>{f("loaded"),p(e),null==n||n(),l()},t.onerror=()=>{f("error"),p(c),null==o||o(),s(Error("Image load failed"))},t.src=e});(0,a.useEffect)(()=>{if(u||y){let e=w(l,m),s=performance.now();N(e).then(()=>{let e=performance.now()-s;console.log("图片加载完成: ".concat(l,", 耗时: ").concat(e.toFixed(2),"ms"))}).catch(()=>{e!==l?(console.warn("优化图片加载失败，尝试原图:",l),N(l).catch(()=>{console.error("原图加载也失败:",l),f("error"),p(c)})):(f("error"),p(c))})}},[y,u,l,m,c,n,o]);let k=(0,a.useCallback)(()=>{if(!u&&!y&&"loading"===g){let e=w(l,m);console.log("鼠标悬停预加载:",l),N(e).catch(()=>{})}},[u,y,g,l,m]);return(0,t.jsxs)("div",{ref:j,className:"relative overflow-hidden w-full h-full",onMouseEnter:k,children:["loading"===g&&!b&&(0,t.jsx)("div",{className:(0,d.cn)("bg-gray-200 animate-pulse flex items-center justify-center w-full h-full",r),children:(0,t.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})})}),"error"===g&&(0,t.jsx)("div",{className:(0,d.cn)("bg-gray-100 flex items-center justify-center text-gray-400 w-full h-full",r),children:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),("loaded"===g||b)&&(0,t.jsx)("img",{ref:v,src:b,alt:s,className:(0,d.cn)("transition-opacity duration-300","loaded"===g?"opacity-100":"opacity-0",r),sizes:h,loading:u?"eager":"lazy",decoding:"async"}),"loading"===g&&b&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]})},m=e=>{var l,s,a;let{post:x,className:m,isDraft:h=!1,showRemoveFromFavorites:g=!1,onRemoveFromFavorites:f}=e,b=(null===(l=x.images)||void 0===l?void 0:l[0])||x.image||"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",p=(e=>{switch(e){case"selling":case"sell":return{label:"出售",color:"bg-green-500"};case"adoption":case"adopt":return{label:"领养",color:"bg-blue-500"};case"breeding":case"mate":return{label:"配种",color:"bg-orange-500"};case"lost":return{label:"寻宠",color:"bg-red-500"};case"found":return{label:"招领",color:"bg-yellow-500"};case"wanted":return{label:"求购",color:"bg-purple-500"};case"sharing":return{label:"分享",color:"bg-indigo-500"};default:return null}})(x.type),v=(0,t.jsxs)("div",{className:(0,d.cn)("bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 h-full flex flex-col",!h&&"cursor-pointer",m),children:[(0,t.jsxs)("div",{className:"relative w-full overflow-hidden",style:{height:"200px"},children:[(0,t.jsx)(u,{src:b,alt:x.breed||"宠物图片",className:"w-full h-full object-cover rounded-t-lg",fallback:"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",quality:80,sizes:"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"}),h&&(0,t.jsx)("div",{className:"absolute top-2 left-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium",children:"待发布"}),!h&&p&&(0,t.jsx)("div",{className:(0,d.cn)("absolute top-2 left-2 text-white text-xs px-2 py-1 rounded-full font-medium",p.color),children:p.label}),x.images&&x.images.length>1&&(0,t.jsxs)("div",{className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full",children:["1/",x.images.length]}),g&&(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),null==f||f()},className:"absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1.5 rounded-full transition-colors",title:"从收藏中移除",children:(0,t.jsx)(i.Z,{className:"h-3 w-3"})})]}),(0,t.jsx)("div",{className:"p-3 flex-1 flex flex-col justify-center",style:{minHeight:"72px"},children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-full",children:[(0,t.jsxs)("div",{className:"flex flex-col justify-center flex-1 min-w-0 pr-2",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 text-sm leading-tight truncate mb-1",children:x.breed||"未知品种"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-600",children:[(0,t.jsx)(c.Z,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"truncate",children:(null===(s=x.user)||void 0===s?void 0:s.nickname)||(null===(a=x.author)||void 0===a?void 0:a.nickname)||"匿名用户"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col justify-center items-end flex-shrink-0",children:[(x.location||x.city)&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500 mb-1",children:[(0,t.jsx)(n.Z,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"truncate max-w-20",title:x.location||"#".concat(x.province).concat(x.city),children:x.city||(e=>{if(!e)return"未知地区";let l=e.split(/[省市区县]/);return l[l.length-2]||e})(x.location)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[(0,t.jsx)(o.Z,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:x.likes||0})]})]})]})})]});return h?v:(0,t.jsx)(r.default,{href:"/post/detail?id=".concat(x._id),children:v})}},26467:function(e,l,s){s.d(l,{Z:function(){return h}});var t=s(57437),a=s(2265),r=s(99376),i=s(32489),c=s(88997),n=s(88906),o=s(92369),d=s(43745),x=s(56334),u=s(9356),m=s(98011);function h(e){let{isOpen:l,onClose:s,userId:h}=e,g=(0,r.useRouter)(),[f,b]=(0,a.useState)("following"),[p,v]=(0,a.useState)([]),[j,y]=(0,a.useState)([]),[w,N]=(0,a.useState)(!1);(0,a.useEffect)(()=>{l&&("following"===f?C():z())},[l,h,f]);let k=e=>{try{g.push("/profile/".concat(e)),s()}catch(e){console.error("导航到用户资料页面失败:",e),u.C.error("无法打开用户资料页面")}},C=async()=>{N(!0);try{let e=await m.petAPI.getUserFollowing({targetUserId:h,limit:100});if(e.success){let l=e.data.map(e=>({_id:e.following_id,nickname:e.following_nickname||"用户已删除",avatar_url:e.following_avatar_url||"/default-avatar.png",bio:e.following_bio||"",isFollowing:!0}));v(l)}else u.C.error(e.message||"加载关注列表失败")}catch(e){u.C.error("加载关注列表失败")}finally{N(!1)}},_=async e=>{try{let l=await m.petAPI.toggleFollow({targetUserId:e});l.success?(v(l=>l.map(l=>l._id===e?{...l,isFollowing:!1}:l)),u.C.success("已取消关注")):u.C.error(l.message||"取消关注失败")}catch(e){u.C.error("取消关注失败")}},Z=async e=>{try{let l=await m.petAPI.toggleFollow({targetUserId:e});l.success?(v(l=>l.map(l=>l._id===e?{...l,isFollowing:!0}:l)),u.C.success("关注成功")):u.C.error(l.message||"关注失败")}catch(e){u.C.error("关注失败")}},z=async()=>{N(!0);try{let e=await m.petAPI.getBlockedUsers();e.success?y(e.data||[]):u.C.error(e.message||"加载黑名单失败")}catch(e){u.C.error("加载黑名单失败")}finally{N(!1)}},F=async e=>{try{let l=await m.petAPI.unblockUser({targetUserId:e});l.success?(y(l=>l.filter(l=>l._id!==e)),u.C.success("已取消拉黑")):u.C.error(l.message||"取消拉黑失败")}catch(e){u.C.error("取消拉黑失败")}};return l?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:e=>{e.target===e.currentTarget&&s()},children:(0,t.jsxs)("div",{className:"bg-white rounded-lg w-[480px] h-[500px] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"following"===f?"我的关注":"黑名单"}),(0,t.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(i.Z,{className:"w-6 h-6"})})]}),(0,t.jsx)("div",{className:"border-b border-gray-200 flex-shrink-0",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("button",{onClick:()=>b("following"),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat("following"===f?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,t.jsx)(c.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"我的关注"})]}),(0,t.jsxs)("button",{onClick:()=>b("blocked"),className:"flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ".concat("blocked"===f?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[(0,t.jsx)(n.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"黑名单"})]})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:w?(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):"following"===f?0===p.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:(0,t.jsx)(c.Z,{className:"w-16 h-16 mx-auto"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有关注任何人"}),(0,t.jsx)("p",{className:"text-gray-600",children:"去发现更多有趣的用户吧！"})]}):(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:p.map(e=>(0,t.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.avatar_url?(0,t.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(o.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate transition-colors",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.nickname}),e.bio&&(0,t.jsx)("p",{className:"text-sm text-gray-500 truncate",children:e.bio})]}),(0,t.jsx)("div",{className:"flex-shrink-0",children:e.isFollowing?(0,t.jsx)(x.Z,{variant:"outline",size:"sm",onClick:()=>_(e._id),children:"取消关注"}):(0,t.jsx)(x.Z,{size:"sm",onClick:()=>Z(e._id),children:"关注"})})]})},e._id))}):0===j.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:(0,t.jsx)(n.Z,{className:"w-16 h-16 mx-auto"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"黑名单为空"}),(0,t.jsx)("p",{className:"text-gray-600",children:"您还没有拉黑任何用户"})]}):(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:j.map(e=>(0,t.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.avatar_url?(0,t.jsx)("img",{src:e.avatar_url,alt:e.nickname,className:"w-full h-full object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(d.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate transition-colors",onClick:()=>k(e._id),title:"点击查看用户资料",children:e.nickname}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 truncate",children:["拉黑时间：",new Date(e.blockedAt).toLocaleDateString()]})]}),(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(x.Z,{variant:"outline",size:"sm",onClick:()=>F(e._id),className:"text-red-600 border-red-200 hover:bg-red-50",children:"取消拉黑"})})]})},e._id))})})]})}):null}}}]);