(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{49289:function(e,t,s){Promise.resolve().then(s.bind(s,7226))},7226:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return F}});var a=s(57437),r=s(99376),n=s(2265),l=s(98734),i=s(32489),c=s(94630),o=s(32660),d=s(92369),m=s(53581),u=s(78867),x=s(34423),h=s(98728),g=s(82718),f=s(88997),p=s(10407),y=s(41473),j=s(91723),b=s(86595),v=s(42208),w=s(56334),N=s(9356),_=s(98011);let C=e=>e.startsWith("cloud://")||!e.startsWith("http")&&!e.startsWith("/");var k=e=>{let{fileId:t,alt:s="",className:r="",width:l,height:i,style:c,onClick:o}=e,[d,m]=(0,n.useState)(""),[u,x]=(0,n.useState)(!0),[h,g]=(0,n.useState)(!1),[f,p]=(0,n.useState)(0);return((0,n.useEffect)(()=>{(async()=>{if(!t){g(!0),x(!1);return}if(t.startsWith("http")){m(t),x(!1);return}try{x(!0),g(!1),p(0);let e=await (0,_.getImageUrl)(t);m(e)}catch(e){console.error("加载图片失败:",e),g(!0)}finally{x(!1)}})()},[t]),u)?(0,a.jsx)("div",{className:"bg-gray-200 animate-pulse flex items-center justify-center ".concat(r),style:{width:l,height:i,...c},children:(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:"加载中..."})}):h||!d?(0,a.jsx)("div",{className:"bg-gray-200 flex items-center justify-center ".concat(r),style:{width:l,height:i,...c},children:(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:"图片加载失败"})}):(0,a.jsx)("img",{src:d,alt:s,className:r,width:l,height:i,style:c,onClick:o,onError:async()=>{if(console.log("图片加载失败，尝试刷新URL，重试次数:",f),f<2){if(p(e=>e+1),C(t))try{var e;let s=await _.petAPI.getImage({fileId:t});s.success&&(null===(e=s.data)||void 0===e?void 0:e.url)?(console.log("获取新的图片URL:",s.data.url),m(s.data.url)):(console.error("刷新图片URL失败:",s.message),g(!0))}catch(e){console.error("刷新图片URL异常:",e),g(!0)}else console.log("普通URL加载失败，无法刷新"),g(!0)}else g(!0)}})},S=s(27648),I=s(95578);function Z(e){let{isOpen:t,onClose:s,currentBio:r,onSave:l}=e,[c,o]=(0,n.useState)(""),[d,m]=(0,n.useState)(!1);(0,n.useEffect)(()=>{t&&o(r||"")},[t,r]);let u=async()=>{if(c.length>200){N.C.error("个人简介不能超过200字");return}m(!0);try{await l(c),N.C.success("个人简介更新成功"),s()}catch(e){N.C.error("更新失败，请重试")}finally{m(!1)}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"编辑个人简介"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(i.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),(0,a.jsx)("textarea",{value:c,onChange:e=>o(e.target.value),placeholder:"介绍一下自己吧...",rows:4,maxLength:200,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}),(0,a.jsx)("div",{className:"flex justify-between items-center mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[c.length,"/200"]})})]})}),(0,a.jsxs)("div",{className:"flex space-x-3 p-6 border-t",children:[(0,a.jsx)(w.Z,{variant:"outline",onClick:s,className:"flex-1",children:"取消"}),(0,a.jsx)(w.Z,{onClick:u,loading:d,className:"flex-1",children:"保存"})]})]})}):null}var O=s(26467),E=s(83565),D=s(59604),P=s(89841),L=s(18930),R=s(63639),U=e=>{let{isOpen:t,onClose:s,onConfirm:r,title:l,message:c,confirmText:o="确定",cancelText:d="取消",type:m="danger",loading:u=!1}=e;return((0,n.useEffect)(()=>{let e=e=>{t&&("Escape"===e.key?s():"Enter"!==e.key||u||r())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,s,r,u]),(0,n.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),t)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",role:"dialog","aria-modal":"true","aria-labelledby":"confirm-modal-title","aria-describedby":"confirm-modal-description",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:s,"aria-hidden":"true"}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:[(0,a.jsx)("button",{onClick:s,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors","aria-label":"关闭对话框",children:(0,a.jsx)(i.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(()=>{switch(m){case"danger":return(0,a.jsx)(L.Z,{className:"w-6 h-6 text-red-600"});case"warning":return(0,a.jsx)(R.Z,{className:"w-6 h-6 text-yellow-600"});default:return(0,a.jsx)(R.Z,{className:"w-6 h-6 text-blue-600"})}})(),(0,a.jsx)("h3",{id:"confirm-modal-title",className:"text-lg font-medium text-gray-900",children:l||"确认操作"})]}),(0,a.jsx)("p",{id:"confirm-modal-description",className:"text-gray-600 mb-6 leading-relaxed",children:c}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-end",children:[(0,a.jsx)(w.Z,{variant:"outline",onClick:s,disabled:u,className:"px-4 py-2",children:d}),(0,a.jsx)(w.Z,{variant:(()=>{switch(m){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:r,loading:u,disabled:u,className:"px-4 py-2",children:o})]})]})]})]}):null},A=e=>{let{isOpen:t,onClose:s,items:r,position:l,className:i=""}=e,c=(0,n.useRef)(null);if((0,n.useEffect)(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&s()};return t&&(document.addEventListener("mousedown",e),document.addEventListener("touchstart",e)),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("touchstart",e)}},[t,s]),(0,n.useEffect)(()=>{let e=e=>{t&&"Escape"===e.key&&s()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,s]),(0,n.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),!t)return null;let o=e=>{e.onClick(),s()};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-30",onClick:s,"aria-hidden":"true"}),(0,a.jsx)("div",{ref:c,className:"\n          fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200\n          min-w-48 py-3 mx-4 transform transition-all duration-200 ease-out\n          ".concat(l?"":"bottom-6 left-0 right-0 md:bottom-auto md:left-auto md:right-auto md:mx-0","\n          ").concat(t?"scale-100 opacity-100":"scale-95 opacity-0","\n          ").concat(i,"\n        "),style:l?{left:l.x,top:l.y,transform:"translate(-50%, -100%)"}:void 0,role:"menu","aria-orientation":"vertical",children:r.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>o(e),className:"\n              w-full px-5 py-4 text-left flex items-center space-x-3\n              transition-all duration-150 font-medium text-base\n              ".concat("danger"===e.variant?"text-red-600 hover:bg-red-50 active:bg-red-100 hover:scale-[1.02]":"text-gray-700 hover:bg-gray-50 active:bg-gray-100 hover:scale-[1.02]","\n              ").concat(0===t?"rounded-t-xl":"","\n              ").concat(t===r.length-1?"rounded-b-xl":"","\n              ").concat(t>0?"border-t border-gray-100":"","\n            "),role:"menuitem",tabIndex:0,children:[e.icon&&(0,a.jsx)("span",{className:"flex-shrink-0 w-5 h-5 flex items-center justify-center",children:e.icon}),(0,a.jsx)("span",{className:"flex-1",children:e.label})]},e.id))})]})};let z=e=>{let{onLongPress:t,onPress:s,delay:a=500,shouldPreventDefault:r=!0}=e,[l,i]=(0,n.useState)(!1),c=(0,n.useRef)(),o=(0,n.useRef)(),d=(0,n.useCallback)(e=>{r&&e.target&&(e.target.addEventListener("touchend",u,{passive:!1}),o.current=e.target),i(!1),c.current=setTimeout(()=>{i(!0),navigator.vibrate&&navigator.vibrate([50,30,50]),t()},a)},[t,a,r]),m=(0,n.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];c.current&&clearTimeout(c.current),t&&!l&&s&&s(),i(!1),r&&o.current&&o.current.removeEventListener("touchend",u)},[r,l,s]),u=e=>{e.touches&&e.touches.length<2&&e.preventDefault&&e.preventDefault()};return{onMouseDown:e=>d(e),onMouseUp:e=>m(e),onMouseLeave:e=>m(e,!1),onTouchStart:e=>d(e),onTouchEnd:e=>m(e),onTouchMove:e=>{e.touches[0]&&m(e,!1)},isLongPressing:l}};var T=e=>{let{post:t,onLongPress:s,onDelete:r,onEdit:n,type:l,isDraft:c=!1,onClick:o}=e,d=z({onLongPress:s,onPress:o,delay:500});return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{...d,className:"relative transition-all duration-200 ".concat(d.isLongPressing?"scale-95 opacity-80 ring-2 ring-blue-500 ring-opacity-50":""," ").concat(o?"cursor-pointer":""),children:[(0,a.jsx)(P.Z,{post:t,isDraft:c}),d.isLongPressing&&(0,a.jsx)("div",{className:"absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center md:hidden",children:(0,a.jsx)("div",{className:"bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm font-medium text-blue-600",children:"松开显示选项"})})]}),(0,a.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),r()},className:"absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors shadow-lg z-10 md:flex hidden",title:(()=>{switch(l){case"published":return"下架宝贝并移至待发布";case"draft":return"删除草稿";case"history":return"从浏览历史中移除";default:return"删除"}})(),children:(0,a.jsx)(i.Z,{className:"w-3 h-3"})})]})};function F(){let e=(0,r.useRouter)(),t=(0,r.useSearchParams)().get("id"),{user:C,isLoggedIn:L,isLoading:R}=(0,l.a)(),[z,F]=(0,n.useState)(null),[M,W]=(0,n.useState)(!0),[J,B]=(0,n.useState)(!1),[K,X]=(0,n.useState)("posts"),[H,q]=(0,n.useState)(!1),[G,Q]=(0,n.useState)(!1),[V,Y]=(0,n.useState)(!1),[$,ee]=(0,n.useState)([]),[et,es]=(0,n.useState)([]),[ea,er]=(0,n.useState)([]),[en,el]=(0,n.useState)(!1),[ei,ec]=(0,n.useState)(!1),[eo,ed]=(0,n.useState)(!1),[em,eu]=(0,n.useState)(""),[ex,eh]=(0,n.useState)([]),[eg,ef]=(0,n.useState)({isOpen:!1,message:"",onConfirm:()=>{}}),[ep,ey]=(0,n.useState)({isOpen:!1,postId:"",postType:"published"});(0,n.useEffect)(()=>{"drafts"===K&&ee((0,E.Oe)())},[K]),(0,n.useEffect)(()=>{"history"===K&&J&&eh(D.KX.getHistory())},[K,J]);let ej=t=>{try{if(!t){N.C.error("草稿ID无效");return}console.log("准备跳转到发布页面，草稿ID:",t),e.push("/upload?draftId=".concat(encodeURIComponent(t)))}catch(e){console.error("跳转到发布页面失败:",e),N.C.error("跳转失败，请重试")}},eb=async()=>{try{el(!0);try{let e=await _.petAPI.getUserRatedPosts({limit:20});if(e&&e.success&&e.data){es(e.data);return}}catch(e){console.warn("getUserRatedPosts API不可用，使用替代方案")}let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");if(0===e.length){es([]);return}let t=e.filter(e=>!e.startsWith("archived_"));if(0===t.length){es([]);return}let s=[];for(let e of t.slice(0,20))try{let t=await _.petAPI.getPostDetail({postId:e});t.success&&t.data&&s.push(t.data)}catch(t){console.warn("获取帖子 ".concat(e," 详情失败:"),t)}es(s)}catch(e){console.error("加载评分记录失败:",e),N.C.error("加载评分记录失败")}finally{el(!1)}},ev=async()=>{if(!L)return;let e=t||(null==C?void 0:C._id);if(e)try{ec(!0);let t=await _.petAPI.getUserPosts({limit:20,targetUserId:e});t.success&&er(t.data||[])}catch(e){console.error("加载发布失败:",e),N.C.error("加载发布失败")}finally{ec(!1)}};(0,n.useEffect)(()=>{"wants"===K&&J?eb():"posts"===K&&ev()},[K,J,L]);let ew=async e=>{ef({isOpen:!0,title:"确认下架",message:"下架这个宝贝并移动到待发布吗？",onConfirm:()=>eN(e)})},eN=async e=>{ef(e=>({...e,loading:!0}));try{N.C.loading("正在下架宝贝...");let t=await _.petAPI.getPostDetail({postId:e});if(!t.success){N.C.dismiss(),N.C.error("获取帖子信息失败");return}let s=t.data,a=await _.petAPI.deletePost({postId:e});if(!a||!a.success){N.C.dismiss(),N.C.error("下架失败："+((null==a?void 0:a.message)||"服务器删除失败"));return}console.log("服务器删除成功，开始保存草稿");let r=[];if(s.images&&Array.isArray(s.images))for(let e of s.images)try{if("string"==typeof e&&e.startsWith("http")){let t=await fetch(e),s=await t.blob(),a=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(s)});r.push(a),console.log("成功转换图片URL为base64:",e.substring(0,50)+"...")}else r.push(e)}catch(t){console.error("转换图片失败:",e,t),r.push(e)}let n={id:"archived_".concat(e,"_").concat(Date.now()),title:s.breed||"",breed:s.breed||"",description:s.description||"",images:r,category:s.category||"",location:s.location||"",contact_info:s.contact_info||{},type:s.type||"selling",price:s.price||"",age:s.age||"",gender:s.gender||"",vaccination:s.vaccination||!1,health_certificate:s.health_certificate||!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),isArchived:!0,originalPostId:e},l=(0,E.Oe)(),i=[n,...l];localStorage.setItem("petDrafts",JSON.stringify(i)),console.log("草稿保存成功，包含",r.length,"张图片"),er(t=>t.filter(t=>t._id!==e)),"drafts"===K&&ee(i),N.C.dismiss(),N.C.success("宝贝已下架并移至待发布")}catch(e){console.error("下架宝贝失败:",e),N.C.dismiss(),N.C.error(e.message||"下架失败，请重试")}finally{ef(e=>({...e,isOpen:!1,loading:!1}))}},e_=async e=>{try{let t=(0,E.Oe)().find(t=>t.id===e);if(!t){N.C.error("草稿不存在");return}let s=t.isArchived?"确定要删除这个归档的宝贝吗？删除后无法恢复。":"确定要删除这个草稿吗？";ef({isOpen:!0,title:"确认删除",message:s,onConfirm:()=>eC(e)})}catch(e){console.error("删除草稿失败:",e),N.C.error("删除失败，请重试")}},eC=async e=>{try{let t=(0,E.Oe)(),s=t.find(t=>t.id===e),a=t.filter(t=>t.id!==e);localStorage.setItem("petDrafts",JSON.stringify(a)),ee(a);let r=(null==s?void 0:s.isArchived)?"归档宝贝已删除":"草稿已删除";N.C.success(r),ef(e=>({...e,isOpen:!1}))}catch(e){console.error("删除草稿失败:",e),N.C.error("删除失败，请重试"),ef(e=>({...e,isOpen:!1}))}},ek=e=>{try{D.KX.removeRecord(e),eh(t=>t.filter(t=>t.postId!==e)),N.C.success("已从浏览历史中移除")}catch(e){console.error("删除历史记录失败:",e),N.C.error("操作失败，请重试")}},eS=(e,t)=>{ey({isOpen:!0,postId:e,postType:t})};(0,n.useEffect)(()=>{let e=t||(null==C?void 0:C._id);if(!e){R||L||N.C.error("请先登录"),W(!1);return}if(C&&C._id===e){var s;B(!0),F({_id:C._id,nickname:C.nickname,email:C.email,avatar_url:C.avatar_url,location:"未设置",bio:C.bio||"这个人很懒，什么都没有留下...",created_at:(null===(s=C.created_at)||void 0===s?void 0:s.toString())||new Date().toISOString(),posts_count:C.posts_count||0,likes_count:C.total_likes||0,followers_count:C.followers_count||0,following_count:C.following_count||0,rating:5,rating_count:0}),W(!1)}else eI(e)},[t,C,R,L]),(0,n.useEffect)(()=>{z&&L&&!ei&&ev()},[z,L]);let eI=async e=>{try{W(!0);let t=await _.petAPI.getUserInfo({userId:e});if(t.success&&t.data)F({...t.data,posts_count:t.data.posts_count||0,likes_count:t.data.likes_count||0,followers_count:t.data.followers_count||0,following_count:t.data.following_count||0,rating:0,rating_count:0});else{let t={_id:e,nickname:"用户".concat(e.slice(-6)),avatar_url:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",location:"北京市朝阳区",bio:"热爱宠物，专业繁殖者",created_at:"2024-01-01T00:00:00Z",posts_count:12,likes_count:89,followers_count:156,following_count:89,rating:4.8,rating_count:23};F(t)}}catch(e){console.error("加载用户资料失败:",e),N.C.error("加载用户资料失败")}finally{W(!1)}},eZ=async()=>{if(z)try{await navigator.clipboard.writeText(z._id),N.C.success("用户ID已复制到剪贴板")}catch(t){let e=document.createElement("textarea");e.value=z._id,document.body.appendChild(e),e.select();try{document.execCommand("copy"),N.C.success("用户ID已复制到剪贴板")}catch(e){N.C.error("复制失败，请手动复制")}document.body.removeChild(e)}},eO=async e=>{try{let t=await _.petAPI.updateProfile(e);if(t.success)N.C.success("资料更新成功"),z&&F({...z,nickname:e.nickname||z.nickname,bio:e.bio||z.bio,contact_info:e.contactInfo||z.contact_info,address:e.address||z.address}),e.contactInfo&&C&&localStorage.setItem("contact_".concat(C._id),JSON.stringify(e.contactInfo)),void 0!==e.address&&C&&localStorage.setItem("address_".concat(C._id),e.address);else throw Error(t.message||"更新失败")}catch(e){throw N.C.error(e.message||"更新失败，请重试"),e}},eE=async e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];if(a){if(!a.type.startsWith("image/")){eu("请选择图片文件");return}if(a.size>5242880){eu("图片大小不能超过5MB");return}ed(!0),eu("");try{let{uploadFile:e}=await Promise.resolve().then(s.bind(s,98011)),t=document.createElement("canvas"),r=t.getContext("2d"),n=new Image,l=await new Promise((e,s)=>{n.onload=()=>{let{width:l,height:i}=n;l>i?l>300&&(i=300*i/l,l=300):i>300&&(l=300*l/i,i=300),t.width=l,t.height=i,null==r||r.drawImage(n,0,0,l,i),t.toBlob(t=>{if(t){let s=new File([t],a.name,{type:"image/jpeg",lastModified:Date.now()});e(s)}else s(Error("压缩失败"))},"image/jpeg",.9)},n.onerror=()=>s(Error("图片加载失败")),n.src=URL.createObjectURL(a)});console.log("头像压缩: ".concat(a.size," -> ").concat(l.size," (").concat(Math.round((1-l.size/a.size)*100),"% 减少)"));let{petAPI:i}=await Promise.resolve().then(s.bind(s,98011)),c=await new Promise((e,t)=>{let s=new FileReader;s.onload=()=>{let t=s.result.split(",")[1];e(t)},s.onerror=t,s.readAsDataURL(l)}),o=Date.now(),d=Math.random().toString(36).substring(2,8),m="avatar_".concat(o,"_").concat(d,".jpg"),u=await i.uploadToStatic({fileName:m,fileData:c,contentType:"image/jpeg"});if(!u.success)throw Error(u.message||"头像上传失败");let x=u.data.url,h=await i.updateAvatar({avatarUrl:x});if(h.success){if(F(e=>e?{...e,avatar_url:x}:null),C)try{let e={...C,avatar_url:x};localStorage.setItem("pet_platform_user",JSON.stringify(e))}catch(e){console.error("保存头像到本地存储失败:",e)}N.C.success("您的头像已经更改完成，三十天内只能修改一次头像")}else console.error("头像更新失败:",h),eu(h.message||"头像更新失败")}catch(e){console.error("头像上传失败:",e),eu("头像上传失败，请重试: "+(e instanceof Error?e.message:String(e)))}finally{ed(!1)}}},eD=async e=>{try{z&&F({...z,bio:e||"这个人很懒，什么都没有留下..."})}catch(e){throw e}},eP=t=>{e.push("/upload?draftId=".concat(t))};return M?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"加载中..."})]})}):z?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsx)("button",{onClick:()=>e.push("/"),className:"mb-6 p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(o.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-24 h-24 md:w-32 md:h-32 rounded-full bg-gray-200 overflow-hidden",children:[z.avatar_url?(0,a.jsx)(k,{fileId:z.avatar_url,alt:z.nickname,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(d.Z,{className:"w-12 h-12 md:w-16 md:h-16 text-gray-400"})}),eo&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-full",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})})]}),J&&(0,a.jsxs)("div",{className:"absolute bottom-0 right-0",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:eE,className:"hidden",id:"avatar-upload",disabled:eo}),(0,a.jsx)("label",{htmlFor:"avatar-upload",className:"bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors cursor-pointer block ".concat(eo?"opacity-50 cursor-not-allowed":""),children:(0,a.jsx)(m.Z,{className:"w-4 h-4"})})]}),em&&(0,a.jsx)("div",{className:"absolute top-full left-0 mt-2 text-red-500 text-sm",children:em})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:z.nickname}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2 text-gray-600",children:[(0,a.jsx)(d.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{className:"text-sm",children:["ID: ",z._id]}),(0,a.jsx)("button",{onClick:eZ,className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"复制用户ID",children:(0,a.jsx)(u.Z,{className:"w-4 h-4 text-gray-500 hover:text-blue-600"})})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-700",children:z.bio||"这个人很懒，什么都没有留下..."}),J&&(0,a.jsx)("button",{onClick:()=>Q(!0),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})})]})]}),(0,a.jsx)("div",{className:"flex space-x-3 mt-4 md:mt-0",children:J?(0,a.jsx)(w.Z,{variant:"outline",icon:(0,a.jsx)(h.Z,{className:"w-4 h-4"}),onClick:()=>q(!0),children:"个人设置"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.Z,{icon:(0,a.jsx)(g.Z,{className:"w-4 h-4"}),children:"私信"}),(0,a.jsx)(w.Z,{variant:"outline",icon:(0,a.jsx)(f.Z,{className:"w-4 h-4"}),children:"关注"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:z.likes_count}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"获赞"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:z.followers_count}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"粉丝"})]}),(0,a.jsxs)("div",{className:"text-center cursor-pointer",onClick:()=>J&&Y(!0),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:z.following_count}),J&&(0,a.jsx)(p.Z,{className:"w-4 h-4 text-gray-400"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"关注"})]})]})]})]})]})}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[J?(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>X("posts"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("posts"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(y.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"已发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>X("drafts"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("drafts"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(j.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"待发布宝贝"})]}),(0,a.jsxs)("button",{onClick:()=>X("wants"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("wants"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(b.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"评分"})]}),(0,a.jsxs)("button",{onClick:()=>X("history"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("history"===K?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(v.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"浏览历史"})]})]})}):(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"".concat(z.nickname,"的发布")}),J&&(0,a.jsx)("div",{className:"mb-4 md:hidden",children:(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"\uD83D\uDCA1 提示：长按卡片可显示操作选项"})})}),"posts"===K&&(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(S.default,{href:"/upload",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer",children:(0,a.jsx)("div",{className:"aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.Z,{className:"w-12 h-12 text-blue-500 mx-auto mb-3"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"发布宝贝"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"分享您的宠物"})]})})})}),ei?(0,a.jsx)("div",{className:"col-span-full text-center py-8 text-gray-500",children:(0,a.jsx)("p",{children:"加载中..."})}):0===ea.length?(0,a.jsx)("div",{className:"col-span-full text-center py-8 text-gray-500",children:(0,a.jsx)("p",{children:"还没有发布任何内容"})}):ea.map(e=>(0,a.jsx)(T,{post:e,onLongPress:()=>eS(e._id,"published"),onDelete:()=>ew(e._id),type:"published"},e._id))]}),"drafts"===K&&(0,a.jsxs)(a.Fragment,{children:[$.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"待发布宝贝"}),(0,a.jsx)("button",{onClick:()=>{ef({isOpen:!0,title:"确认清空",message:"确定要清空所有待发布的宝贝吗？此操作不可恢复。",onConfirm:()=>{localStorage.removeItem("petDrafts"),ee([]),N.C.success("所有待发布宝贝已清空"),ef(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"全部清空"})]}),0===$.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(j.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有待发布的内容"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"在发布页面保存草稿后会显示在这里"}),(0,a.jsx)(S.default,{href:"/upload",children:(0,a.jsx)(w.Z,{icon:(0,a.jsx)(m.Z,{className:"w-4 h-4"}),children:"创建内容"})})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:$.map(e=>{var t;let s=(null===(t=e.images)||void 0===t?void 0:t.map(e=>"string"==typeof e?e:(console.warn("草稿中发现非字符串图片数据，使用默认图片:",e),"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center")))||[],r={_id:e.id,breed:e.breed||"未命名草稿",description:e.description||"",images:s,author:{_id:"draft",nickname:"草稿"},location:e.location||"",likes_count:0,created_at:e.updated_at||e.created_at,type:e.type||"selling",gender:e.gender};return(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(T,{post:r,onLongPress:()=>eS(e.id,"draft"),onDelete:()=>e_(e.id),onClick:()=>ej(e.id),type:"draft",isDraft:!0})},e.id)})})]}),"wants"===K&&(0,a.jsx)(a.Fragment,{children:en?(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("div",{className:"text-gray-400",children:"加载中..."})}):0===et.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(b.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有评分任何内容"}),(0,a.jsx)("p",{className:"text-gray-600",children:"给喜欢的宠物评分后会显示在这里"})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:et.map(e=>(0,a.jsx)(P.Z,{post:e},e._id))})}),"history"===K&&(0,a.jsxs)("div",{children:[ex.length>0&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["浏览记录保留3天，共",ex.length,"条记录"]}),(0,a.jsx)("button",{onClick:()=>{ef({isOpen:!0,title:"确认清空",message:"确定要清空所有浏览历史吗？",onConfirm:()=>{D.KX.clearHistory(),eh([]),N.C.success("浏览历史已清空"),ef(e=>({...e,isOpen:!1}))}})},className:"text-sm text-red-600 hover:text-red-800 transition-colors font-medium",children:"清空历史"})]}),0===ex.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(v.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有浏览历史"}),(0,a.jsx)("p",{className:"text-gray-600",children:"浏览过的内容会显示在这里，记录保留3天"})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:ex.map(e=>{let t={_id:e.postId,breed:e.title,description:"",images:e.image?[e.image]:[],author:{_id:"history",nickname:e.author||"匿名用户"},location:"",likes_count:0,created_at:new Date(e.timestamp).toISOString()};return(0,a.jsx)(T,{post:t,onLongPress:()=>eS(e.postId,"history"),onDelete:()=>ek(e.postId),type:"history"},e.postId)})})]})]})}),(0,a.jsx)(I.Z,{isOpen:H,onClose:()=>q(!1),currentUser:C,onUpdate:eO}),(0,a.jsx)(Z,{isOpen:G,onClose:()=>Q(!1),currentBio:(null==z?void 0:z.bio)||"",onSave:eD}),(0,a.jsx)(O.Z,{isOpen:V,onClose:()=>Y(!1),userId:(null==C?void 0:C._id)||""}),(0,a.jsx)(U,{isOpen:eg.isOpen,onClose:()=>ef(e=>({...e,isOpen:!1})),onConfirm:eg.onConfirm,title:eg.title,message:eg.message,loading:eg.loading}),(0,a.jsx)(A,{isOpen:ep.isOpen,onClose:()=>ey(e=>({...e,isOpen:!1})),items:(()=>{let{postId:e,postType:t}=ep;switch(t){case"published":return[{id:"delete",label:"下架宝贝",icon:(0,a.jsx)(i.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),ew(e)},variant:"danger"}];case"draft":return[{id:"edit",label:"编辑草稿",icon:(0,a.jsx)(c.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),eP(e)}},{id:"delete",label:"删除草稿",icon:(0,a.jsx)(i.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),e_(e)},variant:"danger"}];case"history":return[{id:"delete",label:"从历史中移除",icon:(0,a.jsx)(i.Z,{className:"w-4 h-4"}),onClick:()=>{ey(e=>({...e,isOpen:!1})),ek(e)},variant:"danger"}];default:return[]}})(),position:ep.position})]}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"用户不存在"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"抱歉，找不到该用户的信息"}),(0,a.jsx)(S.default,{href:"/",children:(0,a.jsx)(w.Z,{children:"返回首页"})})]})})}}},function(e){e.O(0,[649,19,347,554,222,319,62,11,734,136,696,971,117,744],function(){return e(e.s=49289)}),_N_E=e.O()}]);