import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export interface FilterState {
  category: string;
  petType: 'all' | 'breeding' | 'selling' | 'lost' | 'wanted';
  location: string;
  breed: string;
  sortBy: 'created_at' | 'likes_count' | 'wants_count' | 'avg_rating' | 'exposure_score';
  page: number;
  limit: number;
}

export interface UsePostFiltersReturn {
  filters: FilterState;
  updateFilter: (key: keyof FilterState, value: any) => void;
  resetFilters: () => void;
  resetPage: () => void;
  getQueryParams: () => URLSearchParams;
  hasActiveFilters: boolean;
}

const defaultFilters: FilterState = {
  category: '',
  petType: 'all',
  location: '',
  breed: '',
  sortBy: 'exposure_score',
  page: 1,
  limit: 20
};

export const usePostFilters = (): UsePostFiltersReturn => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // 从URL参数初始化筛选状态
  const initializeFiltersFromURL = useCallback((): FilterState => {
    return {
      category: searchParams.get('category') || defaultFilters.category,
      petType: (searchParams.get('petType') as FilterState['petType']) || defaultFilters.petType,
      location: searchParams.get('location') || defaultFilters.location,
      breed: searchParams.get('breed') || defaultFilters.breed,
      sortBy: (searchParams.get('sortBy') as FilterState['sortBy']) || defaultFilters.sortBy,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20')
    };
  }, [searchParams]);

  const [filters, setFilters] = useState<FilterState>(initializeFiltersFromURL);

  // 同步URL参数
  const updateURL = useCallback((newFilters: FilterState) => {
    const params = new URLSearchParams();
    
    // 只添加非默认值的参数
    if (newFilters.category && newFilters.category !== defaultFilters.category) {
      params.set('category', newFilters.category);
    }
    if (newFilters.petType !== defaultFilters.petType) {
      params.set('petType', newFilters.petType);
    }
    if (newFilters.location && newFilters.location !== defaultFilters.location) {
      params.set('location', newFilters.location);
    }
    if (newFilters.breed && newFilters.breed !== defaultFilters.breed) {
      params.set('breed', newFilters.breed);
    }
    if (newFilters.sortBy !== defaultFilters.sortBy) {
      params.set('sortBy', newFilters.sortBy);
    }
    if (newFilters.page !== defaultFilters.page) {
      params.set('page', newFilters.page.toString());
    }

    const newURL = params.toString() ? `?${params.toString()}` : '/';
    router.replace(newURL, { scroll: false });
  }, [router]);

  // 更新单个筛选条件
  const updateFilter = useCallback((key: keyof FilterState, value: any) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value,
        // 当筛选条件改变时重置页码（除非是页码本身在改变）
        page: key === 'page' ? value : 1
      };
      
      updateURL(newFilters);
      return newFilters;
    });
  }, [updateURL]);

  // 重置所有筛选条件
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
    updateURL(defaultFilters);
  }, [updateURL]);

  // 重置页码
  const resetPage = useCallback(() => {
    updateFilter('page', 1);
  }, [updateFilter]);

  // 获取查询参数对象
  const getQueryParams = useCallback((): URLSearchParams => {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== defaultFilters[key as keyof FilterState]) {
        params.set(key, value.toString());
      }
    });
    
    return params;
  }, [filters]);

  // 检查是否有活跃的筛选条件
  const hasActiveFilters = useCallback((): boolean => {
    return Object.entries(filters).some(([key, value]) => {
      if (key === 'page' || key === 'limit') return false;
      return value !== defaultFilters[key as keyof FilterState];
    });
  }, [filters]);

  // 监听URL变化，同步筛选状态
  useEffect(() => {
    const newFilters = initializeFiltersFromURL();
    setFilters(newFilters);
  }, [searchParams, initializeFiltersFromURL]);

  return {
    filters,
    updateFilter,
    resetFilters,
    resetPage,
    getQueryParams,
    hasActiveFilters: hasActiveFilters()
  };
};
